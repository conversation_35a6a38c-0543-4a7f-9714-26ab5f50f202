
import {
  <PERSON>sponsive<PERSON>ontainer,
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Cell,
  ReferenceLine,
} from "recharts";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

interface ProgressItem {
  name: string;
  value: number;
  target?: number;
  color?: string;
}

interface ProgressChartProps {
  data: ProgressItem[];
  title: string;
  description?: string;
  isLoading?: boolean;
  defaultColor?: string;
  valueFormat?: (value: number) => string;
  targetLabel?: string;
  horizontal?: boolean;
}

const ProgressChart = ({
  data,
  title,
  description,
  isLoading = false,
  defaultColor = "#8884d8",
  valueFormat = (value) => value.toString(),
  targetLabel = "Target",
  horizontal = false,
}: ProgressChartProps) => {
  // Custom tooltip for the chart
  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const item = payload[0].payload;
      return (
        <div className="bg-white p-3 border rounded-lg shadow-md">
          <p className="font-medium text-sm">{label}</p>
          <p className="text-sm text-muted-foreground">
            Current: {valueFormat(item.value)}
          </p>
          {item.target && (
            <p className="text-sm text-muted-foreground">
              {targetLabel}: {valueFormat(item.target)}
            </p>
          )}
        </div>
      );
    }
    return null;
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        {description && <CardDescription>{description}</CardDescription>}
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="w-full h-[300px] flex items-center justify-center">
            <Skeleton className="h-full w-full" />
          </div>
        ) : (
          <ResponsiveContainer width="100%" height={300}>
            <BarChart
              data={data}
              layout={horizontal ? "vertical" : "horizontal"}
              margin={{ top: 10, right: 30, left: 20, bottom: 10 }}
            >
              <CartesianGrid strokeDasharray="3 3" vertical={!horizontal} horizontal={horizontal} opacity={0.3} />
              {horizontal ? (
                <>
                  <XAxis type="number" axisLine={false} tickLine={false} />
                  <YAxis 
                    dataKey="name" 
                    type="category" 
                    axisLine={false} 
                    tickLine={false}
                    width={100}
                    tick={{ fontSize: 12 }}
                  />
                </>
              ) : (
                <>
                  <XAxis 
                    dataKey="name" 
                    axisLine={false} 
                    tickLine={false} 
                    tick={{ fontSize: 12 }}
                  />
                  <YAxis axisLine={false} tickLine={false} tick={{ fontSize: 12 }} />
                </>
              )}
              <Tooltip content={<CustomTooltip />} />
              <Bar 
                dataKey="value" 
                radius={[4, 4, 0, 0]}
                maxBarSize={horizontal ? 20 : 50}
              >
                {data.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color || defaultColor} />
                ))}
              </Bar>
              {data.some(item => item.target !== undefined) && (
                data.map((item, index) => (
                  item.target && (
                    <ReferenceLine
                      key={`ref-${index}`}
                      y={horizontal ? undefined : item.target}
                      x={horizontal ? item.target : undefined}
                      stroke="#ff7300"
                      strokeDasharray="3 3"
                      isFront={true}
                      ifOverflow="extendDomain"
                    />
                  )
                ))
              )}
            </BarChart>
          </ResponsiveContainer>
        )}
      </CardContent>
    </Card>
  );
};

export default ProgressChart;
