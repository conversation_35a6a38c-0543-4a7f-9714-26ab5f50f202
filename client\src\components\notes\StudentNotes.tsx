
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { PlusCircle, Search, Filter, Calendar, SortAsc, SortDesc } from "lucide-react";
import NoteDetailDialog from "./NoteDetailDialog";
import CreateNoteDialog from "./CreateNoteDialog";
import { getCurrentUser, hasPermission } from "@/lib/auth";
import { canCreateNotes } from "@/lib/noteUtils";

// Mock function - replace with real API call
const fetchStudentNotes = async (studentId: string) => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  return {
    student: {
      id: studentId,
      name: `Student ${studentId}`,
      email: `student${studentId}@example.com`,
      currentClasses: ["English 101", "Math 202"]
    },
    notes: [
      {
        id: "1",
        title: "Academic progress",
        content: "Student is showing excellent progress in written assignments but needs more practice with speaking.",
        createdBy: {
          id: "t1",
          name: "John Smith",
          role: "Teacher"
        },
        createdAt: new Date().toISOString(),
        type: "academic",
        visibility: "teachers",
        relatedClass: {
          id: "c1",
          name: "English 101"
        },
        attachments: []
      },
      {
        id: "2",
        title: "Payment reminder",
        content: "Outstanding balance for the current semester needs to be paid by end of month.",
        createdBy: {
          id: "s1",
          name: "Anna White",
          role: "Secretary"
        },
        createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
        type: "administrative",
        visibility: "all",
        attachments: []
      }
    ]
  };
};

interface StudentNotesProps {
  studentId: string;
}

const StudentNotes = ({ studentId }: StudentNotesProps) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedNoteId, setSelectedNoteId] = useState<string | null>(null);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  
  const user = getCurrentUser();
  
  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ["studentNotes", studentId],
    queryFn: () => fetchStudentNotes(studentId),
  });
  
  const studentInfo = data?.student;
  const notes = data?.notes || [];
  
  // Filter notes based on search query
  const filteredNotes = notes.filter(note => 
    note.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
    note.content.toLowerCase().includes(searchQuery.toLowerCase())
  );
  
  const handleNoteCreated = () => {
    setIsCreateDialogOpen(false);
    toast.success("Note created successfully");
    refetch();
  };
  
  // Find the currently selected note
  const selectedNote = selectedNoteId 
    ? notes.find(note => note.id === selectedNoteId) 
    : null;
  
  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="text-center text-red-500">
            Error loading student notes
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              {isLoading ? (
                <>
                  <Skeleton className="h-7 w-48 mb-2" />
                  <Skeleton className="h-5 w-32" />
                </>
              ) : (
                <>
                  <CardTitle>{studentInfo?.name || `Student ${studentId}`}</CardTitle>
                  <CardDescription>
                    {studentInfo?.email} • Classes: {studentInfo?.currentClasses.join(", ")}
                  </CardDescription>
                </>
              )}
            </div>
            {user && canCreateNotes(user.role) && (
              <Button onClick={() => setIsCreateDialogOpen(true)}>
                <PlusCircle className="mr-2 h-4 w-4" />
                Add Note
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex justify-between mb-4">
            <div className="relative w-full max-w-sm">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search notes..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Button variant="outline" size="icon">
              <Filter className="h-4 w-4" />
            </Button>
          </div>
          
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[300px]">Title</TableHead>
                  <TableHead>Created By</TableHead>
                  <TableHead>
                    <div className="flex items-center">
                      Date
                      <SortAsc className="ml-1 h-4 w-4" />
                    </div>
                  </TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Class</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {isLoading ? (
                  Array(3).fill(0).map((_, index) => (
                    <TableRow key={index}>
                      <TableCell><Skeleton className="h-5 w-40" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-32" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-24" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-20" /></TableCell>
                      <TableCell><Skeleton className="h-5 w-28" /></TableCell>
                    </TableRow>
                  ))
                ) : filteredNotes.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8 text-muted-foreground">
                      No notes found
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredNotes.map((note) => (
                    <TableRow 
                      key={note.id} 
                      className="cursor-pointer hover:bg-muted/50"
                      onClick={() => setSelectedNoteId(note.id)}
                    >
                      <TableCell className="font-medium">{note.title}</TableCell>
                      <TableCell>{note.createdBy.name}</TableCell>
                      <TableCell>
                        <div className="flex items-center">
                          <Calendar className="mr-2 h-4 w-4 text-muted-foreground" />
                          {new Date(note.createdAt).toLocaleDateString()}
                        </div>
                      </TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          note.type === 'academic' 
                            ? 'bg-green-100 text-green-800' 
                            : note.type === 'administrative'
                            ? 'bg-purple-100 text-purple-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {note.type.charAt(0).toUpperCase() + note.type.slice(1)}
                        </span>
                      </TableCell>
                      <TableCell>
                        {note.relatedClass?.name || "-"}
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>
      
      {selectedNote && (
        <NoteDetailDialog
          note={selectedNote}
          open={!!selectedNoteId}
          onOpenChange={(open) => {
            if (!open) setSelectedNoteId(null);
          }}
          onNoteUpdated={() => refetch()}
        />
      )}
      
      <CreateNoteDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onNoteCreated={handleNoteCreated}
        initialStudentId={studentId}
      />
    </div>
  );
};

export default StudentNotes;
