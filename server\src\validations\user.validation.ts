// server/src/validations/user.validation.ts
import <PERSON><PERSON> from 'joi';
import { UserRole, RoleHierarchy } from '../types/user.types';

export const userValidation = {
    // GET /api/users query parameters
    getUsersQuery: Joi.object({
        page: Joi.number().min(1),
        limit: Joi.number().min(1).max(100),
        sortBy: Joi.string().valid(
            'username',
            'role',
            'status',
            'createdAt',
            'lastLogin'
        ),
        sortOrder: Joi.string().valid('asc', 'desc'),
        status: Joi.string().valid('active', 'inactive'),
        role: Joi.string().valid(
            'superAdmin',
            'manager',
            'secretary',
            'teacher'
        ),
        search: Joi.string().min(1).max(50),
        fromDate: Joi.date().iso(),
        toDate: Joi.date().iso().min(Joi.ref('fromDate')),
        activityStatus: Joi.string().valid('online', 'offline', 'idle')
    }),

    // GET /api/users/:id/activities query parameters
    getUserActivitiesQuery: Joi.object({
        page: Joi.number().min(1),
        limit: Joi.number().min(1).max(100),
        fromDate: Joi.date().iso(),
        toDate: Joi.date().iso().min(Joi.ref('fromDate')),
        activityType: Joi.string().valid(
            'login',
            'logout',
            'status_change',
            'role_change',
            'password_change',
            'system_access'
        )
    }),

    // PATCH /api/users/:id/status
    updateStatus: Joi.object({
        status: Joi.string()
            .valid('active', 'inactive')
            .required()
            .messages({
                'any.only': 'Status must be either active or inactive',
                'any.required': 'Status is required'
            }),
        reason: Joi.string()
            .min(10)
            .max(500)
            .required()
            .messages({
                'string.min': 'Reason must be at least 10 characters long',
                'string.max': 'Reason cannot exceed 500 characters',
                'any.required': 'Reason for status change is required'
            })
    }),

    // PATCH /api/users/:id/role
    updateRole: Joi.object({
        role: Joi.string()
            .valid('manager', 'secretary', 'teacher', 'superAdmin')
            .required()
            .messages({
                'any.only': 'Invalid role specified',
                'any.required': 'Role is required'
            }),
        reason: Joi.string()
            .min(10)
            .max(500)
            .required()
            .messages({
                'string.min': 'Reason must be at least 10 characters long',
                'string.max': 'Reason cannot exceed 500 characters',
                'any.required': 'Reason for role change is required'
            })
    }),

    // POST /api/users/bulk/status
    bulkUpdateStatus: Joi.object({
        userIds: Joi.array()
            .items(Joi.string().regex(/^[0-9a-fA-F]{24}$/))
            .min(1)
            .max(50)
            .unique()
            .required()
            .messages({
                'array.min': 'At least one user ID must be provided',
                'array.max': 'Cannot update more than 50 users at once',
                'array.unique': 'Duplicate user IDs are not allowed',
                'string.pattern.base': 'Invalid user ID format',
                'any.required': 'User IDs are required'
            }),
        status: Joi.string()
            .valid('active', 'inactive')
            .required()
            .messages({
                'any.only': 'Status must be either active or inactive',
                'any.required': 'Status is required'
            }),
        reason: Joi.string()
            .min(10)
            .max(500)
            .required()
            .messages({
                'string.min': 'Reason must be at least 10 characters long',
                'string.max': 'Reason cannot exceed 500 characters',
                'any.required': 'Reason for bulk status change is required'
            })
    }),

    // POST /api/users/bulk/role
    bulkUpdateRole: Joi.object({
        userIds: Joi.array()
            .items(Joi.string().regex(/^[0-9a-fA-F]{24}$/))
            .min(1)
            .max(50)
            .unique()
            .required()
            .messages({
                'array.min': 'At least one user ID must be provided',
                'array.max': 'Cannot update more than 50 users at once',
                'array.unique': 'Duplicate user IDs are not allowed',
                'string.pattern.base': 'Invalid user ID format',
                'any.required': 'User IDs are required'
            }),
        role: Joi.string()
            .valid('manager', 'secretary', 'teacher')
            .required()
            .messages({
                'any.only': 'Invalid role specified',
                'any.required': 'Role is required'
            }),
        reason: Joi.string()
            .min(10)
            .max(500)
            .required()
            .messages({
                'string.min': 'Reason must be at least 10 characters long',
                'string.max': 'Reason cannot exceed 500 characters',
                'any.required': 'Reason for bulk role change is required'
            })
    }),

    // GET /api/users/validate/username query parameters
    validateUsername: Joi.object({
        username: Joi.string()
            .min(3)
            .max(30)
            .pattern(/^[a-zA-Z0-9_]+$/)
            .required()
            .messages({
                'string.min': 'Username must be at least 3 characters long',
                'string.max': 'Username cannot exceed 30 characters',
                'string.pattern.base': 'Username can only contain letters, numbers and underscores',
                'any.required': 'Username is required'
            })
    }),

    // GET /api/users/export query parameters
    exportUsers: Joi.object({
        format: Joi.string()
            .valid('csv', 'json')
            .default('csv')
            .messages({
                'any.only': 'Export format must be either csv or json'
            }),
        fields: Joi.string()
            .pattern(/^[a-zA-Z_]+(,[a-zA-Z_]+)*$/)
            .messages({
                'string.pattern.base': 'Invalid fields format. Use comma-separated field names'
            })
    }),

    // Common id parameter validation for routes using :id
    idParam: Joi.object({
        id: Joi.string()
            .regex(/^[0-9a-fA-F]{24}$/)
            .required()
            .messages({
                'string.pattern.base': 'Invalid ID format',
                'any.required': 'ID is required'
            })
    }),

    // Common validation for role hierarchy checks
    validateRoleHierarchy: (requestingRole: UserRole, targetRole: UserRole): boolean => {
        const roleHierarchy: RoleHierarchy = {
            superAdmin: ['manager', 'secretary', 'teacher', 'superAdmin'] as UserRole[],
            manager: ['secretary', 'teacher'] as UserRole[],
            secretary: [] as UserRole[],
            teacher: [] as UserRole[]
        };
    
        return roleHierarchy[requestingRole]?.includes(targetRole) || false;
    }
};

// Custom Joi extensions for additional validations
const JoiExtended = Joi.extend((joi) => ({
    type: 'roleHierarchy',
    base: joi.string(),
    messages: {
        'roleHierarchy.invalid': 'Unauthorized to manage this role level'
    },
    validate(value: string, helpers: any) {
        const { requestingRole } = helpers.state.ancestors[0];
        // Type assertion since we know these values will be UserRole
        if (!userValidation.validateRoleHierarchy(
            requestingRole as UserRole, 
            value as UserRole
        )) {
            return { value, errors: helpers.error('roleHierarchy.invalid') };
        }
        return { value };
    }
}));