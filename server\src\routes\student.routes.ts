// server/src/routes/student.routes.ts
import express from 'express';
import { StudentController } from '../controllers/student.controller';
import { authenticateToken, authorizeRoles } from '../middleware/auth.middleware';
import { validate } from '../middleware/validate.middleware';
import { studentValidation } from '../validations/student.validation';
import { catchAsync } from '../middleware/error.middleware';
import { apiLimiter } from '../middleware/rateLimiter.middleware';
import { versionControl } from '../middleware/version-control.middleware';

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Get students with role-based access
router.get(
    '/',
    authorizeRoles('superAdmin', 'manager', 'secretary', 'teacher'),
    validate(studentValidation.getStudentsQuery),
    catchAsync(StudentController.getStudents)
);

// Create new student
router.post(
    '/',
    authorizeRoles('superAdmin', 'manager', 'secretary'),
    validate(studentValidation.createStudent),
    catchAsync(StudentController.createStudent)
);

// Export students
router.get(
    '/export',
    authorizeRoles('superAdmin', 'manager'),
    validate(studentValidation.exportStudents),
    catchAsync(StudentController.exportStudents)
);

// Validate email
router.get(
    '/validate/email',
    apiLimiter,
    catchAsync(StudentController.validateStudentEmail)
);

// Bulk operations
router.post(
    '/bulk',
    authorizeRoles('superAdmin', 'manager'),
    validate(studentValidation.bulkOperation),
    catchAsync(StudentController.bulkOperation)
);

// Get level mismatches - MOVED UP
router.get(
    '/level-mismatches',
    authorizeRoles('superAdmin', 'manager'),
    catchAsync(StudentController.getLevelMismatches)
);

// Get specific student
router.get(
    '/:id',
    authorizeRoles('superAdmin', 'manager', 'secretary', 'teacher'),
    catchAsync(StudentController.getStudentById)
);

// Update student
router.patch(
    '/:id',
    authorizeRoles('superAdmin', 'manager', 'secretary'),
    validate(studentValidation.updateStudent),
    versionControl('Student'),
    catchAsync(StudentController.updateStudent)
);

// Record payment
router.post(
    '/:id/payments',
    authorizeRoles('superAdmin', 'manager', 'secretary'),
    validate(studentValidation.recordPayment),
    catchAsync(StudentController.recordPayment)
);

// Get payment history
router.get(
    '/:id/payments',
    authorizeRoles('superAdmin', 'manager', 'secretary'),
    catchAsync(StudentController.getStudentPaymentHistory)
);

// Transfer class
router.post(
    '/:id/transfer',
    authorizeRoles('superAdmin', 'manager'),
    validate(studentValidation.transferClass),
    catchAsync(StudentController.transferClass)
);

// Get class history
router.get(
    '/:id/classes',
    authorizeRoles('superAdmin', 'manager', 'secretary', 'teacher'),
    catchAsync(StudentController.getStudentClassHistory)
);

// Get suggested classes for a student
router.get(
    '/:id/suggested-classes',
    authorizeRoles('superAdmin', 'manager', 'secretary'),
    catchAsync(StudentController.getSuggestedClasses)
);

// Archive student
router.post(
    '/:id/archive',
    authorizeRoles('superAdmin', 'manager'),
    catchAsync(StudentController.archiveStudent)
);

// Restore student
router.post(
    '/:id/restore',
    authorizeRoles('superAdmin'),
    catchAsync(StudentController.restoreStudent)
);

export default router;