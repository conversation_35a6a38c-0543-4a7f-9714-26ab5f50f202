// server/src/models/user.model.ts
import mongoose, { Schema } from 'mongoose';
import { IUser } from '../types/auth.types';
import { UserRole } from '../types/user.types';

const userSchema = new Schema<IUser>({
    username: { 
        type: String, 
        required: true, 
        //unique: true,
        trim: true
    },
    password: { 
        type: String, 
        required: true 
    },
    role: { 
        type: String, 
        required: true,
        enum: ['superAdmin', 'manager', 'secretary', 'teacher']  // Direct values instead of Object.values(UserRole)
    },
    status: {
        type: String,
        required: true,
        enum: ['active', 'inactive'],
        default: 'active'
    },
    lastLogin: Date,
    createdAt: {
        type: Date,
        default: Date.now
    },
    modifiedAt: {
        type: Date,
        default: Date.now
    },
    createdBy: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true
    },
    roleHistory: [{
        role: String,
        changedAt: Date,
        changedBy: {
            type: Schema.Types.ObjectId,
            ref: 'User'
        },
        reason: String
    }],
    loginAttempts: [{
        timestamp: Date,
        ipAddress: String,
        success: Boolean
    }]
});

// Add indexes
userSchema.index({ username: 1 }, { unique: true });
userSchema.index({ role: 1 });
userSchema.index({ status: 1 });
userSchema.index({ 'roleHistory.role': 1, 'roleHistory.changedAt': 1 });

export const User = mongoose.model<IUser>('User', userSchema);