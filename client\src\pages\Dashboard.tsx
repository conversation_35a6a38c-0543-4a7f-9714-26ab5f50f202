
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { isAuthenticated, getCurrentUser } from "@/lib/auth";
import MainLayout from "@/components/layout/MainLayout";
import WelcomeBar from "@/components/dashboard/WelcomeBar";
import DateRangeSelector from "@/components/dashboard/DateRangeSelector";
import RoleBasedDashboard from "@/components/dashboard/RoleBasedDashboard";
import { UserRole } from "@/types";

const Dashboard = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [isAuthChecking, setIsAuthChecking] = useState(true);
  const [dateRange, setDateRange] = useState({
    from: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
    to: new Date()
  });
  const [userRole, setUserRole] = useState<UserRole>('guest');
  
  // Check authentication and get user role
  useEffect(() => {
    const checkAuth = async () => {
      try {
        setIsAuthChecking(true);
        const isAuth = await isAuthenticated();
        if (!isAuth) {
          navigate("/");
          return;
        }

        const user = await getCurrentUser();
        if (user) {
          setUserRole(user.role || 'guest');
        } else {
          setUserRole('guest');
        }
      } catch (error) {
        console.error("Error checking authentication:", error);
        // Don't redirect on error, just set to guest
        setUserRole('guest');
      } finally {
        setIsAuthChecking(false);
      }
    };

    checkAuth();
  }, [navigate]);

  // Show loading state while checking authentication
  if (isAuthChecking) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto py-6 px-4">
        {/* Welcome message and stats summary */}
        <WelcomeBar />

        {/* Date range selector */}
        <div className="mb-6 flex justify-end">
          <DateRangeSelector onRangeChange={setDateRange} />
        </div>

        {/* Role-based dashboard content */}
        <RoleBasedDashboard
          userRole={userRole}
          dateRange={dateRange}
          isLoading={isLoading}
          setIsLoading={setIsLoading}
        />
      </div>
    </MainLayout>
  );
};

export default Dashboard;
