
import React from 'react';
import { RoomUtilization } from '@/types/room';
import { Card, CardContent } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';

interface RoomUtilizationChartProps {
  utilization: RoomUtilization;
}

const RoomUtilizationChart: React.FC<RoomUtilizationChartProps> = ({ utilization }) => {
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <div className="flex justify-between">
          <span className="text-sm font-medium">Room Usage</span>
          <span className="text-sm font-medium">{utilization.usagePercentage}%</span>
        </div>
        <Progress value={utilization.usagePercentage} className="h-2" />
        <div className="flex justify-between text-xs text-muted-foreground">
          <span>Total Available Hours: {utilization.totalHours}h</span>
          <span>Used: {utilization.usedHours}h</span>
        </div>
      </div>

      <div>
        <h4 className="text-sm font-medium mb-3">Peak Usage Times</h4>
        <div className="space-y-3">
          {utilization.peakTimes.map((peak, index) => (
            <div key={index} className="space-y-1">
              <div className="flex justify-between text-sm">
                <span>{peak.day}, {peak.time}</span>
                <span>{peak.usagePercentage}%</span>
              </div>
              <Progress value={peak.usagePercentage} className="h-1.5" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default RoomUtilizationChart;
