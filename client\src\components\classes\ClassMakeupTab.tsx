
import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { format, parseISO } from "date-fns";
import { updateMakeupClassStatus, getClassScheduleWithMakeup } from "@/services/makeupClassService";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Calendar, 
  CheckCircle, 
  XCircle, 
  Clock, 
  Filter, 
  Plus, 
  MoreHorizontal
} from "lucide-react";
import { MakeupClass } from "@/types/class";
import { useToast } from "@/hooks/use-toast";
import MakeupClassFormDialog from "./MakeupClassFormDialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { hasRole } from "@/lib/auth";

interface ClassMakeupTabProps {
  classId: string;
  makeupClasses: MakeupClass[];
}

const ClassMakeupTab = ({ classId, makeupClasses: initialMakeupClasses }: ClassMakeupTabProps) => {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const canManageClasses = hasRole(["SuperAdmin", "Manager", "Teacher"]);
  
  // State for dialogs
  const [isMakeupFormOpen, setIsMakeupFormOpen] = useState(false);
  const [isStatusDialogOpen, setIsStatusDialogOpen] = useState(false);
  const [selectedMakeupId, setSelectedMakeupId] = useState<string | null>(null);
  const [statusAction, setStatusAction] = useState<'completed' | 'cancelled' | null>(null);

  // State for filtering
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState("list");
  
  // Fetch makeup classes
  const { data: scheduleData, isLoading } = useQuery({
    queryKey: ["class-schedule-makeup", classId],
    queryFn: () => getClassScheduleWithMakeup(classId),
    initialData: { 
      regularSchedule: [],
      makeupClasses: initialMakeupClasses 
    },
  });

  // Update makeup class status mutation
  const updateStatusMutation = useMutation({
    mutationFn: ({ makeupId, status }: { makeupId: string, status: 'completed' | 'cancelled' }) => 
      updateMakeupClassStatus(classId, makeupId, status),
    onSuccess: (data) => {
      toast({
        title: "Success",
        description: data.message,
      });
      
      // Invalidate the schedule query to refetch data
      queryClient.invalidateQueries({ queryKey: ["class-schedule-makeup", classId] });
      
      // Close the dialog
      setIsStatusDialogOpen(false);
      setSelectedMakeupId(null);
      setStatusAction(null);
    },
    onError: (error) => {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to update makeup class status.",
      });
    },
  });
  
  // Function to get badge color based on status
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'scheduled':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800 hover:bg-blue-100">Scheduled</Badge>;
      case 'completed':
        return <Badge variant="outline" className="bg-green-100 text-green-800 hover:bg-green-100">Completed</Badge>;
      case 'cancelled':
        return <Badge variant="outline" className="bg-red-100 text-red-800 hover:bg-red-100">Cancelled</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };
  
  // Function to handle status update
  const handleStatusUpdate = (makeupId: string, action: 'completed' | 'cancelled') => {
    setSelectedMakeupId(makeupId);
    setStatusAction(action);
    setIsStatusDialogOpen(true);
  };
  
  // Function to confirm status update
  const confirmStatusUpdate = () => {
    if (selectedMakeupId && statusAction) {
      updateStatusMutation.mutate({
        makeupId: selectedMakeupId,
        status: statusAction
      });
    }
  };
  
  // Filter makeup classes
  const filteredMakeupClasses = scheduleData.makeupClasses.filter(makeup => {
    // Apply status filter
    if (statusFilter !== "all" && makeup.status !== statusFilter) {
      return false;
    }
    
    // Apply search filter
    if (searchQuery.trim() !== "") {
      const searchLower = searchQuery.toLowerCase();
      return (
        makeup.reason.toLowerCase().includes(searchLower) ||
        (makeup.teacherName && makeup.teacherName.toLowerCase().includes(searchLower)) ||
        (makeup.room && makeup.room.toLowerCase().includes(searchLower))
      );
    }
    
    return true;
  });
  
  // Sort makeup classes by date (most recent first)
  const sortedMakeupClasses = [...filteredMakeupClasses].sort((a, b) => {
    return new Date(b.makeupDate).getTime() - new Date(a.makeupDate).getTime();
  });
  
  // Function to handle query success
  const handleQuerySuccess = () => {
    queryClient.invalidateQueries({ queryKey: ["class-schedule-makeup", classId] });
  };
  
  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="pb-3">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
            <div>
              <CardTitle>Makeup Classes</CardTitle>
              <CardDescription>
                Schedule adjustments and makeup sessions for this class
              </CardDescription>
            </div>
            {canManageClasses && (
              <Button 
                onClick={() => setIsMakeupFormOpen(true)}
                className="mt-2 md:mt-0"
              >
                <Plus className="mr-2 h-4 w-4" />
                Schedule Makeup Class
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="list" value={activeTab} onValueChange={setActiveTab}>
            <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between mb-4 gap-2">
              <TabsList>
                <TabsTrigger value="list">
                  <Filter className="mr-2 h-4 w-4" />
                  List View
                </TabsTrigger>
                <TabsTrigger value="calendar">
                  <Calendar className="mr-2 h-4 w-4" />
                  Calendar View
                </TabsTrigger>
              </TabsList>
              
              <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
                <Input
                  placeholder="Search makeup classes..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full sm:w-[200px]"
                />
                <Select 
                  value={statusFilter} 
                  onValueChange={setStatusFilter}
                >
                  <SelectTrigger className="w-full sm:w-[150px]">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Statuses</SelectItem>
                    <SelectItem value="scheduled">Scheduled</SelectItem>
                    <SelectItem value="completed">Completed</SelectItem>
                    <SelectItem value="cancelled">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <TabsContent value="list" className="m-0">
              {sortedMakeupClasses.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  {searchQuery || statusFilter !== "all" ? 
                    "No makeup classes match your filters." : 
                    "No makeup classes scheduled for this class."}
                </div>
              ) : (
                <div className="border rounded-md">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Original Date</TableHead>
                        <TableHead>Makeup Date</TableHead>
                        <TableHead>Time</TableHead>
                        <TableHead>Room</TableHead>
                        <TableHead>Teacher</TableHead>
                        <TableHead>Reason</TableHead>
                        <TableHead>Status</TableHead>
                        {canManageClasses && <TableHead>Actions</TableHead>}
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {sortedMakeupClasses.map((makeupClass) => (
                        <TableRow key={makeupClass.id || `${makeupClass.originalDate}-${makeupClass.makeupDate}`}>
                          <TableCell>{format(new Date(makeupClass.originalDate), "MMM d, yyyy")}</TableCell>
                          <TableCell>{format(new Date(makeupClass.makeupDate), "MMM d, yyyy")}</TableCell>
                          <TableCell>
                            {makeupClass.timeStart && makeupClass.timeEnd ? 
                              `${makeupClass.timeStart} - ${makeupClass.timeEnd}` : 
                              "N/A"}
                          </TableCell>
                          <TableCell>{makeupClass.room || "N/A"}</TableCell>
                          <TableCell>{makeupClass.teacherName || "N/A"}</TableCell>
                          <TableCell className="max-w-[200px] truncate" title={makeupClass.reason}>
                            {makeupClass.reason}
                          </TableCell>
                          <TableCell>{getStatusBadge(makeupClass.status)}</TableCell>
                          {canManageClasses && (
                            <TableCell>
                              {makeupClass.status === 'scheduled' && (
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button variant="ghost" size="icon">
                                      <MoreHorizontal className="h-4 w-4" />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuItem
                                      onClick={() => handleStatusUpdate(makeupClass.id!, 'completed')}
                                    >
                                      <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                                      Mark as Completed
                                    </DropdownMenuItem>
                                    <DropdownMenuItem
                                      onClick={() => handleStatusUpdate(makeupClass.id!, 'cancelled')}
                                      className="text-destructive"
                                    >
                                      <XCircle className="mr-2 h-4 w-4" />
                                      Cancel Makeup Class
                                    </DropdownMenuItem>
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              )}
                            </TableCell>
                          )}
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="calendar" className="m-0">
              <div className="text-center py-12 text-muted-foreground">
                <Calendar className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                <h3 className="text-lg font-medium">Calendar View Coming Soon</h3>
                <p className="max-w-md mx-auto mt-2">
                  We're working on a calendar view that will show regular classes
                  and makeup classes together for better schedule visualization.
                </p>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
      
      {/* Schedule Makeup Class Dialog */}
      {isMakeupFormOpen && (
        <MakeupClassFormDialog
          isOpen={isMakeupFormOpen}
          onClose={() => setIsMakeupFormOpen(false)}
          classData={{ id: classId, teachers: [], ...scheduleData } as any}
          onSuccess={handleQuerySuccess}
        />
      )}
      
      {/* Status Update Confirmation Dialog */}
      <AlertDialog 
        open={isStatusDialogOpen} 
        onOpenChange={setIsStatusDialogOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              {statusAction === 'completed' ? 'Mark Makeup Class as Completed?' : 'Cancel Makeup Class?'}
            </AlertDialogTitle>
            <AlertDialogDescription>
              {statusAction === 'completed' 
                ? 'This will mark the makeup class as completed. This action cannot be undone.'
                : 'This will cancel the scheduled makeup class. This action cannot be undone.'}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={confirmStatusUpdate}
              className={statusAction === 'cancelled' ? 'bg-destructive text-destructive-foreground hover:bg-destructive/90' : ''}
            >
              {statusAction === 'completed' ? 'Mark as Completed' : 'Cancel Makeup Class'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default ClassMakeupTab;
