
import { useState } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { scheduleRoomMaintenance } from "@/services/roomService";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import { Calendar as CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { checkRoomAvailability } from "@/services/roomService";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  <PERSON><PERSON><PERSON><PERSON>og<PERSON>oot<PERSON>,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface MaintenanceScheduleFormProps {
  roomId: string;
  onScheduled?: () => void;
}

const maintenanceSchema = z.object({
  startDate: z.date({
    required_error: "A start date is required",
  }),
  endDate: z.date({
    required_error: "An end date is required",
  }).refine(
    (endDate) => endDate >= new Date(),
    { message: "End date cannot be in the past" }
  ),
  reason: z.string().min(10, { message: "Reason must be at least 10 characters" }),
});

type MaintenanceFormValues = z.infer<typeof maintenanceSchema>;

const MaintenanceScheduleForm: React.FC<MaintenanceScheduleFormProps> = ({ 
  roomId, 
  onScheduled 
}) => {
  const [loading, setLoading] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [conflicts, setConflicts] = useState<any[]>([]);
  
  const form = useForm<MaintenanceFormValues>({
    resolver: zodResolver(maintenanceSchema),
    defaultValues: {
      startDate: new Date(),
      endDate: new Date(new Date().setDate(new Date().getDate() + 1)),
      reason: "",
    },
  });
  
  const handleCheckAvailability = async (values: MaintenanceFormValues) => {
    setLoading(true);
    try {
      // Format dates for API call
      const formattedStartDate = format(values.startDate, 'yyyy-MM-dd');
      const formattedEndDate = format(values.endDate, 'yyyy-MM-dd');
      
      // Check availability for each day in the range
      const startTime = "00:00"; // Whole day
      const endTime = "23:59"; // Whole day
      
      const response = await checkRoomAvailability(roomId, formattedStartDate, endTime, startTime);
      
      if (!response.available && response.conflictingEvents && response.conflictingEvents.length > 0) {
        setConflicts(response.conflictingEvents);
        setShowConfirmDialog(true);
      } else {
        // No conflicts, proceed with scheduling
        await scheduleMaintenancePeriod(values);
      }
    } catch (error) {
      console.error("Error checking availability:", error);
      toast.error("Failed to check room availability");
    } finally {
      setLoading(false);
    }
  };
  
  const scheduleMaintenancePeriod = async (values: MaintenanceFormValues) => {
    setLoading(true);
    try {
      // Format dates for API call
      const formattedStartDate = format(values.startDate, 'yyyy-MM-dd');
      const formattedEndDate = format(values.endDate, 'yyyy-MM-dd');
      
      await scheduleRoomMaintenance(
        roomId,
        formattedStartDate,
        formattedEndDate,
        values.reason
      );
      
      toast.success("Maintenance scheduled successfully");
      form.reset();
      
      if (onScheduled) {
        onScheduled();
      }
    } catch (error) {
      console.error("Error scheduling maintenance:", error);
      toast.error("Failed to schedule maintenance");
    } finally {
      setLoading(false);
    }
  };
  
  const onConfirmScheduleWithConflicts = () => {
    const values = form.getValues();
    scheduleMaintenancePeriod(values);
    setShowConfirmDialog(false);
  };
  
  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleCheckAvailability)} className="space-y-6">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <FormField
              control={form.control}
              name="startDate"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Start Date</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value ? (
                            format(field.value, "PPP")
                          ) : (
                            <span>Select start date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) => date < new Date(new Date().setHours(0, 0, 0, 0))}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormDescription>
                    The first day of maintenance.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="endDate"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>End Date</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          className={cn(
                            "w-full pl-3 text-left font-normal",
                            !field.value && "text-muted-foreground"
                          )}
                        >
                          {field.value ? (
                            format(field.value, "PPP")
                          ) : (
                            <span>Select end date</span>
                          )}
                          <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) => {
                          const startDate = form.getValues().startDate;
                          return date < (startDate || new Date(new Date().setHours(0, 0, 0, 0)));
                        }}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  <FormDescription>
                    The last day of maintenance.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
          
          <FormField
            control={form.control}
            name="reason"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Reason for Maintenance</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Provide details about the maintenance purpose"
                    className="resize-none min-h-[120px]"
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  This information will be visible to staff and may be shared with students if classes are affected.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
          
          <Button type="submit" className="w-full" disabled={loading}>
            {loading ? "Checking Availability..." : "Schedule Maintenance"}
          </Button>
        </form>
      </Form>
      
      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Scheduling Conflicts Detected</AlertDialogTitle>
            <AlertDialogDescription>
              There are {conflicts.length} scheduled events during the selected maintenance period:
              <ul className="mt-2 space-y-1 list-disc list-inside">
                {conflicts.map((conflict, index) => (
                  <li key={index} className="text-sm">
                    {conflict.name} ({conflict.type === 'class' ? 'Class' : 'Maintenance'}) on{' '}
                    {format(new Date(conflict.start), 'PP')} at{' '}
                    {format(new Date(conflict.start), 'h:mm a')}
                  </li>
                ))}
              </ul>
              <p className="mt-4">
                Do you want to proceed with scheduling this maintenance? If you proceed, you'll need to reschedule the affected classes.
              </p>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={onConfirmScheduleWithConflicts}>
              Schedule Anyway
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default MaintenanceScheduleForm;
