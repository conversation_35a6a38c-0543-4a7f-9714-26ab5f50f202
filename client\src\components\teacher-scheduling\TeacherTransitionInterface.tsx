import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardT<PERSON>le, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { Switch } from "@/components/ui/switch";
import { TeacherScheduleItem } from "@/types/class";
import { TeacherScheduleData, TeacherTransition } from "@/types/teacherScheduling";
import { capitalizeFirstLetter } from "@/lib/utils";
import { format } from "date-fns";
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon, Check, AlertCircle } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Pop<PERSON>,
  <PERSON>overContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";

interface TeacherTransitionInterfaceProps {
  classId: string;
  teachers: TeacherScheduleData[];
  availableTeachers: TeacherScheduleData[];
  onSave: (transitionData: TeacherTransition) => void;
  onCancel: () => void;
}

const TeacherTransitionInterface = ({
  classId,
  teachers,
  availableTeachers,
  onSave,
  onCancel,
}: TeacherTransitionInterfaceProps) => {
  const [oldTeacherId, setOldTeacherId] = useState("");
  const [newTeacherId, setNewTeacherId] = useState("");
  const [transitionStartDate, setTransitionStartDate] = useState<Date | undefined>(new Date());
  const [transitionEndDate, setTransitionEndDate] = useState<Date | undefined>(
    new Date(new Date().setDate(new Date().getDate() + 14)) // Default to 2 weeks later
  );
  const [notes, setNotes] = useState("");
  const [notifyStudents, setNotifyStudents] = useState(true);
  const [materialTransferNotes, setMaterialTransferNotes] = useState("");
  const [selectedScheduleSlots, setSelectedScheduleSlots] = useState<Map<string, boolean>>(new Map());
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  
  useEffect(() => {
    if (oldTeacherId) {
      const teacher = teachers.find(t => t.teacherId === oldTeacherId);
      if (teacher) {
        const newScheduleSlots = new Map<string, boolean>();
        teacher.schedule.forEach((slot, index) => {
          const key = `${slot.day}-${slot.timeStart}-${slot.timeEnd}`;
          newScheduleSlots.set(key, false);
        });
        setSelectedScheduleSlots(newScheduleSlots);
      }
    }
  }, [oldTeacherId, teachers]);
  
  const toggleScheduleSlot = (slot: TeacherScheduleItem) => {
    const key = `${slot.day}-${slot.timeStart}-${slot.timeEnd}`;
    const newSelectedScheduleSlots = new Map(selectedScheduleSlots);
    newSelectedScheduleSlots.set(key, !newSelectedScheduleSlots.get(key));
    setSelectedScheduleSlots(newSelectedScheduleSlots);
  };
  
  const validateForm = (): boolean => {
    const errors: string[] = [];
    
    if (!oldTeacherId) {
      errors.push("Original teacher is required");
    }
    
    if (!newTeacherId) {
      errors.push("New teacher is required");
    }
    
    if (!transitionStartDate) {
      errors.push("Transition start date is required");
    }
    
    if (!transitionEndDate) {
      errors.push("Transition end date is required");
    }
    
    if (transitionStartDate && transitionEndDate && transitionStartDate >= transitionEndDate) {
      errors.push("Transition end date must be after start date");
    }
    
    const hasSelectedSlots = Array.from(selectedScheduleSlots.values()).some(selected => selected);
    if (!hasSelectedSlots) {
      errors.push("At least one schedule slot must be selected");
    }
    
    setValidationErrors(errors);
    return errors.length === 0;
  };
  
  const handleSubmit = () => {
    if (validateForm()) {
      const teacher = teachers.find(t => t.teacherId === oldTeacherId);
      if (!teacher) return;
      
      const schedulesToTransfer: TeacherScheduleItem[] = teacher.schedule.filter(slot => {
        const key = `${slot.day}-${slot.timeStart}-${slot.timeEnd}`;
        return selectedScheduleSlots.get(key);
      });
      
      const transitionData: TeacherTransition = {
        originalTeacherId: oldTeacherId,
        newTeacherId,
        transitionStartDate: transitionStartDate ? format(transitionStartDate, 'yyyy-MM-dd') : '',
        transitionEndDate: transitionEndDate ? format(transitionEndDate, 'yyyy-MM-dd') : '',
        reason: notes || "Teacher transition",
        schedulesToTransfer,
        notes,
        notifyStudents,
        materialTransfer: {
          notes: materialTransferNotes
        }
      };
      
      onSave(transitionData);
    }
  };
  
  const getAvailableTransitions = () => {
    return availableTeachers.filter(t => t.teacherId !== oldTeacherId);
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Teacher Transition</CardTitle>
        <CardDescription>
          Plan a gradual transition between teachers for selected schedule slots
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div>
          <Label htmlFor="oldTeacher">Current Teacher</Label>
          <Select value={oldTeacherId} onValueChange={setOldTeacherId}>
            <SelectTrigger id="oldTeacher">
              <SelectValue placeholder="Select current teacher" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                {teachers.map((teacher) => (
                  <SelectItem key={teacher.teacherId} value={teacher.teacherId}>
                    {teacher.name}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <Label htmlFor="newTeacher">New Teacher</Label>
          <Select 
            value={newTeacherId} 
            onValueChange={setNewTeacherId}
            disabled={!oldTeacherId}
          >
            <SelectTrigger id="newTeacher">
              <SelectValue placeholder="Select new teacher" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                {getAvailableTransitions().map((teacher) => (
                  <SelectItem key={teacher.teacherId} value={teacher.teacherId}>
                    {teacher.name}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
        
        <div className="grid grid-cols-2 gap-4">
          <div className="grid gap-2">
            <Label htmlFor="transitionStartDate">Transition Start Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  id="transitionStartDate"
                  variant={"outline"}
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !transitionStartDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {transitionStartDate ? format(transitionStartDate, "PPP") : <span>Pick a date</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={transitionStartDate}
                  onSelect={setTransitionStartDate}
                  initialFocus
                  className="pointer-events-auto"
                />
              </PopoverContent>
            </Popover>
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="transitionEndDate">Transition End Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  id="transitionEndDate"
                  variant={"outline"}
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !transitionEndDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {transitionEndDate ? format(transitionEndDate, "PPP") : <span>Pick a date</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={transitionEndDate}
                  onSelect={setTransitionEndDate}
                  initialFocus
                  className="pointer-events-auto"
                  disabled={(date) => 
                    transitionStartDate ? date < transitionStartDate : false
                  }
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>
        
        <div>
          <Label className="mb-2 block">Schedule Slots to Transfer</Label>
          {oldTeacherId ? (
            <div className="space-y-2 max-h-[150px] overflow-y-auto pr-2">
              {teachers
                .find(t => t.teacherId === oldTeacherId)
                ?.schedule.map((slot, index) => {
                  const key = `${slot.day}-${slot.timeStart}-${slot.timeEnd}`;
                  return (
                    <div
                      key={index}
                      className="flex items-center space-x-2 p-2 rounded-md border hover:bg-accent"
                    >
                      <Checkbox
                        id={`slot-${index}`}
                        checked={selectedScheduleSlots.get(key) || false}
                        onCheckedChange={() => toggleScheduleSlot(slot)}
                      />
                      <Label
                        htmlFor={`slot-${index}`}
                        className="flex-1 cursor-pointer"
                      >
                        <span className="font-medium">{capitalizeFirstLetter(slot.day)}</span>:{' '}
                        {slot.timeStart} - {slot.timeEnd}
                      </Label>
                    </div>
                  );
                })}
            </div>
          ) : (
            <div className="text-muted-foreground text-sm py-2">
              Select a current teacher to see their schedule slots
            </div>
          )}
        </div>
        
        <div>
          <Label htmlFor="notes">Transition Notes</Label>
          <Textarea
            id="notes"
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            placeholder="Enter notes about this teacher transition"
            className="min-h-[80px]"
          />
        </div>
        
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label htmlFor="notifyStudents">Notify Students</Label>
            <div className="text-sm text-muted-foreground">
              Send an automatic notification to students about this teacher change
            </div>
          </div>
          <Switch
            id="notifyStudents"
            checked={notifyStudents}
            onCheckedChange={setNotifyStudents}
          />
        </div>
        
        <div>
          <Label htmlFor="materialTransferNotes">Material Transfer Notes</Label>
          <Textarea
            id="materialTransferNotes"
            value={materialTransferNotes}
            onChange={(e) => setMaterialTransferNotes(e.target.value)}
            placeholder="Enter notes about materials that need to be transferred between teachers"
            className="min-h-[80px]"
          />
        </div>
        
        {validationErrors.length > 0 && (
          <div className="p-3 rounded-md bg-destructive/10 text-destructive space-y-1">
            {validationErrors.map((error, index) => (
              <div key={index} className="flex items-center">
                <AlertCircle className="h-4 w-4 mr-2" />
                <span>{error}</span>
              </div>
            ))}
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={handleSubmit}>
          <Check className="mr-2 h-4 w-4" />
          Start Transition
        </Button>
      </CardFooter>
    </Card>
  );
};

export default TeacherTransitionInterface;
