// server/src/types/secretary.report.types.ts
import { Types } from 'mongoose';

export type SecretaryReportFormat = 'pdf' | 'excel' | 'csv' | 'json';

export type SecretaryReportType = 
    | 'student_registration'
    | 'payment_collection'
    | 'class_capacity'
    | 'student_attendance'
    | 'payment_due';

// Request structure
export interface ReportDateRange {
    startDate: Date;
    endDate: Date;
}

export interface PaymentStatus {
    status: 'paid' | 'pending' | 'overdue';
    dueDate?: Date;
}

export interface SecretaryReportRequestDTO {
    type: SecretaryReportType;
    format: SecretaryReportFormat;
    dateRange: ReportDateRange;
    filters?: {
        classId?: Types.ObjectId;
        paymentStatus?: PaymentStatus['status'];
        registrationStatus?: 'active' | 'inactive';
        minCapacity?: number;
        maxCapacity?: number;
        attendanceRate?: number;
        building?: string;
        floor?: number;
    };
    groupBy?: 'class' | 'date' | 'status' | 'building';
    sortBy?: 'date' | 'amount' | 'name' | 'dueDate';
    sortOrder?: 'asc' | 'desc';
}

// Report Data Structures
export interface RegistrationReportData {
    period: ReportDateRange;
    totalRegistrations: number;
    registrationsByDate: Array<{
        date: Date;
        count: number;
        details: Array<{
            studentName: string;
            className: string;
            registeredBy: string;
        }>;
    }>;
    summary: {
        activeStudents: number;
        inactiveStudents: number;
        registrationTrend: 'increasing' | 'decreasing' | 'stable';
    };
}

export interface PaymentCollectionReportData {
    period: ReportDateRange;
    totalCollected: number;
    paymentsByDate: Array<{
        date: Date;
        amount: number;
        payments: Array<{
            studentName: string;
            amount: number;
            method: string;
            recordedBy: string;
        }>;
    }>;
    summary: {
        totalPending: number;
        totalOverdue: number;
        collectionRate: number;
    };
}

export interface ClassCapacityReportData {
    period: ReportDateRange;
    classes: Array<{
        className: string;
        room: string;
        building: string;
        capacity: number;
        currentCount: number;
        utilizationRate: number;
        waitingList?: number;
    }>;
    summary: {
        totalCapacity: number;
        totalEnrolled: number;
        averageUtilization: number;
        fullClasses: number;
    };
}

export interface AttendanceReportData {
    period: ReportDateRange;
    attendanceByClass: Array<{
        className: string;
        totalStudents: number;
        records: Array<{
            date: Date;
            present: number;
            absent: number;
            late: number;
            excused: number;
            rate: number;
        }>;
    }>;
    summary: {
        averageAttendanceRate: number;
        lowestAttendanceClass: string;
        highestAttendanceClass: string;
    };
}

export interface PaymentDueReportData {
    period: ReportDateRange;
    duePayments: Array<{
        studentName: string;
        className: string;
        amount: number;
        dueDate: Date;
        status: PaymentStatus['status'];
        contactInfo: {
            phone: string;
            email: string;
        };
        lastPayment?: {
            date: Date;
            amount: number;
        };
    }>;
    summary: {
        totalDueAmount: number;
        overdueCount: number;
        upcomingDueCount: number;
    };
}

// Template sections for different report types
export const reportSections: Record<SecretaryReportType, string[]> = {
    student_registration: ['summary', 'registrationsByDate', 'details'],
    payment_collection: ['summary', 'paymentsByDate', 'details'],
    class_capacity: ['summary', 'classDetails', 'utilization'],
    student_attendance: ['summary', 'attendanceByClass', 'details'],
    payment_due: ['summary', 'duePayments', 'reminders']
};