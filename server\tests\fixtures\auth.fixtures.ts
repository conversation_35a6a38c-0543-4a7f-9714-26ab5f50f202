// server/src/tests/fixtures/auth.fixtures.ts
import { User } from '../../src/models/user.model';
import { AuthService } from '../../src/services/auth.service';

export const createTestUser = async (
  username: string,
  password: string,
  role: string,
  createdById: string
) => {
  const hashedPassword = await AuthService.hashPassword(password);
  const user = new User({
    username,
    password: hashedPassword,
    role,
    status: 'active',
    createdBy: createdById,
    roleHistory: [{
      role,
      changedAt: new Date(),
      changedBy: createdById,
      reason: 'Test user creation'
    }]
  });
  await user.save();
  return user;
};