import mongoose, { Schema, Document, Types, Model, CallbackError } from 'mongoose';
import { AppError } from '../types/error.types';
import { IRoom, RoomStatus, TimeSlot } from '../types/room.types';

interface AvailabilitySlot {
    date: Date;
    timeSlots: Array<TimeSlot & { classId: Types.ObjectId }>;
}

interface MaintenanceScheduleItem {
    startDate: Date;
    endDate: Date;
    reason: string;
    scheduledBy: Types.ObjectId;
}

// Interface for the document (instance methods)
export interface IRoomDocument extends Document, Omit<IRoom, '_id'> {
    _id: Types.ObjectId;
    availability: AvailabilitySlot[];
    maintenanceSchedule: MaintenanceScheduleItem[];
    isAvailable(date: Date, timeSlot: TimeSlot): Promise<boolean>;
    addTimeSlot(date: Date, timeSlot: TimeSlot, classId: Types.ObjectId): Promise<void>;
    removeTimeSlot(date: Date, classId: Types.ObjectId): Promise<void>;
    validateCapacity(requestedCapacity: number): boolean;
    checkScheduleConflict(timeSlot: TimeSlot): Promise<boolean>;
    convertTimeToMinutes(time: string): number;
}

// Interface for the model (static methods)
interface IRoomModel extends Model<IRoomDocument> {
    findAvailableRooms(date: Date, timeSlot: TimeSlot, requiredCapacity?: number): Promise<IRoomDocument[]>;
    validateRoomAvailability(roomId: Types.ObjectId, date: Date, timeSlot: TimeSlot): Promise<boolean>;
}

const roomSchema = new Schema<IRoomDocument>({
    name: {
        type: String,
        required: true,
        unique: true,
        trim: true
    },
    capacity: {
        type: Number,
        required: true,
        min: [1, 'Room capacity must be at least 1']
    },
    building: {
        type: String,
        required: true,
        trim: true
    },
    floor: {
        type: Number,
        required: true
    },
    features: [{
        type: String,
        trim: true
    }],
    status: {
        type: String,
        enum: ['active', 'maintenance', 'inactive'],
        default: 'active',
        required: true
    },
    maintenanceSchedule: [{
        startDate: {
            type: Date,
            required: true
        },
        endDate: {
            type: Date,
            required: true
        },
        reason: {
            type: String,
            required: true
        },
        scheduledBy: {
            type: Schema.Types.ObjectId,
            ref: 'User',
            required: true
        }
    }],
    availability: [{
        date: {
            type: Date,
            required: true
        },
        timeSlots: [{
            start: {
                type: String,
                required: true,
                validate: {
                    validator: (v: string) => /^([01]\d|2[0-3]):([0-5]\d)$/.test(v),
                    message: 'Start time must be in HH:mm format'
                }
            },
            end: {
                type: String,
                required: true,
                validate: {
                    validator: (v: string) => /^([01]\d|2[0-3]):([0-5]\d)$/.test(v),
                    message: 'End time must be in HH:mm format'
                }
            },
            classId: {
                type: Schema.Types.ObjectId,
                ref: 'Class',
                required: true
            }
        }]
    }],
    lastModifiedAt: {
        type: Date,
        default: Date.now
    },
    lastModifiedBy: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true
    }
}, {
    timestamps: true
});

// Indexes
roomSchema.index({ name: 1 }, { unique: true });
roomSchema.index({ status: 1 });
roomSchema.index({ building: 1, floor: 1 });
roomSchema.index({ 'availability.date': 1 });
roomSchema.index({ 'maintenanceSchedule.startDate': 1, 'maintenanceSchedule.endDate': 1 });

// Instance methods
roomSchema.methods.convertTimeToMinutes = function(time: string): number {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
};

roomSchema.methods.isAvailable = async function(
    this: IRoomDocument,
    date: Date,
    timeSlot: TimeSlot
): Promise<boolean> {
    // Check if room is active
    if (this.status !== 'active') {
        return false;
    }

    // Check maintenance schedule
    const isUnderMaintenance = this.maintenanceSchedule.some((schedule: MaintenanceScheduleItem) => 
        date >= schedule.startDate && date <= schedule.endDate
    );

    if (isUnderMaintenance) {
        return false;
    }

    // Convert times to comparable format
    const requestedStart = this.convertTimeToMinutes(timeSlot.start);
    const requestedEnd = this.convertTimeToMinutes(timeSlot.end);

    // Get day's schedule
    const daySchedule = this.availability.find((a: AvailabilitySlot) => 
        a.date.toDateString() === date.toDateString()
    );

    if (!daySchedule) {
        return true;
    }

    // Check for conflicts
    return !daySchedule.timeSlots.some((slot: TimeSlot & { classId: Types.ObjectId }) => {
        const slotStart = this.convertTimeToMinutes(slot.start);
        const slotEnd = this.convertTimeToMinutes(slot.end);

        return (requestedStart < slotEnd && requestedEnd > slotStart);
    });
};

roomSchema.methods.addTimeSlot = async function(
    this: IRoomDocument,
    date: Date,
    timeSlot: TimeSlot,
    classId: Types.ObjectId
): Promise<void> {
    const daySchedule = this.availability.find((a: AvailabilitySlot) => 
        a.date.toDateString() === date.toDateString()
    );

    if (daySchedule) {
        daySchedule.timeSlots.push({ ...timeSlot, classId });
    } else {
        this.availability.push({
            date,
            timeSlots: [{ ...timeSlot, classId }]
        });
    }

    await this.save();
};

roomSchema.methods.removeTimeSlot = async function(
    this: IRoomDocument,
    date: Date,
    classId: Types.ObjectId
): Promise<void> {
    const daySchedule = this.availability.find((a: AvailabilitySlot) => 
        a.date.toDateString() === date.toDateString()
    );

    if (daySchedule) {
        daySchedule.timeSlots = daySchedule.timeSlots.filter((slot: TimeSlot & { classId: Types.ObjectId }) => 
            !slot.classId.equals(classId)
        );

        if (daySchedule.timeSlots.length === 0) {
            this.availability = this.availability.filter((a: AvailabilitySlot) => 
                a.date.toDateString() !== date.toDateString()
            );
        }
    }

    await this.save();
};

roomSchema.methods.validateCapacity = function(
    this: IRoomDocument,
    requestedCapacity: number
): boolean {
    return requestedCapacity <= this.capacity;
};

roomSchema.methods.checkScheduleConflict = async function(
    this: IRoomDocument,
    timeSlot: TimeSlot
): Promise<boolean> {
    return !(await this.isAvailable(new Date(), timeSlot));
};

// Static methods
roomSchema.static('findAvailableRooms', async function(
    date: Date,
    timeSlot: TimeSlot,
    requiredCapacity?: number
): Promise<IRoomDocument[]> {
    const query: mongoose.FilterQuery<IRoomDocument> = {
        status: 'active'
    };

    if (requiredCapacity) {
        query.capacity = { $gte: requiredCapacity };
    }

    // Exclude rooms under maintenance
    query['maintenanceSchedule'] = {
        $not: {
            $elemMatch: {
                startDate: { $lte: date },
                endDate: { $gte: date }
            }
        }
    };

    const rooms = await this.find(query);
    const availableRooms = [];

    for (const room of rooms) {
        if (await room.isAvailable(date, timeSlot)) {
            availableRooms.push(room);
        }
    }

    return availableRooms;
});

roomSchema.static('validateRoomAvailability', async function(
    roomId: Types.ObjectId,
    date: Date,
    timeSlot: TimeSlot
): Promise<boolean> {
    const room = await this.findById(roomId);
    if (!room) {
        throw new AppError(404, 'Room not found');
    }

    return room.isAvailable(date, timeSlot);
});

// Middleware
roomSchema.pre('save', async function(
    this: IRoomDocument,
    next: (err?: CallbackError) => void
) {
    try {
        // Validate maintenance schedule dates
        if (this.isModified('maintenanceSchedule')) {
            this.maintenanceSchedule.forEach((schedule: MaintenanceScheduleItem) => {
                if (schedule.startDate > schedule.endDate) {
                    throw new AppError(400, 'Maintenance end date must be after start date');
                }
            });
        }

        // Validate time slots
        if (this.isModified('availability')) {
            this.availability.forEach((day: AvailabilitySlot) => {
                day.timeSlots.forEach((slot: TimeSlot) => {
                    const startMinutes = this.convertTimeToMinutes(slot.start);
                    const endMinutes = this.convertTimeToMinutes(slot.end);
                    
                    if (startMinutes >= endMinutes) {
                        throw new AppError(400, 'Time slot end must be after start');
                    }
                });
            });
        }

        next();
    } catch (error) {
        if (error instanceof Error) {
            next(error as CallbackError);
        } else {
            next(new Error('Unknown error occurred'));
        }
    }
});

export const Room = mongoose.model<IRoomDocument, IRoomModel>('Room', roomSchema);