import { UserRole } from "@/types";

// Helper functions for checking permissions

export const canCreateNotes = (userRole: string): boolean => {
  return ['SuperAdmin', 'Manager', 'Teacher'].includes(userRole);
};

export const canAccessAllNotesTab = (userRole: string): boolean => {
  return ['SuperAdmin', 'Manager'].includes(userRole);
};

export const canAccessStudentNotesTab = (userRole: string): boolean => {
  return ['SuperAdmin', 'Manager', 'Teacher'].includes(userRole);
};

export const canAccessClassNotesTab = (userRole: string): boolean => {
  return ['SuperAdmin', 'Manager', 'Teacher'].includes(userRole);
};

export const canEditNote = (userRole: string, noteCreatorId: string, currentUserId: string): boolean => {
  // SuperAdmin and Manager can edit all notes
  if (['SuperAdmin', 'Manager'].includes(userRole)) {
    return true;
  }
  
  // Other roles can only edit their own notes
  return noteCreatorId === currentUserId;
};

export const canDeleteNote = (userRole: string, noteCreatorId: string, currentUserId: string): boolean => {
  // SuperAdmin and Manager can delete all notes
  if (['SuperAdmin', 'Manager'].includes(userRole)) {
    return true;
  }
  
  // Other roles can only delete their own notes
  return noteCreatorId === currentUserId;
};

export const canViewNotes = (userRole: string): boolean => {
  return ['SuperAdmin', 'Manager', 'Teacher', 'Secretary'].includes(userRole);
};

export const getNoteTypeIcon = (type: string): string => {
  switch (type) {
    case 'academic':
      return '📚';
    case 'behavioral':
      return '🔍';
    case 'administrative':
      return '📋';
    case 'announcement':
      return '📢';
    default:
      return '📝';
  }
};

export const getNoteTypeColor = (type: string): string => {
  switch (type) {
    case 'academic':
      return 'text-green-500';
    case 'behavioral':
      return 'text-orange-500';
    case 'administrative':
      return 'text-purple-500';
    case 'announcement':
      return 'text-blue-500';
    default:
      return 'text-gray-500';
  }
};
