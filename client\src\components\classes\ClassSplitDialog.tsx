
import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { useMutation, useQuery } from "@tanstack/react-query";
import { fetchStudents } from "@/services/studentService";
import { Student } from "@/types/student";
import { Class } from "@/types/class";

interface ClassSplitDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSplit?: (studentIds: string[]) => void;
  classId?: string;
  sourceClass?: Class;
  onSuccess?: () => void;
}

const ClassSplitDialog = ({
  open,
  onOpenChange,
  onSplit,
  classId,
  sourceClass,
  onSuccess,
}: ClassSplitDialogProps) => {
  const [searchQuery, setSearchQuery] = useState("");
  const [allStudents, setAllStudents] = useState<Student[]>([]);
  const [selectedStudents, setSelectedStudents] = useState<string[]>([]);
  const { toast } = useToast();

  // Use the classId from sourceClass if available
  const effectiveClassId = classId || (sourceClass?.id || "");

  const studentResponse = useQuery({
    queryKey: ["students", { classId: effectiveClassId }],
    queryFn: () => fetchStudents({ classId: effectiveClassId }),
  });

  useEffect(() => {
    if (studentResponse?.data) {
      const availableStudents = Array.isArray(studentResponse.data) 
        ? studentResponse.data 
        : studentResponse.data.data || [];
      setAllStudents(availableStudents);
    }
  }, [studentResponse]);

  const handleStudentSelect = (studentId: string) => {
    setSelectedStudents((prevSelected) =>
      prevSelected.includes(studentId)
        ? prevSelected.filter((id) => id !== studentId)
        : [...prevSelected, studentId]
    );
  };

  const handleSplit = () => {
    if (selectedStudents.length === 0) {
      toast({
        title: "No students selected",
        description: "Please select at least one student to split from the class.",
      });
      return;
    }

    if (onSplit) {
      onSplit(selectedStudents);
    }
    
    onOpenChange(false);
    setSelectedStudents([]);
    
    if (onSuccess) {
      onSuccess();
    }
  };

  const filteredStudents = allStudents.filter((student) =>
    `${student.firstName} ${student.lastName}`
      .toLowerCase()
      .includes(searchQuery.toLowerCase())
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Split Students from Class</DialogTitle>
          <DialogDescription>
            Select students to split from the current class.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="search">Search:</Label>
            <Input
              type="search"
              id="search"
              placeholder="Search students..."
              className="col-span-3"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          <div className="divide-y divide-border rounded-md border">
            {filteredStudents.map((student) => (
              <div
                key={student.id}
                className="flex items-center justify-between p-4"
              >
                <Label htmlFor={student.id} className="cursor-pointer">
                  {student.firstName} {student.lastName}
                </Label>
                <Input
                  type="checkbox"
                  id={student.id}
                  checked={selectedStudents.includes(student.id)}
                  onChange={() => handleStudentSelect(student.id)}
                />
              </div>
            ))}
          </div>
        </div>
        <DialogFooter>
          <Button type="button" variant="secondary" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button type="button" onClick={handleSplit}>
            Split Students
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ClassSplitDialog;
