
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Link } from "react-router-dom";
import { cn } from "@/lib/utils";
import { Calendar, CreditCard, DollarSign, FileText, Plus } from "lucide-react";
import { Payment } from "@/types/student";

interface StudentPaymentsTabProps {
  studentId: string;
  payments: Payment[];
}

const StudentPaymentsTab = ({ studentId, payments }: StudentPaymentsTabProps) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Calculate total payments
  const totalPaid = payments.reduce((total, payment) => {
    return payment.status === 'completed' ? total + payment.amount : total;
  }, 0);

  return (
    <div className="space-y-6">
      {/* Payment Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="p-4 pb-2">
            <CardTitle className="text-base">Total Paid</CardTitle>
          </CardHeader>
          <CardContent className="p-4 pt-0">
            <div className="flex items-center gap-2">
              <DollarSign size={20} className="text-green-600" />
              <span className="text-2xl font-bold text-green-600">
                {formatCurrency(totalPaid)}
              </span>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="p-4 pb-2">
            <CardTitle className="text-base">Recent Payment</CardTitle>
          </CardHeader>
          <CardContent className="p-4 pt-0">
            <div className="space-y-1">
              {payments.length > 0 ? (
                <>
                  <div className="text-2xl font-bold">
                    {formatCurrency(payments[0].amount)}
                  </div>
                  <div className="text-sm text-muted-foreground">
                    {formatDate(payments[0].date)}
                  </div>
                </>
              ) : (
                <div className="text-sm text-muted-foreground">
                  No payments yet
                </div>
              )}
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="p-4 pb-2">
            <CardTitle className="text-base">Next Payment</CardTitle>
          </CardHeader>
          <CardContent className="p-4 pt-0">
            <div className="space-y-1">
              {payments.length > 0 && payments[0].nextDueDate ? (
                <>
                  <div className="text-lg font-medium">
                    Due {formatDate(payments[0].nextDueDate)}
                  </div>
                  <Button size="sm" className="mt-2" asChild>
                    <Link to={`/students/${studentId}/payment`}>
                      <Plus size={14} className="mr-1" />
                      Record Payment
                    </Link>
                  </Button>
                </>
              ) : (
                <div className="text-sm text-muted-foreground">
                  No upcoming payments scheduled
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Payment History */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Payment History</CardTitle>
            <CardDescription>
              All payment records for this student
            </CardDescription>
          </div>
          <Button asChild>
            <Link to={`/students/${studentId}/payment`}>
              <Plus size={16} className="mr-2" />
              New Payment
            </Link>
          </Button>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Receipt</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Method</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Next Due</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {payments.length > 0 ? (
                payments.map((payment) => (
                  <TableRow key={payment.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <FileText size={16} className="text-muted-foreground" />
                        {payment.receiptNumber || "—"}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Calendar size={16} className="text-muted-foreground" />
                        {formatDate(payment.date)}
                      </div>
                    </TableCell>
                    <TableCell className="font-medium">
                      {formatCurrency(payment.amount)}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <CreditCard size={16} className="text-muted-foreground" />
                        <span className="capitalize">{payment.method}</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge
                        variant="outline"
                        className={cn(
                          "capitalize",
                          payment.status === "completed" && "border-green-200 bg-green-50 text-green-700",
                          payment.status === "pending" && "border-yellow-200 bg-yellow-50 text-yellow-700",
                          payment.status === "cancelled" && "border-red-200 bg-red-50 text-red-700"
                        )}
                      >
                        {payment.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {payment.nextDueDate ? formatDate(payment.nextDueDate) : "—"}
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} className="h-24 text-center">
                    No payment records found.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default StudentPaymentsTab;
