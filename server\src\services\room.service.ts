// server/src/services/room.service.ts
import mongoose, { FilterQuery, Error as MongoError } from 'mongoose';
import { Room } from '../models/room.model';
import { Class } from '../models/class.model';
import { AppError } from '../types/error.types';
import { SystemLogger } from './logger.service';
import {
    IRoom,
    RoomQueryOptions,
    CreateRoomDTO,
    UpdateRoomDTO,
    ScheduleMaintenanceDTO,
    RoomResponseDTO,
    TimeSlot,
    RoomUtilizationStats,
    RoomExportOptions
} from '../types/room.types';

export class RoomService {
    /**
     * Core CRUD Operations
     */
    static async getRooms(
        options: RoomQueryOptions,
        requestingUserId: string
    ) {
        try {
            const {
                page = 1,
                limit = 10,
                sortBy = 'name',
                sortOrder = 'asc',
                status,
                building,
                floor,
                minCapacity,
                features,
                search,
                availableAt
            } = options;

            const query: FilterQuery<IRoom> = {};

            // Apply filters
            if (status) query.status = status;
            if (building) query.building = building;
            if (floor) query.floor = floor;
            if (minCapacity) query.capacity = { $gte: minCapacity };
            if (features?.length) query.features = { $all: features };

            if (search) {
                query.$or = [
                    { name: { $regex: search, $options: 'i' } },
                    { building: { $regex: search, $options: 'i' } }
                ];
            }

            let rooms = await Room.find(query)
                .populate('lastModifiedBy', 'username')
                .sort({ [sortBy]: sortOrder })
                .skip((page - 1) * limit)
                .limit(limit)
                .lean();

            // Filter by availability if requested
            if (availableAt) {
                const availableRooms = await Promise.all(
                    rooms.map(async room => {
                        const isAvailable = await Room.validateRoomAvailability(
                            room._id,
                            availableAt.date,
                            availableAt.timeSlot
                        );
                        return isAvailable ? room : null;
                    })
                );
                // Properly type the filtered rooms
                rooms = availableRooms.filter((room): room is (typeof rooms)[0] => room !== null);
            }

            const total = await Room.countDocuments(query);

            await SystemLogger.log({
                severity: 'info',
                category: 'room_management',
                action: 'list_rooms',
                performedBy: requestingUserId,
                details: { filters: options },
                status: 'success',
                timestamp: new Date()
            });

            return {
                rooms: rooms.map(room => this.formatRoomResponse(room)),
                pagination: {
                    total,
                    page,
                    limit,
                    pages: Math.ceil(total / limit)
                }
            };
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error fetching rooms');
        }
    }

    static async getRoomById(
        id: string,
        requestingUserId: string
    ): Promise<RoomResponseDTO> {
        try {
            const room = await Room.findById(id)
                .populate('lastModifiedBy', 'username')
                .populate({
                    path: 'availability.timeSlots.classId',
                    select: 'name'
                })
                .populate('maintenanceSchedule.scheduledBy', 'username')
                .lean();

            if (!room) {
                throw new AppError(404, 'Room not found');
            }

            await SystemLogger.log({
                severity: 'info',
                category: 'room_management',
                action: 'view_room',
                performedBy: requestingUserId,
                targetId: id,
                details: { roomName: room.name },
                status: 'success',
                timestamp: new Date()
            });

            return this.formatRoomResponse(room);
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error fetching room');
        }
    }

    static async createRoom(
        roomData: CreateRoomDTO,
        createdBy: string
    ): Promise<RoomResponseDTO> {
        try {
            const room = new Room({
                ...roomData,
                lastModifiedBy: new mongoose.Types.ObjectId(createdBy)
            });
    
            await room.save();
    
            const populatedRoom = await Room.findById(room._id)
                .populate('lastModifiedBy', 'username')
                .lean();
    
            if (!populatedRoom) {
                throw new AppError(500, 'Error retrieving created room');
            }
    
            await SystemLogger.log({
                severity: 'info',
                category: 'room_management',
                action: 'create_room',
                performedBy: createdBy,
                targetId: room._id.toString(),
                details: {
                    roomName: room.name,
                    building: room.building
                },
                status: 'success',
                timestamp: new Date()
            });
    
            return this.formatRoomResponse(populatedRoom);
        } catch (error: unknown) {
            // MongoDB error code 11000 indicates a duplicate key error
            if (error instanceof Error && 
                typeof (error as any).code === 'number' && 
                (error as any).code === 11000) {
                throw new AppError(400, 'Room with this name already exists');
            }
            throw error instanceof AppError ? error : new AppError(500, 'Error creating room');
        }
    }

    static async updateRoom(
        id: string,
        updateData: UpdateRoomDTO,
        updatedBy: string
    ): Promise<RoomResponseDTO> {
        try {
            const room = await Room.findById(id);
            if (!room) {
                throw new AppError(404, 'Room not found');
            }

            // Validate capacity update if there are scheduled classes
            if (updateData.capacity && updateData.capacity < room.capacity) {
                const hasScheduledClasses = room.availability.some(day => 
                    day.timeSlots.length > 0
                );
                if (hasScheduledClasses) {
                    throw new AppError(400, 'Cannot reduce room capacity while classes are scheduled');
                }
            }

            Object.assign(room, updateData);
            room.lastModifiedBy = new mongoose.Types.ObjectId(updatedBy);
            room.lastModifiedAt = new Date();

            await room.save();

            const updatedRoom = await Room.findById(room._id)
                .populate('lastModifiedBy', 'username')
                .lean();

            if (!updatedRoom) {
                throw new AppError(500, 'Error retrieving updated room');
            }

            await SystemLogger.log({
                severity: 'info',
                category: 'room_management',
                action: 'update_room',
                performedBy: updatedBy,
                targetId: id,
                details: { updates: updateData },
                status: 'success',
                timestamp: new Date()
            });

            return this.formatRoomResponse(updatedRoom);
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error updating room');
        }
    }

    /**
     * Room Scheduling and Availability
     */
    static async checkRoomAvailability(
        id: string,
        date: Date,
        timeSlot: TimeSlot,
        requiredCapacity?: number
    ): Promise<{
        available: boolean;
        reason?: string;
        nextAvailableSlot?: TimeSlot;
    }> {
        try {
            const room = await Room.findById(id);
            if (!room) {
                throw new AppError(404, 'Room not found');
            }

            // Check capacity requirement
            if (requiredCapacity && room.capacity < requiredCapacity) {
                return {
                    available: false,
                    reason: 'Room capacity insufficient'
                };
            }

            // Check room status
            if (room.status !== 'active') {
                return {
                    available: false,
                    reason: `Room is ${room.status}`
                };
            }

            // Check maintenance schedule
            const isUnderMaintenance = room.maintenanceSchedule.some(schedule => 
                date >= schedule.startDate && date <= schedule.endDate
            );

            if (isUnderMaintenance) {
                return {
                    available: false,
                    reason: 'Room under maintenance'
                };
            }

            const isAvailable = await room.isAvailable(date, timeSlot);
            if (!isAvailable) {
                // Find next available slot
                const nextSlot = await this.findNextAvailableSlot(room, date, timeSlot);
                return {
                    available: false,
                    reason: 'Time slot not available',
                    nextAvailableSlot: nextSlot
                };
            }

            return { available: true };
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error checking room availability');
        }
    }

    static async findAvailableRooms(
        date: Date,
        timeSlot: TimeSlot,
        options: {
            minCapacity?: number;
            building?: string;
            floor?: number;
        } = {}
    ): Promise<RoomResponseDTO[]> {
        try {
            const query: FilterQuery<IRoom> = {
                status: 'active'
            };

            if (options.minCapacity) {
                query.capacity = { $gte: options.minCapacity };
            }
            if (options.building) {
                query.building = options.building;
            }
            if (options.floor) {
                query.floor = options.floor;
            }

            const rooms = await Room.find(query)
                .populate('lastModifiedBy', 'username')
                .lean();

            const availableRooms = [];
            for (const room of rooms) {
                const roomDoc = await Room.findById(room._id);
                if (roomDoc && await roomDoc.isAvailable(date, timeSlot)) {
                    availableRooms.push(room);
                }
            }

            return availableRooms.map(room => this.formatRoomResponse(room));
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error finding available rooms');
        }
    }

    static async scheduleMaintenance(
        id: string,
        maintenanceData: ScheduleMaintenanceDTO,
        scheduledBy: string
    ): Promise<RoomResponseDTO> {
        try {
            const room = await Room.findById(id);
            if (!room) {
                throw new AppError(404, 'Room not found');
            }

            // Validate maintenance dates
            if (maintenanceData.startDate <= new Date()) {
                throw new AppError(400, 'Maintenance start date must be in the future');
            }
            if (maintenanceData.endDate <= maintenanceData.startDate) {
                throw new AppError(400, 'Maintenance end date must be after start date');
            }

            // Check for scheduling conflicts
            const hasConflicts = room.availability.some(day => {
                return day.date >= maintenanceData.startDate &&
                       day.date <= maintenanceData.endDate &&
                       day.timeSlots.length > 0;
            });

            if (hasConflicts) {
                throw new AppError(400, 'Cannot schedule maintenance - room has scheduled classes during this period');
            }

            // Add maintenance schedule
            room.maintenanceSchedule.push({
                startDate: maintenanceData.startDate,
                endDate: maintenanceData.endDate,
                reason: maintenanceData.reason,
                scheduledBy: new mongoose.Types.ObjectId(scheduledBy)
            });

            room.lastModifiedBy = new mongoose.Types.ObjectId(scheduledBy);
            room.lastModifiedAt = new Date();

            await room.save();

            const updatedRoom = await Room.findById(room._id)
                .populate('lastModifiedBy', 'username')
                .populate('maintenanceSchedule.scheduledBy', 'username')
                .lean();

            if (!updatedRoom) {
                throw new AppError(500, 'Error retrieving updated room');
            }

            await SystemLogger.log({
                severity: 'info',
                category: 'room_management',
                action: 'schedule_maintenance',
                performedBy: scheduledBy,
                targetId: id,
                details: maintenanceData,
                status: 'success',
                timestamp: new Date()
            });

            return this.formatRoomResponse(updatedRoom);
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error scheduling maintenance');
        }
    }

    /**
     * Room Analytics and Reports
     */
    static async getRoomUtilization(
        id: string,
        startDate: Date,
        endDate: Date
    ): Promise<RoomUtilizationStats> {
        try {
            const room = await Room.findById(id)
                .populate({
                    path: 'availability.timeSlots.classId',
                    select: 'name'
                });

            if (!room) {
                throw new AppError(404, 'Room not found');
            }

            // Calculate total available hours (excluding maintenance)
            const workingHoursPerDay = 12; // Assuming 8am to 8pm
            const totalDays = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
            const maintenanceDays = room.maintenanceSchedule.reduce((total, schedule) => {
                if (schedule.startDate >= startDate && schedule.endDate <= endDate) {
                    return total + Math.ceil((schedule.endDate.getTime() - schedule.startDate.getTime()) / (1000 * 60 * 60 * 24));
                }
                return total;
            }, 0);

            const totalAvailableHours = (totalDays - maintenanceDays) * workingHoursPerDay;

            // Calculate used hours
            const usedHours = room.availability.reduce((total, day) => {
                if (day.date >= startDate && day.date <= endDate) {
                    return total + day.timeSlots.reduce((hours, slot) => {
                        const start = this.timeToMinutes(slot.start);
                        const end = this.timeToMinutes(slot.end);
                        return hours + (end - start) / 60;
                    }, 0);
                }
                return total;
            }, 0);

            // Calculate periodic usage (weekly)
            const periodicUsage = [];
            let currentDate = new Date(startDate);
            while (currentDate <= endDate) {
                const weekEnd = new Date(currentDate);
                weekEnd.setDate(weekEnd.getDate() + 6);

                const weeklyHours = room.availability.reduce((total, day) => {
                    if (day.date >= currentDate && day.date <= weekEnd) {
                        return total + day.timeSlots.reduce((hours, slot) => {
                            const start = this.timeToMinutes(slot.start);
                            const end = this.timeToMinutes(slot.end);
                            return hours + (end - start) / 60;
                        }, 0);
                    }
                    return total;
                }, 0);

                periodicUsage.push({
                    period: `${currentDate.toISOString().split('T')[0]} to ${weekEnd.toISOString().split('T')[0]}`,
                    hours: weeklyHours,
                    rate: (weeklyHours / (workingHoursPerDay * 7)) * 100
                });

                currentDate.setDate(currentDate.getDate() + 7);
            }

            return {
                roomId: room._id.toString(),
                roomName: room.name,
                totalHours: usedHours,
                utilizationRate: (usedHours / totalAvailableHours) * 100,
                classesHeld: room.availability.reduce((total, day) => {
                    if (day.date >= startDate && day.date <= endDate) {
                        return total + day.timeSlots.length;
                    }
                    return total;
                }, 0),
                periodicUsage
            };
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error calculating room utilization');
        }
    }

    static async getRoomSchedule(
        id: string,
        startDate: Date,
        endDate: Date
    ): Promise<Array<{
        date: string;
        timeSlots: Array<{
            start: string;
            end: string;
            class: {
                id: string;
                name: string;
            };
        }>;
        maintenanceScheduled?: boolean;
    }>> {
        try {
            const room = await Room.findById(id)
                .populate({
                    path: 'availability.timeSlots.classId',
                    select: 'name'
                });

            if (!room) {
                throw new AppError(404, 'Room not found');
            }

            const schedule: any[] = [];
            let currentDate = new Date(startDate);

            while (currentDate <= endDate) {
                const dateStr = currentDate.toISOString().split('T')[0];
                const daySchedule = room.availability.find(day => 
                    day.date.toISOString().split('T')[0] === dateStr
                );

                // Check if maintenance is scheduled for this date
                const maintenanceScheduled = room.maintenanceSchedule.some(
                    schedule => currentDate >= schedule.startDate && currentDate <= schedule.endDate
                );

                schedule.push({
                    date: dateStr,
                    timeSlots: daySchedule?.timeSlots.map(slot => ({
                        start: slot.start,
                        end: slot.end,
                        class: {
                            id: slot.classId._id.toString(),
                            name: (slot.classId as any).name
                        }
                    })) || [],
                    maintenanceScheduled
                });

                currentDate.setDate(currentDate.getDate() + 1);
            }

            return schedule;
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error fetching room schedule');
        }
    }

    static async exportRoomData(options: RoomExportOptions): Promise<string> {
        try {
            const query: FilterQuery<IRoom> = {};

            // Apply date range filter for related data if specified
            if (options.dateRange) {
                if (options.includeSchedule) {
                    query['availability.date'] = {
                        $gte: options.dateRange.start,
                        $lte: options.dateRange.end
                    };
                }
                if (options.includeMaintenance) {
                    query['maintenanceSchedule.startDate'] = {
                        $gte: options.dateRange.start,
                        $lte: options.dateRange.end
                    };
                }
            }

            const rooms = await Room.find(query)
                .populate('lastModifiedBy', 'username')
                .populate({
                    path: 'availability.timeSlots.classId',
                    select: 'name'
                })
                .populate('maintenanceSchedule.scheduledBy', 'username')
                .lean();

            const exportData = rooms.map(room => {
                const formattedRoom = this.formatRoomResponse(room);
                
                // Remove schedule/maintenance if not requested
                if (!options.includeSchedule) {
                    delete (formattedRoom as any).currentSchedule;
                }
                if (!options.includeMaintenance) {
                    delete (formattedRoom as any).maintenanceSchedule;
                }

                return formattedRoom;
            });

            if (options.format === 'json') {
                return JSON.stringify(exportData, null, 2);
            }

            // CSV format
            const fields = options.fields || Object.keys(exportData[0]);
            const header = fields.join(',');
            const rows = exportData.map(room =>
                fields.map(field => this.formatCSVValue(this.getNestedValue(room, field)))
                    .join(',')
            );

            return [header, ...rows].join('\n');
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error exporting room data');
        }
    }

    /**
     * Helper Methods
     */
    private static async findNextAvailableSlot(
        room: any,
        startDate: Date,
        timeSlot: TimeSlot
    ): Promise<TimeSlot | undefined> {
        const maxDays = 7; // Look up to 7 days ahead
        let currentDate = new Date(startDate);
        currentDate.setDate(currentDate.getDate() + 1); // Start from next day

        for (let i = 0; i < maxDays; i++) {
            const isAvailable = await room.isAvailable(currentDate, timeSlot);
            if (isAvailable) {
                return timeSlot;
            }
            currentDate.setDate(currentDate.getDate() + 1);
        }

        return undefined;
    }

    private static formatRoomResponse(room: any): RoomResponseDTO {
        return {
            id: room._id.toString(),
            name: room.name,
            capacity: room.capacity,
            building: room.building,
            floor: room.floor,
            features: room.features || [],
            status: room.status,
            currentSchedule: room.availability?.[0] ? {
                date: room.availability[0].date.toISOString().split('T')[0],
                timeSlots: room.availability[0].timeSlots.map((slot: any) => ({
                    start: slot.start,
                    end: slot.end,
                    class: {
                        id: slot.classId._id.toString(),
                        name: slot.classId.name
                    }
                }))
            } : undefined,
            maintenanceSchedule: room.maintenanceSchedule?.map((schedule: any) => ({
                startDate: schedule.startDate.toISOString(),
                endDate: schedule.endDate.toISOString(),
                reason: schedule.reason,
                scheduledBy: {
                    id: schedule.scheduledBy._id.toString(),
                    username: schedule.scheduledBy.username
                }
            })) || [],
            lastModified: {
                at: room.lastModifiedAt,
                by: {
                    id: room.lastModifiedBy._id.toString(),
                    username: room.lastModifiedBy.username
                }
            }
        };
    }

    private static timeToMinutes(time: string): number {
        const [hours, minutes] = time.split(':').map(Number);
        return hours * 60 + minutes;
    }

    private static getNestedValue(obj: any, path: string): any {
        return path.split('.').reduce((current, key) => current?.[key], obj);
    }

    private static formatCSVValue(value: any): string {
        if (value === null || value === undefined) return '';
        if (value instanceof Date) return value.toISOString();
        if (typeof value === 'object') return JSON.stringify(value);
        return `"${String(value).replace(/"/g, '""')}"`;
    }
}