
import { useState } from "react";
import { toast } from "sonner";
import { 
  Database, 
  Trash, 
  Refresh<PERSON><PERSON>, 
  AlertTriangle,
  Server,
  <PERSON><PERSON><PERSON>,
  Clock,
  PowerOff,
  Play,
  FileCog,
  HardDrive
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import CustomCard from "@/components/ui/CustomCard";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { optimizeDatabase, clearSystemCache, toggleMaintenanceMode } from "@/services/systemService";

const MaintenanceTools = () => {
  const [isOptimizingDatabase, setIsOptimizingDatabase] = useState(false);
  const [isClearingCache, setIsClearingCache] = useState(false);
  const [maintenanceMode, setMaintenanceMode] = useState(false);
  const [scheduledMaintenance, setScheduledMaintenance] = useState(false);
  const [scheduledTime, setScheduledTime] = useState("01:00");

  const [optimizationProgress, setOptimizationProgress] = useState(0);

  const handleOptimizeDatabase = async () => {
    try {
      setIsOptimizingDatabase(true);
      setOptimizationProgress(0);
      
      // Simulate progress
      const interval = setInterval(() => {
        setOptimizationProgress(prev => {
          const newProgress = prev + 5;
          if (newProgress >= 100) {
            clearInterval(interval);
            return 100;
          }
          return newProgress;
        });
      }, 200);
      
      // Call API to optimize database
      const result = await optimizeDatabase();
      
      // Log this maintenance operation
      console.log("Database optimization performed");
      
      clearInterval(interval);
      setOptimizationProgress(100);
      
      if (result.success) {
        toast.success(result.message);
      } else {
        toast.error("Database optimization failed");
      }
    } catch (error) {
      toast.error("Failed to optimize database");
      console.error("Optimization error:", error);
    } finally {
      setTimeout(() => {
        setIsOptimizingDatabase(false);
        setOptimizationProgress(0);
      }, 1000);
    }
  };

  const handleClearCache = async () => {
    try {
      setIsClearingCache(true);
      
      // Call API to clear cache
      const result = await clearSystemCache();
      
      // Log this maintenance operation
      console.log("System cache cleared");
      
      if (result.success) {
        toast.success(result.message);
      } else {
        toast.error("Failed to clear cache");
      }
    } catch (error) {
      toast.error("Failed to clear cache");
      console.error("Cache clear error:", error);
    } finally {
      setTimeout(() => {
        setIsClearingCache(false);
      }, 1000);
    }
  };

  const handleToggleMaintenanceMode = async () => {
    try {
      const newState = !maintenanceMode;
      
      // Call API to toggle maintenance mode
      const result = await toggleMaintenanceMode(newState);
      
      // Log this maintenance operation
      console.log(`Maintenance mode ${newState ? 'enabled' : 'disabled'}`);
      
      if (result.success) {
        setMaintenanceMode(newState);
        toast.success(result.message);
      } else {
        toast.error(`Failed to ${newState ? 'enable' : 'disable'} maintenance mode`);
      }
    } catch (error) {
      toast.error("Failed to toggle maintenance mode");
      console.error("Maintenance mode error:", error);
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-xl font-semibold">System Maintenance Tools</h2>
        <p className="text-muted-foreground">
          Perform system maintenance operations and monitor system health
        </p>
      </div>

      {/* Maintenance Mode */}
      <CustomCard className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium flex items-center">
            <Server className="mr-2 h-5 w-5 text-primary" />
            Maintenance Mode
          </h3>
          <div className="flex items-center space-x-2">
            <Switch 
              id="maintenance-mode" 
              checked={maintenanceMode}
              onCheckedChange={handleToggleMaintenanceMode}
            />
            <Label htmlFor="maintenance-mode">
              {maintenanceMode ? "Enabled" : "Disabled"}
            </Label>
          </div>
        </div>
        
        <p className="text-sm mb-4">
          When maintenance mode is enabled, the system will display a maintenance page to all users except administrators.
          All background jobs and scheduled tasks will continue to run.
        </p>
        
        <Alert variant={maintenanceMode ? "destructive" : "default"}>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            {maintenanceMode 
              ? "Maintenance mode is currently active. The system is not accessible to regular users." 
              : "The system is currently operating normally and is accessible to all users."}
          </AlertDescription>
        </Alert>
        
        <Separator className="my-4" />
        
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Switch 
              id="scheduled-maintenance" 
              checked={scheduledMaintenance}
              onCheckedChange={(checked) => setScheduledMaintenance(checked)}
            />
            <Label htmlFor="scheduled-maintenance">
              Schedule Maintenance Window
            </Label>
          </div>
          
          <div className="flex space-x-4 items-center">
            <Label htmlFor="maintenance-time" className="whitespace-nowrap">Start Time:</Label>
            <input 
              id="maintenance-time" 
              type="time" 
              value={scheduledTime}
              onChange={(e) => setScheduledTime(e.target.value)}
              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              disabled={!scheduledMaintenance}
            />
            <select 
              className="flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              disabled={!scheduledMaintenance}
            >
              <option value="daily">Daily</option>
              <option value="weekly">Weekly (Sunday)</option>
              <option value="monthly">Monthly (1st day)</option>
            </select>
            <select 
              className="flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
              disabled={!scheduledMaintenance}
            >
              <option value="30">30 minutes</option>
              <option value="60">1 hour</option>
              <option value="120">2 hours</option>
              <option value="240">4 hours</option>
            </select>
          </div>
          
          <Button disabled={!scheduledMaintenance}>
            <Clock className="h-4 w-4 mr-2" />
            Save Schedule
          </Button>
        </div>
      </CustomCard>

      {/* Database Maintenance */}
      <CustomCard className="p-6">
        <h3 className="text-lg font-medium flex items-center mb-4">
          <Database className="mr-2 h-5 w-5 text-primary" />
          Database Maintenance
        </h3>
        
        <div className="space-y-4">
          {isOptimizingDatabase ? (
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span>Optimizing database...</span>
                <span>{optimizationProgress}%</span>
              </div>
              <Progress value={optimizationProgress} className="h-2" />
            </div>
          ) : (
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button>
                  <Database className="h-4 w-4 mr-2" />
                  Optimize Database
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Optimize Database</AlertDialogTitle>
                  <AlertDialogDescription>
                    Database optimization will analyze tables, optimize indices, and reclaim unused space.
                    This process may take several minutes to complete and could temporarily impact system performance.
                    It is recommended to run this during low-usage periods or in maintenance mode.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={handleOptimizeDatabase}>
                    Proceed
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          )}
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex justify-between p-3 bg-muted rounded-lg">
              <span className="text-sm">Last Optimization:</span>
              <span className="text-sm font-medium">2 days ago</span>
            </div>
            <div className="flex justify-between p-3 bg-muted rounded-lg">
              <span className="text-sm">Database Size:</span>
              <span className="text-sm font-medium">1.2 GB</span>
            </div>
            <div className="flex justify-between p-3 bg-muted rounded-lg">
              <span className="text-sm">Fragmentation:</span>
              <span className="text-sm font-medium">8%</span>
            </div>
            <div className="flex justify-between p-3 bg-muted rounded-lg">
              <span className="text-sm">Active Connections:</span>
              <span className="text-sm font-medium">12</span>
            </div>
          </div>
          
          <Separator />
          
          <div className="flex flex-col md:flex-row gap-4">
            <Button variant="outline">
              <FileCog className="h-4 w-4 mr-2" />
              Verify Integrity
            </Button>
            
            <Button variant="outline">
              <BarChart className="h-4 w-4 mr-2" />
              View Query Stats
            </Button>
          </div>
        </div>
      </CustomCard>

      {/* Cache Management */}
      <CustomCard className="p-6">
        <h3 className="text-lg font-medium flex items-center mb-4">
          <HardDrive className="mr-2 h-5 w-5 text-primary" />
          Cache Management
        </h3>
        
        <div className="space-y-4">
          <p className="text-sm">
            Clear system caches to free up memory and resolve potential issues with stale data.
          </p>
          
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Clearing cache may temporarily slow down the system as caches are rebuilt.
            </AlertDescription>
          </Alert>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="flex flex-col items-center bg-muted p-4 rounded-lg">
              <span className="text-sm text-muted-foreground mb-1">System Cache</span>
              <span className="text-lg font-medium">128 MB</span>
              <span className="text-xs text-muted-foreground">12 hours old</span>
            </div>
            
            <div className="flex flex-col items-center bg-muted p-4 rounded-lg">
              <span className="text-sm text-muted-foreground mb-1">Query Cache</span>
              <span className="text-lg font-medium">64 MB</span>
              <span className="text-xs text-muted-foreground">45 minutes old</span>
            </div>
            
            <div className="flex flex-col items-center bg-muted p-4 rounded-lg">
              <span className="text-sm text-muted-foreground mb-1">Template Cache</span>
              <span className="text-lg font-medium">32 MB</span>
              <span className="text-xs text-muted-foreground">2 days old</span>
            </div>
            
            <div className="flex flex-col items-center bg-muted p-4 rounded-lg">
              <span className="text-sm text-muted-foreground mb-1">User Sessions</span>
              <span className="text-lg font-medium">48 MB</span>
              <span className="text-xs text-muted-foreground">18 active</span>
            </div>
          </div>
          
          <div className="flex flex-col md:flex-row gap-4">
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button>
                  <Trash className="h-4 w-4 mr-2" />
                  Clear All Caches
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Clear All Caches</AlertDialogTitle>
                  <AlertDialogDescription>
                    This will clear all system caches including query cache, template cache, and application cache.
                    The system may experience temporary slowdowns as caches are rebuilt.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction onClick={handleClearCache} disabled={isClearingCache}>
                    {isClearingCache ? (
                      <>
                        <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                        Clearing...
                      </>
                    ) : (
                      "Clear All Caches"
                    )}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
            
            <div className="flex gap-2">
              <Button variant="outline">
                <Play className="h-4 w-4 mr-2" />
                Run Garbage Collection
              </Button>
              
              <Button variant="outline" disabled>
                <PowerOff className="h-4 w-4 mr-2" />
                Restart Services
              </Button>
            </div>
          </div>
        </div>
      </CustomCard>

      {/* System Checks */}
      <CustomCard className="p-6">
        <h3 className="text-lg font-medium flex items-center mb-4">
          <RefreshCw className="mr-2 h-5 w-5 text-primary" />
          System Health Checks
        </h3>
        
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button variant="outline" className="h-auto py-3 justify-start">
              <span className="flex flex-col items-start">
                <span className="font-medium">Check File Permissions</span>
                <span className="text-xs text-muted-foreground">Verify file and directory permissions</span>
              </span>
            </Button>
            
            <Button variant="outline" className="h-auto py-3 justify-start">
              <span className="flex flex-col items-start">
                <span className="font-medium">Error Log Analysis</span>
                <span className="text-xs text-muted-foreground">Analyze error patterns and frequencies</span>
              </span>
            </Button>
            
            <Button variant="outline" className="h-auto py-3 justify-start">
              <span className="flex flex-col items-start">
                <span className="font-medium">Performance Diagnostics</span>
                <span className="text-xs text-muted-foreground">Identify performance bottlenecks</span>
              </span>
            </Button>
            
            <Button variant="outline" className="h-auto py-3 justify-start">
              <span className="flex flex-col items-start">
                <span className="font-medium">Security Audit</span>
                <span className="text-xs text-muted-foreground">Check for security vulnerabilities</span>
              </span>
            </Button>
          </div>
          
          <Button className="w-full">
            <RefreshCw className="h-4 w-4 mr-2" />
            Run All System Checks
          </Button>
        </div>
      </CustomCard>

      {/* Scheduled Tasks */}
      <CustomCard className="p-6">
        <h3 className="text-lg font-medium mb-4">Scheduled Maintenance Tasks</h3>
        
        <div className="space-y-4">
          <div className="overflow-x-auto">
            <table className="min-w-full border-collapse">
              <thead>
                <tr className="border-b">
                  <th className="text-left p-2">Task</th>
                  <th className="text-left p-2">Schedule</th>
                  <th className="text-left p-2">Last Run</th>
                  <th className="text-left p-2">Status</th>
                  <th className="text-left p-2">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-b">
                  <td className="p-2">Database Backup</td>
                  <td className="p-2">Daily, 3:00 AM</td>
                  <td className="p-2">10 hours ago</td>
                  <td className="p-2">
                    <span className="text-green-600 text-xs bg-green-100 px-2 py-1 rounded-full">Success</span>
                  </td>
                  <td className="p-2">
                    <Button variant="ghost" size="sm">Run Now</Button>
                  </td>
                </tr>
                <tr className="border-b">
                  <td className="p-2">Cache Cleanup</td>
                  <td className="p-2">Every 6 hours</td>
                  <td className="p-2">4 hours ago</td>
                  <td className="p-2">
                    <span className="text-green-600 text-xs bg-green-100 px-2 py-1 rounded-full">Success</span>
                  </td>
                  <td className="p-2">
                    <Button variant="ghost" size="sm">Run Now</Button>
                  </td>
                </tr>
                <tr className="border-b">
                  <td className="p-2">Database Optimization</td>
                  <td className="p-2">Weekly, Sunday 2:00 AM</td>
                  <td className="p-2">5 days ago</td>
                  <td className="p-2">
                    <span className="text-green-600 text-xs bg-green-100 px-2 py-1 rounded-full">Success</span>
                  </td>
                  <td className="p-2">
                    <Button variant="ghost" size="sm">Run Now</Button>
                  </td>
                </tr>
                <tr className="border-b">
                  <td className="p-2">Integrity Check</td>
                  <td className="p-2">Monthly, 1st day, 4:00 AM</td>
                  <td className="p-2">12 days ago</td>
                  <td className="p-2">
                    <span className="text-amber-600 text-xs bg-amber-100 px-2 py-1 rounded-full">Warning</span>
                  </td>
                  <td className="p-2">
                    <Button variant="ghost" size="sm">Run Now</Button>
                  </td>
                </tr>
                <tr>
                  <td className="p-2">Log Rotation</td>
                  <td className="p-2">Daily, 1:00 AM</td>
                  <td className="p-2">1 day ago</td>
                  <td className="p-2">
                    <span className="text-red-600 text-xs bg-red-100 px-2 py-1 rounded-full">Failed</span>
                  </td>
                  <td className="p-2">
                    <Button variant="ghost" size="sm">Run Now</Button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          
          <div className="flex justify-end">
            <Button variant="outline">
              <Settings className="h-4 w-4 mr-2" />
              Configure Tasks
            </Button>
          </div>
        </div>
      </CustomCard>
    </div>
  );
};

export default MaintenanceTools;

const Settings = ({ className, ...props }: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
      {...props}
    >
      <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z" />
      <circle cx="12" cy="12" r="3" />
    </svg>
  );
};
