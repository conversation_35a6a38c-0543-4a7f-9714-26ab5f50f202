// server/src/middleware/class.middleware.ts
import { Request, Response, NextFunction } from 'express';
import { Class } from '../models/class.model';
import { User } from '../models/user.model';
import { AppError } from '../types/error.types';
import { ClassService } from '../services/class.service';
import { TeacherAssignment, TeacherSchedule, MakeupClass } from '../types/class.types';
import { Types } from 'mongoose';
import { IClassDocument } from '../models/class.model';

export class ClassMiddleware {
  // Middleware to validate class exists
  static async validateClassExists(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const classId = req.params.id;
      const classDoc = await Class.findById(classId);

      if (!classDoc) {
        throw new AppError(404, 'Class not found');
      }

      // Attach class to request for later use
      req.classDoc = classDoc;
      next();
    } catch (error) {
      next(error);
    }
  }

  // Middleware to check class capacity
  static async checkClassCapacity(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const classDoc = req.classDoc;
      if (!classDoc) {
        throw new AppError(500, 'Class document not found in request');
      }

      if (classDoc.currentStudentCount >= classDoc.capacity) {
        throw new AppError(400, 'Class has reached maximum capacity');
      }

      next();
    } catch (error) {
      next(error);
    }
  }

  // Middleware to validate teacher assignments
  static async validateTeacherAssignments(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { teachers } = req.body;
      if (!teachers || !Array.isArray(teachers)) {
        next();
        return;
      }

      // Get all teacher IDs from the request
      const teacherIds = teachers.map(t => t.teacherId);

      // Verify all teachers exist and are active
      const foundTeachers = await User.find({
        _id: { $in: teacherIds },
        role: 'teacher',
        status: 'active'
      });

      if (foundTeachers.length !== teacherIds.length) {
        throw new AppError(400, 'One or more teachers not found or inactive');
      }

      // Check for schedule conflicts
      const classId = req.params.id; // Will be undefined for new classes
      for (const teacher of teachers) {
        const hasConflict = await Class.validateScheduleConflicts(
          new Types.ObjectId(teacher.teacherId), // Convert string to ObjectId
          teacher.schedule,
          classId ? new Types.ObjectId(classId) : undefined
        );

        if (hasConflict) {
          throw new AppError(400, `Schedule conflict detected for teacher ${teacher.teacherId}`);
        }
      }

      next();
    } catch (error) {
      next(error);
    }
  }

  // Update the validateMakeupClass method
  static async validateMakeupClass(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { makeupDate, teacherId } = req.body;
      const classDoc = req.classDoc as IClassDocument;

      if (!classDoc) {
        throw new AppError(500, 'Class document not found in request');
      }

      // Validate teacher belongs to class
      const teacher = classDoc.teachers.find((t: TeacherAssignment) => 
        t.teacherId.toString() === teacherId
      );

      if (!teacher) {
        throw new AppError(400, 'Teacher is not assigned to this class');
      }

      // Check for existing makeup class on same date
      const existingMakeup = classDoc.makeupClasses.find(
        (makeup: MakeupClass) => makeup.makeupDate.getTime() === new Date(makeupDate).getTime()
      );

      if (existingMakeup) {
        throw new AppError(400, 'A makeup class is already scheduled for this date');
      }

      next();
    } catch (error) {
      next(error);
    }
  }

  // Middleware to validate room availability
  static async validateRoomAvailability(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { room, teachers } = req.body;
      if (!room || !teachers) {
        next();
        return;
      }

      const schedules = teachers.flatMap((t: any) => t.schedule);
      const availableRooms = await Class.findAvailableRooms(schedules);

      if (!availableRooms.includes(room)) {
        throw new AppError(400, 'Room is not available for the specified schedule');
      }

      next();
    } catch (error) {
      next(error);
    }
  }

  // Middleware to validate class dates
  static async validateClassDates(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { startDate, endDate } = req.body;
      if (!startDate || !endDate) {
        next();
        return;
      }

      const start = new Date(startDate);
      const end = new Date(endDate);

      if (start >= end) {
        throw new AppError(400, 'End date must be after start date');
      }

      // For updates, ensure no active students if changing dates
      if (req.method === 'PATCH' && req.classDoc) {
        const classDoc = req.classDoc;
        if (classDoc.currentStudentCount > 0 && 
            (start.getTime() !== classDoc.startDate.getTime() || 
             end.getTime() !== classDoc.endDate.getTime())) {
          throw new AppError(400, 'Cannot modify class dates while students are enrolled');
        }
      }

      next();
    } catch (error) {
      next(error);
    }
  }

  // Middleware to validate merge operations
  static async validateMergeOperation(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const sourceClass = req.classDoc;
      if (!sourceClass) {
        throw new AppError(500, 'Source class not found in request');
      }

      const { targetClassId } = req.body;
      const targetClass = await Class.findById(targetClassId);

      if (!targetClass) {
        throw new AppError(404, 'Target class not found');
      }

      // Validate merge conditions
      if (sourceClass.level !== targetClass.level) {
        throw new AppError(400, 'Cannot merge classes of different levels');
      }

      if (targetClass.status !== 'active') {
        throw new AppError(400, 'Target class must be active');
      }

      if (sourceClass.currentStudentCount + targetClass.currentStudentCount > sourceClass.capacity) {
        throw new AppError(400, 'Combined student count exceeds class capacity');
      }

      next();
    } catch (error) {
      next(error);
    }
  }

  // Middleware for bulk operations validation
  static async validateBulkOperation(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { classIds, operation, newValue } = req.body;

      // Verify all classes exist
      const classes = await Class.find({ _id: { $in: classIds } });
      if (classes.length !== classIds.length) {
        throw new AppError(400, 'One or more classes not found');
      }

      // Operation-specific validations
      switch (operation) {
        case 'changeLevel':
          if (!newValue) {
            throw new AppError(400, 'New level value is required');
          }
          break;

        case 'changeRoom':
          if (!newValue) {
            throw new AppError(400, 'New room value is required');
          }
          // Room availability will be checked in the service
          break;

        case 'deactivate':
          // Check if any classes have active students
          const classesWithStudents = classes.filter(c => c.currentStudentCount > 0);
          if (classesWithStudents.length > 0) {
            throw new AppError(400, 'Cannot deactivate classes with active students');
          }
          break;
      }

      next();
    } catch (error) {
      next(error);
    }
  }

  static async validateTeacherReplacement(
    req: Request,
    res: Response,
    next: NextFunction
  ): Promise<void> {
    try {
      const { originalTeacherId, newTeacherId, scheduleToReplace } = req.body;
      const classDoc = req.classDoc;
  
      if (!classDoc) {
        throw new AppError(500, 'Class document not found in request');
      }
  
      // Check if original teacher is assigned to this class
      const originalTeacher = classDoc.teachers.find(
        t => t.teacherId.toString() === originalTeacherId
      );
  
      if (!originalTeacher) {
        throw new AppError(400, 'Original teacher is not assigned to this class');
      }
  
      // Validate schedules to replace are part of the original teacher's schedule
      for (const schedule of scheduleToReplace) {
        const hasSchedule = originalTeacher.schedule.some(
          s => s.day === schedule.day && 
               s.timeStart === schedule.timeStart && 
               s.timeEnd === schedule.timeEnd
        );
  
        if (!hasSchedule) {
          throw new AppError(400, 'One or more schedules are not assigned to the original teacher');
        }
      }
  
      // Verify new teacher exists and is active
      const newTeacher = await User.findOne({
        _id: newTeacherId,
        role: 'teacher',
        status: 'active'
      });
  
      if (!newTeacher) {
        throw new AppError(400, 'New teacher not found or is inactive');
      }
  
      // Check for schedule conflicts for the new teacher
      const hasConflict = await Class.validateScheduleConflicts(
        new Types.ObjectId(newTeacherId),
        scheduleToReplace,
        classDoc._id
      );
  
      if (hasConflict) {
        throw new AppError(400, 'New teacher has schedule conflicts with the proposed schedules');
      }
  
      next();
    } catch (error) {
      next(error);
    }
  }
}

// Augment Express Request interface
declare global {
    namespace Express {
      interface Request {
        classDoc?: IClassDocument;
      }
    }
  }