
export interface Teacher {
  id: string;
  name: string;
  schedule: TeacherScheduleItem[];
}

export interface TeacherScheduleItem {
  day: string;
  timeStart: string;
  timeEnd: string;
}

export interface ClassCapacity {
  total: number;
  current: number;
  available: number;
}

export interface ClassSchedule {
  startDate: string;
  endDate: string;
  days?: ClassScheduleDay[];
}

export interface ClassScheduleDay {
  day: string;
  times: ClassScheduleTime[];
}

export interface ClassScheduleTime {
  start: string;
  end: string;
  teacher: string;
}

export interface StudentHistory {
  studentId: string;
  studentName: string;
  joinDate: string;
  leaveDate: string;
  reason: string;
}

export interface MakeupClass {
  id?: string;
  originalDate: string;
  makeupDate: string;
  teacherId?: string;
  teacherName?: string;
  roomId?: string;
  room?: string;
  timeStart?: string;
  timeEnd?: string;
  reason: string;
  notes?: string;
  status: 'scheduled' | 'completed' | 'cancelled';
  notifyStudents?: boolean;
  createdBy?: string;
  approvedBy?: {
    id: string;
    username: string;
  };
}

export interface Class {
  id: string;
  name: string;
  level: string;
  teachers: Teacher[];
  room: string;
  capacity: ClassCapacity;
  schedule: ClassSchedule;
  status: 'active' | 'inactive' | 'merged';
  studentHistory?: StudentHistory[];
  makeupClasses?: MakeupClass[];
  createdAt?: string;
  updatedAt?: string;
}

export interface ClassesApiResponse {
  success: boolean;
  data: Class[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}

export interface ClassApiResponse {
  success: boolean;
  data: Class;
}

export interface ClassFilter {
  page: number;
  limit: number;
  sortBy?: 'name' | 'level' | 'startDate' | 'endDate' | 'currentStudentCount' | 'status';
  sortOrder?: 'asc' | 'desc';
  status?: 'active' | 'inactive' | 'merged' | '';
  level?: string;
  teacherId?: string;
  search?: string;
  room?: string;
}

export interface Room {
  id: string;
  name: string;
}

export interface TeacherFormData {
  teacherId: string;
  schedule: TeacherScheduleItem[];
}

export interface ClassFormData {
  name: string;
  level: string;
  room: string;
  capacity: number;
  startDate: Date | undefined;
  endDate: Date | undefined;
  teachers: TeacherFormData[];
  status?: 'active' | 'inactive' | 'merged';
}

export interface MakeupClassFormData {
  originalDate: Date | null;
  makeupDate: Date | null;
  teacherId: string;
  roomId: string;
  timeStart: string;
  timeEnd: string;
  reason: string;
  reasonType: 'teacher-absence' | 'holiday' | 'weather' | 'other';
  notes: string;
  notifyStudents: boolean;
}

export interface RoomAvailability {
  id: string;
  name: string;
  available: boolean;
  conflicts?: string[];
}

export interface ScheduleWithMakeup {
  regularSchedule: ClassScheduleDay[];
  makeupClasses: MakeupClass[];
}
