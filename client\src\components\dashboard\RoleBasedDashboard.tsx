
import { useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { getDashboardData } from "@/services/dashboardService";
import { toast } from "sonner";
import { 
  SuperAdminDashboard,
  ManagerDashboard,
  SecretaryDashboard,
  TeacherDashboard
} from "./role-dashboards";

interface RoleBasedDashboardProps {
  userRole: string;
  dateRange: {
    from: Date;
    to: Date;
  };
  isLoading: boolean;
  setIsLoading: (loading: boolean) => void;
}

const RoleBasedDashboard = ({ 
  userRole, 
  dateRange,
  isLoading,
  setIsLoading
}: RoleBasedDashboardProps) => {
  const [activeTab, setActiveTab] = useState<string>("overview");
  
  // Fetch dashboard data based on user role with better error handling
  const { data: dashboardData, isLoading: isLoadingData, error } = useQuery({
    queryKey: ['dashboardData', userRole, dateRange.from.toDateString(), dateRange.to.toDateString()],
    queryFn: async () => {
      try {
        const data = await getDashboardData(userRole, {
          fromDate: dateRange.from.toISOString(),
          toDate: dateRange.to.toISOString()
        });
        return data || {}; // Ensure we always return at least an empty object
      } catch (err) {
        console.error("Error fetching dashboard data:", err);
        // Return an empty object instead of throwing to prevent the query from failing
        return {};
      }
    },
    retry: 1,
    staleTime: 1000 * 60 * 5, // 5 minutes
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    refetchOnReconnect: false,
    enabled: !!userRole, // Only run query if userRole exists
  });

  // Update parent loading state - But add a timeout to ensure we don't get stuck
  useEffect(() => {
    setIsLoading(isLoadingData);
    
    // Safety timeout - if loading takes more than 5 seconds, force it to complete
    if (isLoadingData) {
      const timer = setTimeout(() => {
        setIsLoading(false);
      }, 5000);
      
      return () => clearTimeout(timer);
    }
  }, [isLoadingData, setIsLoading]);

  // Handle data loading errors
  useEffect(() => {
    if (error) {
      console.error("Dashboard data error:", error);
      toast.error("Failed to load dashboard data. Using default display.");
      setIsLoading(false); // Ensure loading is turned off on error
    }
  }, [error, setIsLoading]);

  // Render dashboard based on user role with fallback for each role
  const renderDashboardByRole = () => {
    const emptyData = {};
    
    switch (userRole) {
      case "superAdmin":
        return (
          <SuperAdminDashboard
            dashboardData={dashboardData || emptyData}
            isLoadingData={isLoading}
            activeTab={activeTab}
            setActiveTab={setActiveTab}
          />
        );
      case "manager":
        return (
          <ManagerDashboard
            dashboardData={dashboardData || emptyData}
            isLoadingData={isLoading}
          />
        );
      case "secretary":
        return (
          <SecretaryDashboard
            dashboardData={dashboardData || emptyData}
            isLoadingData={isLoading}
          />
        );
      case "teacher":
        return (
          <TeacherDashboard
            dashboardData={dashboardData || emptyData}
            isLoadingData={isLoading}
          />
        );
      default:
        // Fallback to SuperAdmin dashboard if role is unknown
        return (
          <SuperAdminDashboard 
            dashboardData={dashboardData || emptyData} 
            isLoadingData={isLoading}
            activeTab={activeTab}
            setActiveTab={setActiveTab}
          />
        );
    }
  };

  return (
    <div className="space-y-6">
      {renderDashboardByRole()}
    </div>
  );
};

export default RoleBasedDashboard;
