// server/src/routes/room.routes.ts
import express from 'express';
import { Room<PERSON>ontroller } from '../controllers/room.controller';
import { authenticateToken, authorizeRoles } from '../middleware/auth.middleware';
import { validate } from '../middleware/validate.middleware';
import { roomValidation } from '../validations/room.validation';
import { catchAsync } from '../middleware/error.middleware';
import { apiLimiter } from '../middleware/rateLimiter.middleware';
import { RoomMiddleware } from '../middleware/room.middleware';

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Get rooms with role-based access
router.get(
    '/',
    authorizeRoles('superAdmin', 'manager', 'secretary', 'teacher'),
    validate(roomValidation.getRoomsQuery),
    catchAsync(RoomController.getRooms)
);

// Create new room
router.post(
    '/',
    authorizeRoles('superAdmin', 'manager'),
    validate(roomValidation.createRoom),
    RoomMiddleware.validateRoomName,
    catchAsync(RoomController.createRoom)
);

// Find available rooms (must be before /:id route)
router.get(
    '/available',
    authorizeRoles('superAdmin', 'manager', 'secretary', 'teacher'),
    RoomMiddleware.validateTimeSlotFormat,
    catchAsync(RoomController.findAvailableRooms)
);

// Export room data (must be before /:id route)
router.get(
    '/export',
    authorizeRoles('superAdmin', 'manager'),
    validate(roomValidation.exportRooms),
    catchAsync(RoomController.exportRoomData)
);

// Get room by ID
router.get(
    '/:id',
    authorizeRoles('superAdmin', 'manager', 'secretary', 'teacher'),
    RoomMiddleware.validateRoomExists,
    catchAsync(RoomController.getRoomById)
);

// Update room
router.patch(
    '/:id',
    authorizeRoles('superAdmin', 'manager'),
    validate(roomValidation.updateRoom),
    RoomMiddleware.validateRoomExists,
    RoomMiddleware.validateRoomName,
    RoomMiddleware.validateCapacityChange,
    RoomMiddleware.validateStatusChange,
    catchAsync(RoomController.updateRoom)
);

// Schedule maintenance
router.post(
    '/:id/maintenance',
    authorizeRoles('superAdmin', 'manager'),
    validate(roomValidation.scheduleMaintenance),
    RoomMiddleware.validateRoomExists,
    RoomMiddleware.validateMaintenanceSchedule,
    catchAsync(RoomController.scheduleMaintenance)
);

// Check room availability
router.get(
    '/:id/availability',
    authorizeRoles('superAdmin', 'manager', 'secretary', 'teacher'),
    RoomMiddleware.validateRoomExists,
    RoomMiddleware.validateTimeSlotFormat,
    catchAsync(RoomController.checkAvailability)
);

// Get room schedule
router.get(
    '/:id/schedule',
    authorizeRoles('superAdmin', 'manager', 'secretary', 'teacher'),
    RoomMiddleware.validateRoomExists,
    RoomMiddleware.validateDateRange,
    catchAsync(RoomController.getRoomSchedule)
);

// Get room utilization stats
router.get(
    '/:id/utilization',
    authorizeRoles('superAdmin', 'manager'),
    RoomMiddleware.validateRoomExists,
    RoomMiddleware.validateDateRange,
    catchAsync(RoomController.getRoomUtilization)
);

// Apply rate limiting to all routes
router.use(apiLimiter);

export default router;