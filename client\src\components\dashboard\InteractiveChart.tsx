
import { useEffect, useState } from "react";
import { 
  <PERSON>sponsive<PERSON><PERSON>r, 
  AreaChart, 
  Area, 
  BarChart,
  Bar,
  XAxis, 
  YAxis, 
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  Cell
} from "recharts";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { Skeleton } from "@/components/ui/skeleton";
import DashboardSection from "./DashboardSection";
import { FilePenLine, Download } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface ChartData {
  [key: string]: any;
}

interface InteractiveChartProps {
  data: ChartData[];
  title: string;
  description?: string;
  isLoading?: boolean;
  type?: 'area' | 'bar' | 'pie';
  xKey: string;
  yKeys: {
    key: string;
    name: string;
    color: string;
  }[];
  height?: number;
  className?: string;
  actions?: boolean;
  onExport?: () => void;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];

const InteractiveChart = ({
  data,
  title,
  description,
  isLoading = false,
  type = 'area',
  xKey,
  yKeys,
  height = 300,
  className,
  actions = false,
  onExport
}: InteractiveChartProps) => {
  const [chartType, setChartType] = useState<'area' | 'bar' | 'pie'>(type);
  
  // Format data for better display if needed
  const formattedData = [...data];

  const handleExport = () => {
    if (onExport) {
      onExport();
      return;
    }
    
    // Default export functionality - CSV
    const headers = [xKey, ...yKeys.map(y => y.name)].join(',');
    const csvData = formattedData.map(item => {
      return [
        item[xKey], 
        ...yKeys.map(y => item[y.key])
      ].join(',');
    }).join('\n');
    
    const csvContent = `${headers}\n${csvData}`;
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `${title.toLowerCase().replace(/\s+/g, '-')}-export.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const renderChart = () => {
    if (isLoading) {
      return <Skeleton className="w-full h-full" />;
    }

    if (formattedData.length === 0) {
      return <div className="flex items-center justify-center h-full text-muted-foreground">No data available</div>;
    }

    switch (chartType) {
      case 'area':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <AreaChart data={formattedData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
              <defs>
                {yKeys.map((yKey, index) => (
                  <linearGradient key={yKey.key} id={`color${yKey.key}`} x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor={yKey.color} stopOpacity={0.8}/>
                    <stop offset="95%" stopColor={yKey.color} stopOpacity={0.1}/>
                  </linearGradient>
                ))}
              </defs>
              <CartesianGrid strokeDasharray="3 3" opacity={0.3} />
              <XAxis 
                dataKey={xKey} 
                tick={{ fontSize: 12 }}
                axisLine={false}
                tickLine={false}
              />
              <YAxis 
                tick={{ fontSize: 12 }}
                axisLine={false}
                tickLine={false}
              />
              <Tooltip
                contentStyle={{
                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                  borderRadius: '8px',
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                  border: '1px solid #eaeaea',
                }}
              />
              <Legend />
              {yKeys.map((yKey, index) => (
                <Area
                  key={yKey.key}
                  type="monotone"
                  dataKey={yKey.key}
                  name={yKey.name}
                  stroke={yKey.color}
                  fillOpacity={1}
                  fill={`url(#color${yKey.key})`}
                  activeDot={{ r: 5 }}
                />
              ))}
            </AreaChart>
          </ResponsiveContainer>
        );
      
      case 'bar':
        return (
          <ResponsiveContainer width="100%" height={height}>
            <BarChart data={formattedData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
              <CartesianGrid strokeDasharray="3 3" opacity={0.3} vertical={false} />
              <XAxis 
                dataKey={xKey} 
                tick={{ fontSize: 12 }}
                axisLine={false}
                tickLine={false}
              />
              <YAxis 
                tick={{ fontSize: 12 }}
                axisLine={false}
                tickLine={false}
              />
              <Tooltip 
                contentStyle={{
                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                  borderRadius: '8px',
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                  border: '1px solid #eaeaea',
                }}
              />
              <Legend />
              {yKeys.map((yKey, index) => (
                <Bar
                  key={yKey.key}
                  dataKey={yKey.key}
                  name={yKey.name}
                  fill={yKey.color}
                  radius={[4, 4, 0, 0]}
                />
              ))}
            </BarChart>
          </ResponsiveContainer>
        );
      
      case 'pie':
        // For pie charts, we need to transform the data
        // We'll take the latest data point for simplicity
        const pieData = yKeys.map((yKey, index) => {
          const total = formattedData.reduce((sum, item) => sum + (parseFloat(item[yKey.key]) || 0), 0);
          return {
            name: yKey.name,
            value: total,
          };
        });
        
        return (
          <ResponsiveContainer width="100%" height={height}>
            <PieChart>
              <Pie
                data={pieData}
                cx="50%"
                cy="50%"
                labelLine={false}
                outerRadius={100}
                fill="#8884d8"
                dataKey="value"
                nameKey="name"
                label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
              >
                {pieData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip
                formatter={(value) => [`${value}`, '']}
                contentStyle={{
                  backgroundColor: 'rgba(255, 255, 255, 0.9)',
                  borderRadius: '8px',
                  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                  border: '1px solid #eaeaea',
                }}
              />
              <Legend />
            </PieChart>
          </ResponsiveContainer>
        );
      
      default:
        return null;
    }
  };

  return (
    <DashboardSection 
      title={title} 
      description={description}
      className={className}
      isLoading={isLoading}
      actions={
        actions ? (
          <div className="flex items-center space-x-2">
            <ToggleGroup type="single" value={chartType} onValueChange={(value) => value && setChartType(value as 'area' | 'bar' | 'pie')}>
              <ToggleGroupItem value="area" aria-label="Area Chart">Area</ToggleGroupItem>
              <ToggleGroupItem value="bar" aria-label="Bar Chart">Bar</ToggleGroupItem>
              <ToggleGroupItem value="pie" aria-label="Pie Chart">Pie</ToggleGroupItem>
            </ToggleGroup>
            <Button size="sm" variant="outline" onClick={handleExport}>
              <Download className="h-4 w-4 mr-1" />
              Export
            </Button>
          </div>
        ) : undefined
      }
    >
      <div className={cn("w-full", isLoading ? "h-[300px] flex items-center justify-center" : "")}>
        {renderChart()}
      </div>
    </DashboardSection>
  );
};

export default InteractiveChart;
