
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle, CardDescription } from "@/components/ui/card";
import { ClassSchedule } from "@/types/class";
import { capitalizeFirstLetter } from "@/lib/utils";

interface ClassScheduleSectionProps {
  schedule: ClassSchedule;
}

const ClassScheduleSection = ({ schedule }: ClassScheduleSectionProps) => {
  if (!schedule.days || schedule.days.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Class Schedule</CardTitle>
          <CardDescription>
            Weekly schedule for this class
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center py-4 text-muted-foreground">
          No schedule information available for this class.
        </CardContent>
      </Card>
    );
  }

  // Sort days in correct order
  const dayOrder = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"];
  const sortedDays = [...schedule.days].sort((a, b) => 
    dayOrder.indexOf(a.day.toLowerCase()) - dayOrder.indexOf(b.day.toLowerCase())
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle>Class Schedule</CardTitle>
        <CardDescription>
          Weekly schedule for this class
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {sortedDays.map((day) => (
            <div key={day.day} className="border rounded-md p-4">
              <h3 className="font-semibold text-lg mb-2">
                {capitalizeFirstLetter(day.day)}
              </h3>
              
              <div className="space-y-3">
                {day.times.map((time, index) => (
                  <div key={index} className="flex justify-between">
                    <div className="text-sm">
                      <span className="font-medium">{time.start}</span> - <span className="font-medium">{time.end}</span>
                    </div>
                    <div className="text-sm text-muted-foreground">
                      {time.teacher}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default ClassScheduleSection;
