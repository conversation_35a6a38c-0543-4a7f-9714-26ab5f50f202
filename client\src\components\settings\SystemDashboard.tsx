
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { 
  AlertCircle, 
  RefreshCw, 
  Server, 
  Database, 
  Users, 
  Clock,
  Activity,
  HardDrive,
  Download,
  Cpu,
  MemoryStick
} from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import CustomCard from "@/components/ui/CustomCard";
import { getSystemStats, triggerSystemBackup } from "@/lib/auth";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "../ui/alert-dialog";

const SystemDashboard = () => {
  const [isBackupInProgress, setIsBackupInProgress] = useState(false);

  const { data: stats, isLoading, refetch } = useQuery({
    queryKey: ["systemStats"],
    queryFn: getSystemStats,
  });

  const handleManualBackup = async () => {
    try {
      setIsBackupInProgress(true);
      await triggerSystemBackup();
      toast.success("Manual backup completed successfully");
      refetch();
    } catch (error) {
      toast.error("Failed to complete backup");
      console.error("Backup error:", error);
    } finally {
      setIsBackupInProgress(false);
    }
  };

  // Health status indicator
  const getHealthStatusColor = (status: string) => {
    switch (status) {
      case "healthy": return "text-green-500";
      case "warning": return "text-amber-500";
      case "critical": return "text-red-500";
      default: return "text-gray-500";
    }
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 gap-4">
        <CustomCard className="p-6">
          <div className="flex items-center justify-center py-8">
            <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
            <span className="ml-2 text-lg">Loading system data...</span>
          </div>
        </CustomCard>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div>
          <h2 className="text-xl font-semibold">System Dashboard</h2>
          <p className="text-muted-foreground">
            Overview of system health and performance metrics
          </p>
        </div>
        <div className="flex gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => refetch()}
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button 
                size="sm" 
                disabled={isBackupInProgress}
              >
                <Download className="h-4 w-4 mr-2" />
                {isBackupInProgress ? "Backup in progress..." : "Backup Now"}
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Initiate System Backup</AlertDialogTitle>
                <AlertDialogDescription>
                  This will start a full system backup. The process may take several minutes to complete.
                  System performance may be affected during the backup process.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction onClick={handleManualBackup}>
                  Start Backup
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </div>

      {/* System Health Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* System Health */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">System Health</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Server className={cn("h-6 w-6 mr-2", getHealthStatusColor(stats?.systemHealth || ""))} />
                <div className="text-2xl font-bold capitalize">{stats?.systemHealth || "Unknown"}</div>
              </div>
              <div className="text-right">
                <div className="text-xs text-muted-foreground">Uptime</div>
                <div className="text-sm font-medium">{stats?.uptime || "Unknown"}</div>
              </div>
            </div>
            <div className="mt-4 space-y-2">
              <div className="flex justify-between text-xs">
                <span>Error Rate</span>
                <span className={stats?.errorRate && stats.errorRate > 0.1 ? "text-red-500" : ""}>
                  {stats?.errorRate ? (stats.errorRate * 100).toFixed(2) + "%" : "N/A"}
                </span>
              </div>
              <div className="flex justify-between text-xs">
                <span>Response Time</span>
                <span>{stats?.averageResponseTime ? stats.averageResponseTime + "ms" : "N/A"}</span>
              </div>
              <div className="flex justify-between text-xs">
                <span>Recent Errors</span>
                <span className={stats?.recentErrors && stats.recentErrors > 0 ? "text-red-500" : ""}>
                  {stats?.recentErrors || 0}
                </span>
              </div>
              <div className="flex justify-between text-xs">
                <span>Pending Updates</span>
                <span className={stats?.pendingUpdates && stats.pendingUpdates > 0 ? "text-amber-500" : ""}>
                  {stats?.pendingUpdates || 0}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Resource Utilization */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Resource Utilization</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div>
              <div className="flex justify-between mb-1 text-xs">
                <div className="flex items-center">
                  <Cpu className="h-4 w-4 mr-1" />
                  <span>CPU</span>
                </div>
                <span>{stats?.systemLoad?.cpu || 0}%</span>
              </div>
              <Progress value={stats?.systemLoad?.cpu || 0} className="h-2" />
            </div>
            <div>
              <div className="flex justify-between mb-1 text-xs">
                <div className="flex items-center">
                  <MemoryStick className="h-4 w-4 mr-1" />
                  <span>Memory</span>
                </div>
                <span>{stats?.systemLoad?.memory || 0}%</span>
              </div>
              <Progress value={stats?.systemLoad?.memory || 0} className="h-2" />
            </div>
            <div>
              <div className="flex justify-between mb-1 text-xs">
                <div className="flex items-center">
                  <HardDrive className="h-4 w-4 mr-1" />
                  <span>Disk</span>
                </div>
                <span>{stats?.systemLoad?.disk || 0}%</span>
              </div>
              <Progress value={stats?.systemLoad?.disk || 0} className="h-2" />
            </div>
            <div>
              <div className="flex justify-between mb-1 text-xs">
                <div className="flex items-center">
                  <Activity className="h-4 w-4 mr-1" />
                  <span>Network</span>
                </div>
                <span>{stats?.systemLoad?.network || 0}%</span>
              </div>
              <Progress value={stats?.systemLoad?.network || 0} className="h-2" />
            </div>
          </CardContent>
        </Card>

        {/* User Activity */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">User Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <Users className="h-6 w-6 mr-2 text-blue-500" />
                <div className="text-2xl font-bold">{stats?.activeUsers || 0}</div>
              </div>
              <div className="text-right">
                <div className="text-xs text-muted-foreground">Active Now</div>
              </div>
            </div>
            <div className="mt-4 space-y-2">
              <div className="flex justify-between text-xs">
                <span>Last Backup</span>
                <span>
                  {stats?.lastBackup 
                    ? format(new Date(stats.lastBackup), "MMM d, yyyy HH:mm") 
                    : "Never"}
                </span>
              </div>
              <div className="flex items-center justify-between pt-2 mt-2 border-t text-xs">
                <span className="flex items-center">
                  <Clock className="h-3 w-3 mr-1" />
                  <span>Last Updated</span>
                </span>
                <span>{format(new Date(), "HH:mm:ss")}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* System Status Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="shadow-sm">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Database</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col">
              <div className="flex items-center">
                <Database className="h-6 w-6 mr-2 text-indigo-500" />
                <span className="text-lg font-semibold">Healthy</span>
              </div>
              <div className="mt-2 space-y-1 text-xs">
                <div className="grid grid-cols-2 gap-1">
                  <span className="text-muted-foreground">Connections:</span>
                  <span className="text-right">16 active</span>
                </div>
                <div className="grid grid-cols-2 gap-1">
                  <span className="text-muted-foreground">Query Time:</span>
                  <span className="text-right">85ms avg</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-sm">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">API Services</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col">
              <div className="flex items-center">
                <Server className="h-6 w-6 mr-2 text-blue-500" />
                <span className="text-lg font-semibold">Operational</span>
              </div>
              <div className="mt-2 space-y-1 text-xs">
                <div className="grid grid-cols-2 gap-1">
                  <span className="text-muted-foreground">Requests/min:</span>
                  <span className="text-right">342</span>
                </div>
                <div className="grid grid-cols-2 gap-1">
                  <span className="text-muted-foreground">Success Rate:</span>
                  <span className="text-right">99.7%</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-sm">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Storage</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col">
              <div className="flex items-center">
                <HardDrive className="h-6 w-6 mr-2 text-green-500" />
                <span className="text-lg font-semibold">68% Used</span>
              </div>
              <div className="mt-2 space-y-1 text-xs">
                <div className="grid grid-cols-2 gap-1">
                  <span className="text-muted-foreground">Free Space:</span>
                  <span className="text-right">128 GB</span>
                </div>
                <div className="grid grid-cols-2 gap-1">
                  <span className="text-muted-foreground">Backups Size:</span>
                  <span className="text-right">42 GB</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-sm">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Security</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col">
              <div className="flex items-center">
                <AlertCircle className="h-6 w-6 mr-2 text-amber-500" />
                <span className="text-lg font-semibold">Warning</span>
              </div>
              <div className="mt-2 space-y-1 text-xs">
                <div className="grid grid-cols-2 gap-1">
                  <span className="text-muted-foreground">Failed Logins:</span>
                  <span className="text-right text-amber-500">12 today</span>
                </div>
                <div className="grid grid-cols-2 gap-1">
                  <span className="text-muted-foreground">Updates:</span>
                  <span className="text-right text-amber-500">3 pending</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default SystemDashboard;
