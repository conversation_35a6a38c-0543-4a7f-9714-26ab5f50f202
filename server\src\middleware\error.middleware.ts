// server/src/middleware/error.middleware.ts
import { Request, Response, NextFunction } from 'express';
import { AppError } from '../types/error.types';
import { SystemLogger } from '../services/logger.service';

export const errorHandler = async (
    err: Error | AppError,
    req: Request,
    res: Response,
    next: NextFunction
): Promise<void> => {
    let error = err instanceof AppError ? err : new AppError(500, err.message || 'Internal Server Error');

    // Log error
    await SystemLogger.log({
        severity: 'error',
        category: 'system',
        action: 'error_handler',
        performedBy: 'system',
        details: {
            error: error.message,
            stack: error.stack,
            path: req.path,
            method: req.method,
            body: req.body,
            params: req.params,
            query: req.query,
        },
        status: 'failed',  // Changed from 'error' to 'failed'
        timestamp: new Date()
    });

    // Development vs Production error response
    if (process.env.NODE_ENV === 'development') {
        res.status(error.statusCode).json({
            status: 'error',
            message: error.message,
            stack: error.stack,
            error: error
        });
    } else {
        // Production: don't leak error details
        res.status(error.statusCode).json({
            status: 'error',
            message: error.isOperational ? error.message : 'Internal Server Error'
        });
    }
};

// Catch async errors
export const catchAsync = (fn: Function) => {
    return (req: Request, res: Response, next: NextFunction): void => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};