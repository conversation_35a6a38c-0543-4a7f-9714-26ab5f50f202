
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import CustomCard from "@/components/ui/CustomCard";
import { 
  Calendar as CalendarIcon, 
  Filter, 
  Search,
  AlertCircle,
  Info,
  AlertTriangle,
  Download,
  CheckCircle,
  FileDown,
  Trash2,
  RefreshCw
} from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Skeleton } from "@/components/ui/skeleton";
import { getSystemLogs } from "@/lib/auth";
import { Badge } from "@/components/ui/badge";
import { Card } from "../ui/card";

// Define types for the system logs
interface SystemLog {
  id: string;
  timestamp: string;
  severity: 'info' | 'warning' | 'error'; // Updated to be a union type
  category: string;
  action: string;
  performedBy: string;
  details: string;
  status: 'resolved' | 'unresolved';
}

interface LogFilterParams {
  severity?: string;
  category?: string;
  startDate?: Date;
  endDate?: Date;
  search?: string;
  page: number;
  pageSize: number;
}

const SystemLogs = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  const [severityFilter, setSeverityFilter] = useState<string | null>(null);
  const [categoryFilter, setCategoryFilter] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const pageSize = 10;

  const [filterParams, setFilterParams] = useState<LogFilterParams>({
    page: 1,
    pageSize: 10
  });

  const { data, isLoading, refetch } = useQuery({
    queryKey: ["systemLogs", filterParams],
    queryFn: () => getSystemLogs(),
  });

  // Ensure we cast the logs correctly to match our SystemLog type
  const logs: SystemLog[] = data?.logs?.map((log: any) => ({
    ...log,
    severity: log.severity as 'info' | 'warning' | 'error',
    status: log.status as 'resolved' | 'unresolved'
  })) || [];
  
  const totalPages = data?.pagination?.totalPages || 1;

  const applyFilters = () => {
    setFilterParams({
      severity: severityFilter || undefined,
      category: categoryFilter || undefined,
      startDate: selectedDate,
      page: 1, // Reset to first page on new filter
      pageSize,
      search: searchQuery || undefined
    });
  };

  const clearFilters = () => {
    setSearchQuery("");
    setSelectedDate(undefined);
    setSeverityFilter(null);
    setCategoryFilter(null);
    setFilterParams({
      page: 1,
      pageSize
    });
  };

  const handlePageChange = (newPage: number) => {
    if (newPage < 1 || newPage > totalPages) return;
    setPage(newPage);
    setFilterParams({
      ...filterParams,
      page: newPage
    });
  };

  const severityIcon = (severity: string) => {
    switch (severity) {
      case "error":
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      case "warning":
        return <AlertTriangle className="w-4 h-4 text-amber-500" />;
      case "info":
        return <Info className="w-4 h-4 text-blue-500" />;
      default:
        return null;
    }
  };

  const severityBadge = (severity: string) => {
    switch (severity) {
      case "error":
        return <Badge variant="destructive" className="text-xs font-normal">Error</Badge>;
      case "warning":
        return <Badge variant="outline" className="text-xs font-normal bg-amber-100 text-amber-800 border-amber-200">Warning</Badge>;
      case "info":
        return <Badge variant="outline" className="text-xs font-normal bg-blue-100 text-blue-800 border-blue-200">Info</Badge>;
      default:
        return null;
    }
  };

  const statusBadge = (status: string) => {
    if (status === "resolved") {
      return <Badge variant="outline" className="text-xs font-normal bg-green-100 text-green-800 border-green-200">Resolved</Badge>;
    } else {
      return <Badge variant="outline" className="text-xs font-normal bg-red-100 text-red-800 border-red-200">Unresolved</Badge>;
    }
  };

  const availableCategories = Array.from(new Set(logs.map(log => log.category)));

  const downloadLogs = () => {
    // In a real implementation, this would call an API endpoint to download logs
    // For now, just show a success message
    alert("Log download initiated. The file will be downloaded shortly.");
  };

  const handleLogAction = (logId: string, action: 'resolve' | 'delete') => {
    // In a real implementation, this would call an API to perform the action
    // For now, just show a success message
    if (action === 'resolve') {
      alert(`Log #${logId} marked as resolved`);
    } else {
      alert(`Log #${logId} deleted from system`);
    }
    // Refresh the logs after action
    refetch();
  };

  return (
    <div className="space-y-4">
      {/* Quick Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <Card className="p-4">
          <div className="flex justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Total Logs (24h)</p>
              <h4 className="text-2xl font-bold">254</h4>
            </div>
            <div className="bg-blue-100 p-2 rounded-full h-10 w-10 flex items-center justify-center">
              <FileDown className="h-5 w-5 text-blue-600" />
            </div>
          </div>
          <p className="text-xs text-muted-foreground mt-2">+12% from yesterday</p>
        </Card>
        
        <Card className="p-4">
          <div className="flex justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Errors</p>
              <h4 className="text-2xl font-bold">18</h4>
            </div>
            <div className="bg-red-100 p-2 rounded-full h-10 w-10 flex items-center justify-center">
              <AlertCircle className="h-5 w-5 text-red-600" />
            </div>
          </div>
          <p className="text-xs text-red-500 mt-2">5 unresolved errors</p>
        </Card>
        
        <Card className="p-4">
          <div className="flex justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Warnings</p>
              <h4 className="text-2xl font-bold">42</h4>
            </div>
            <div className="bg-amber-100 p-2 rounded-full h-10 w-10 flex items-center justify-center">
              <AlertTriangle className="h-5 w-5 text-amber-600" />
            </div>
          </div>
          <p className="text-xs text-muted-foreground mt-2">-8% from yesterday</p>
        </Card>
        
        <Card className="p-4">
          <div className="flex justify-between">
            <div>
              <p className="text-sm text-muted-foreground">Resolution Rate</p>
              <h4 className="text-2xl font-bold">92%</h4>
            </div>
            <div className="bg-green-100 p-2 rounded-full h-10 w-10 flex items-center justify-center">
              <CheckCircle className="h-5 w-5 text-green-600" />
            </div>
          </div>
          <p className="text-xs text-green-500 mt-2">Up 5% this week</p>
        </Card>
      </div>

      <div className="flex flex-col md:flex-row justify-between gap-4">
        <div className="flex w-full md:max-w-md items-center space-x-2">
          <Input
            placeholder="Search logs..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="flex-1"
          />
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" size="icon">
                <CalendarIcon className="h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={setSelectedDate}
                initialFocus
              />
            </PopoverContent>
          </Popover>
          <Button variant="outline" size="icon" onClick={() => applyFilters()}>
            <Search className="h-4 w-4" />
          </Button>
        </div>
        
        <div className="flex flex-wrap gap-2">
          <select
            value={severityFilter || ""}
            onChange={(e) => setSeverityFilter(e.target.value || null)}
            className="h-10 rounded-md border border-input bg-background px-3 py-2"
          >
            <option value="">All Severities</option>
            <option value="error">Error</option>
            <option value="warning">Warning</option>
            <option value="info">Info</option>
          </select>
          
          <select
            value={categoryFilter || ""}
            onChange={(e) => setCategoryFilter(e.target.value || null)}
            className="h-10 rounded-md border border-input bg-background px-3 py-2"
          >
            <option value="">All Categories</option>
            {availableCategories.map(category => (
              <option key={category} value={category}>{category}</option>
            ))}
          </select>
          
          <Button variant="outline" onClick={clearFilters}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Clear
          </Button>
          
          <Button variant="outline" onClick={downloadLogs}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      <CustomCard>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[80px]">Severity</TableHead>
              <TableHead className="w-[180px]">Timestamp</TableHead>
              <TableHead>Category</TableHead>
              <TableHead>Action</TableHead>
              <TableHead>User</TableHead>
              <TableHead>Details</TableHead>
              <TableHead>Status</TableHead>
              <TableHead className="w-[100px]">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              Array(5).fill(0).map((_, index) => (
                <TableRow key={index}>
                  <TableCell><Skeleton className="h-6 w-6 rounded-full" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-24" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-full" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                  <TableCell><Skeleton className="h-8 w-20" /></TableCell>
                </TableRow>
              ))
            ) : logs.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-10">
                  No logs found
                </TableCell>
              </TableRow>
            ) : (
              logs.map((log) => (
                <TableRow key={log.id}>
                  <TableCell>
                    <div className="flex items-center">
                      {severityBadge(log.severity)}
                    </div>
                  </TableCell>
                  <TableCell className="font-mono text-xs">
                    {format(new Date(log.timestamp), "yyyy-MM-dd HH:mm:ss")}
                  </TableCell>
                  <TableCell>
                    <span className="capitalize">{log.category}</span>
                  </TableCell>
                  <TableCell>
                    <span className="capitalize">
                      {log.action.replace(/_/g, " ")}
                    </span>
                  </TableCell>
                  <TableCell>{log.performedBy}</TableCell>
                  <TableCell className="max-w-xs truncate">
                    {log.details}
                  </TableCell>
                  <TableCell>
                    {statusBadge(log.status)}
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-1">
                      {log.status === 'unresolved' && (
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          className="h-8 w-8" 
                          onClick={() => handleLogAction(log.id, 'resolve')}
                          title="Mark as resolved"
                        >
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        </Button>
                      )}
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="h-8 w-8" 
                        onClick={() => handleLogAction(log.id, 'delete')}
                        title="Delete log"
                      >
                        <Trash2 className="h-4 w-4 text-red-500" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
        {/* Pagination */}
        {!isLoading && logs.length > 0 && (
          <div className="flex items-center justify-between px-4 py-4 border-t">
            <div className="text-sm text-muted-foreground">
              Showing page {page} of {totalPages}
            </div>
            <div className="flex space-x-2">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => handlePageChange(page - 1)}
                disabled={page <= 1}
              >
                Previous
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => handlePageChange(page + 1)}
                disabled={page >= totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </CustomCard>
    </div>
  );
};

export default SystemLogs;
