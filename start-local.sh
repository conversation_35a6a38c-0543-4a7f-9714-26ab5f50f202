#!/bin/bash

echo "Starting Vertex Education Management System for Local Network..."
echo ""

echo "Starting Backend Server..."
cd server
npm run dev &
BACKEND_PID=$!
cd ..

echo "Waiting for backend to start..."
sleep 5

echo "Starting Frontend Application..."
cd client
npm run dev &
FRONTEND_PID=$!
cd ..

echo ""
echo "========================================"
echo "Vertex Education Management System"
echo "========================================"
echo "Backend: http://localhost:3000"
echo "Frontend: http://localhost:5173"
echo ""
echo "Access from other devices on your network:"
echo "Replace 'localhost' with your computer's IP address"
echo "Example: http://*************:5173"
echo ""
echo "Simple Login Credentials:"
echo "Admin: admin@local / admin"
echo "Manager: manager@local / manager"
echo "Secretary: secretary@local / secretary"
echo "Teacher: teacher@local / teacher"
echo ""
echo "Press Ctrl+C to stop both servers"

# Function to cleanup background processes
cleanup() {
    echo ""
    echo "Stopping servers..."
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Wait for user to stop
wait
