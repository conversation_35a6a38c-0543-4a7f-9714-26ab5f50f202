
import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Calendar, FileText, User, BookOpen, MoreHorizontal } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import NoteDetailDialog from "./NoteDetailDialog";
import { getCurrentUser } from "@/lib/auth";
import { getNoteTypeColor } from "@/lib/noteUtils";

interface RecentNotesListProps {
  notes: any[];
  isLoading: boolean;
  onStudentSelect: (studentId: string) => void;
  onClassSelect: (classId: string) => void;
}

const RecentNotesList = ({ 
  notes, 
  isLoading, 
  onStudentSelect, 
  onClassSelect 
}: RecentNotesListProps) => {
  const [selectedNoteId, setSelectedNoteId] = useState<string | null>(null);
  
  const currentUser = getCurrentUser();
  
  // Find the currently selected note
  const selectedNote = selectedNoteId 
    ? notes.find(note => note.id === selectedNoteId) 
    : null;
  
  if (isLoading) {
    return (
      <div className="space-y-3">
        {Array(3).fill(0).map((_, index) => (
          <div key={index} className="flex items-start p-3 border rounded-lg">
            <Skeleton className="h-8 w-8 rounded mr-3" />
            <div className="space-y-2 flex-1">
              <Skeleton className="h-5 w-4/5" />
              <Skeleton className="h-4 w-2/3" />
              <div className="flex gap-2 pt-1">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-4 w-24" />
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }
  
  if (notes.length === 0) {
    return (
      <div className="text-center p-6 text-muted-foreground border rounded-lg">
        <p>No notes available</p>
      </div>
    );
  }
  
  // Helper function to get note type emoji
  const getNoteTypeEmoji = (type: string): string => {
    switch (type) {
      case 'academic': 
        return '📚';
      case 'behavioral':
        return '🔍';
      case 'administrative':
        return '📋';
      case 'announcement':
        return '📢';
      default:
        return '📝';
    }
  };
  
  return (
    <div className="space-y-3">
      {notes.map((note) => (
        <div key={note.id} className="p-3 border rounded-lg hover:bg-muted/30 transition-colors">
          <div className="flex items-start">
            <div className={`rounded-full p-2 mr-3 ${
              note.type === 'academic' 
                ? 'bg-green-100 text-green-800' 
                : note.type === 'administrative'
                ? 'bg-purple-100 text-purple-800'
                : note.type === 'announcement'
                ? 'bg-blue-100 text-blue-800'
                : 'bg-gray-100 text-gray-800'
            }`}>
              <FileText className="h-4 w-4" />
            </div>
            
            <div className="flex-1">
              <div className="flex justify-between items-start mb-1">
                <h4 className="font-medium text-sm truncate hover:text-primary cursor-pointer" onClick={() => setSelectedNoteId(note.id)}>
                  {note.title}
                </h4>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => setSelectedNoteId(note.id)}>
                      <FileText className="mr-2 h-4 w-4" />
                      View Note
                    </DropdownMenuItem>
                    {note.student && (
                      <DropdownMenuItem onClick={() => onStudentSelect(note.student.id)}>
                        <User className="mr-2 h-4 w-4" />
                        Student Notes
                      </DropdownMenuItem>
                    )}
                    {note.relatedClass && (
                      <DropdownMenuItem onClick={() => onClassSelect(note.relatedClass.id)}>
                        <BookOpen className="mr-2 h-4 w-4" />
                        Class Notes
                      </DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
              
              <p className="text-sm text-muted-foreground line-clamp-2 mb-2">
                {note.content}
              </p>
              
              <div className="flex flex-wrap gap-2 text-xs text-muted-foreground">
                <div className="flex items-center">
                  <Calendar className="h-3 w-3 mr-1" />
                  {new Date(note.createdAt).toLocaleDateString()}
                </div>
                
                {note.student && (
                  <button 
                    className="flex items-center text-blue-600 hover:underline"
                    onClick={() => onStudentSelect(note.student.id)}
                  >
                    <User className="h-3 w-3 mr-1" />
                    {note.student.name}
                  </button>
                )}
                
                {note.relatedClass && (
                  <button 
                    className="flex items-center text-blue-600 hover:underline"
                    onClick={() => onClassSelect(note.relatedClass.id)}
                  >
                    <BookOpen className="h-3 w-3 mr-1" />
                    {note.relatedClass.name}
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      ))}
      
      {selectedNote && (
        <NoteDetailDialog
          note={selectedNote}
          open={!!selectedNoteId}
          onOpenChange={(open) => {
            if (!open) setSelectedNoteId(null);
          }}
          onNoteUpdated={() => {}}
        />
      )}
    </div>
  );
};

export default RecentNotesList;
