// server/src/types/payment.types.ts
import { Types } from 'mongoose';

// Payment status and method enums
export type PaymentStatus = 'pending' | 'completed' | 'voided';
export type PaymentMethod = 'cash' | 'bank_transfer' | 'other';
export type PaymentPeriod = 'monthly' | 'quarterly' | 'semi_annual' | 'annual';

// Base payment interface
export interface IPayment {
    _id?: Types.ObjectId;
    studentId: Types.ObjectId;
    amount: number;
    remainingBalance: number;  // Track remaining balance for partial payments
    status: PaymentStatus;
    method: PaymentMethod;
    date: Date;
    nextDueDate?: Date;  // Optional for tracking next payment due date
    description: string;
    period?: PaymentPeriod;
    
    // Recording info
    recordedBy: Types.ObjectId;
    recordedAt: Date;
    
    // Modification tracking
    modifiedBy?: Types.ObjectId;
    modifiedAt?: Date;
    modificationHistory: Array<{
        modifiedBy: Types.ObjectId;
        timestamp: Date;
        changes: {
            field: string;
            oldValue: any;
            newValue: any;
        };
    }>;

    // Void tracking
    voidedBy?: Types.ObjectId;
    voidedAt?: Date;
    voidReason?: string;

    // Additional info
    receiptNumber: string;  // Unique receipt number
    notes?: string;
    attachments?: string[];  // URLs to scanned receipts or documents
}

// DTO for creating a new payment
export interface CreatePaymentDTO {
    studentId: string;
    amount: number;
    method: PaymentMethod;
    date: Date;
    nextDueDate?: Date;
    description: string;
    period?: PaymentPeriod;
    notes?: string;
    attachments?: string[];
}

// DTO for updating a payment
export interface UpdatePaymentDTO {
    amount?: number;
    method?: PaymentMethod;
    date?: Date;
    nextDueDate?: Date;
    description?: string;
    notes?: string;
    attachments?: string[];
}

// DTO for voiding a payment
export interface VoidPaymentDTO {
    reason: string;
    notes?: string;
}

// Query options for fetching payments
export interface PaymentQueryOptions {
    page?: number;
    limit?: number;
    sortBy?: 'date' | 'amount' | 'nextDueDate';
    sortOrder?: 'asc' | 'desc';
    status?: PaymentStatus;
    method?: PaymentMethod;
    studentId?: string;
    dateRange?: {
        start: Date;
        end: Date;
    };
    amountRange?: {
        min: number;
        max: number;
    };
    dueDateRange?: {
        start: Date;
        end: Date;
    };
    period?: PaymentPeriod;
    search?: string;  // For searching in description/notes
}

// Response DTOs
export interface PaymentResponseDTO {
    id: string;
    student: {
        id: string;
        name: string;
    };
    amount: number;
    remainingBalance: number;
    status: PaymentStatus;
    method: PaymentMethod;
    date: Date;
    nextDueDate?: Date;
    description: string;
    period?: PaymentPeriod;
    recordedBy: {
        id: string;
        username: string;
    };
    recordedAt: Date;
    modifiedBy?: {
        id: string;
        username: string;
    };
    modifiedAt?: Date;
    voidedBy?: {
        id: string;
        username: string;
    };
    voidedAt?: Date;
    voidReason?: string;
    receiptNumber: string;
    notes?: string;
    attachments?: string[];
}

// Payment summary types
export interface StudentPaymentSummary {
    totalPaid: number;
    totalDue: number;
    lastPaymentDate?: Date;
    nextDueDate?: Date;
    paymentStatus: 'up_to_date' | 'pending' | 'overdue';
    recentPayments: PaymentResponseDTO[];
    upcomingPayments: {
        dueDate: Date;
        expectedAmount: number;
    }[];
}

// Export options
export interface PaymentExportOptions {
    format: 'csv' | 'json';
    dateRange?: {
        start: Date;
        end: Date;
    };
    includeVoided?: boolean;
    groupBy?: 'student' | 'date' | 'method' | 'period';
    fields?: string[];
}

// Error types
export interface PaymentValidationError {
    field: string;
    message: string;
    code: string;
}

// Event types for payment-related notifications
export interface PaymentEvent {
    type: 'created' | 'updated' | 'voided';
    paymentId: string;
    studentId: string;
    timestamp: Date;
    details: any;
    notifyUsers: string[];
}

export interface PaymentStatistics {
    totalAmount: number;
    totalCount: number;
    averageAmount: number;
    groups: Array<{
        key: string;
        count: number;
        amount: number;
        percentage: number;
    }>;
    dateRange: {
        start: Date;
        end: Date;
    };
}
