// server/src/routes/attendance.routes.ts
import express from 'express';
import { AttendanceController } from '../controllers/attendance.controller';
import { authenticateToken, authorizeRoles } from '../middleware/auth.middleware';
import { validate } from '../middleware/validate.middleware';
import { attendanceValidation } from '../validations/attendance.validation';
import { catchAsync } from '../middleware/error.middleware';
import { apiLimiter } from '../middleware/rateLimiter.middleware';
import { AttendanceMiddleware } from '../middleware/attendance.middleware';

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Get attendance records with filtering
router.get(
    '/',
    authorizeRoles('superAdmin', 'manager', 'secretary', 'teacher'),
    validate(attendanceValidation.getAttendanceQuery),
    catchAsync(AttendanceController.getAttendanceRecords)
);

// Mark attendance for a single student
// Mark attendance for a single student
router.post(
    '/classes/:classId',
    authorizeRoles('superAdmin', 'manager', 'teacher'),
    validate(attendanceValidation.markAttendance),
    AttendanceMiddleware.validateAttendanceDate,
    AttendanceMiddleware.validateClassSchedule,
    AttendanceMiddleware.validateTeacherPermission,
    AttendanceMiddleware.validateStudentsInClass,
    AttendanceMiddleware.validateDuplicateAttendance,
    AttendanceMiddleware.validateExcuse,
    AttendanceMiddleware.validateLateArrival,
    catchAsync(AttendanceController.markAttendance)
);

// Bulk mark attendance
router.post(
    '/classes/:classId/bulk',
    authorizeRoles('superAdmin', 'manager', 'teacher'),
    validate(attendanceValidation.bulkMarkAttendance),
    AttendanceMiddleware.validateAttendanceDate,
    AttendanceMiddleware.validateClassSchedule,
    AttendanceMiddleware.validateTeacherPermission,
    AttendanceMiddleware.validateStudentsInClass,
    AttendanceMiddleware.validateDuplicateAttendance,
    AttendanceMiddleware.validateLateArrival,
    catchAsync(AttendanceController.bulkMarkAttendance)
);

// // Bulk mark attendance for multiple students
// router.post(
//     '/classes/:classId/bulk',
//     authorizeRoles('superAdmin', 'manager', 'teacher'),
//     validate(attendanceValidation.bulkMarkAttendance),
//     catchAsync(AttendanceController.bulkMarkAttendance)
// );

// Add excuse for absence
router.post(
    '/classes/:classId/excuse',
    authorizeRoles('superAdmin', 'manager', 'secretary'),
    validate(attendanceValidation.addExcuse),
    catchAsync(AttendanceController.addExcuse)
);

// Verify excuse
router.post(
    '/classes/:classId/students/:studentId/verify-excuse',
    authorizeRoles('superAdmin', 'manager'),
    validate(attendanceValidation.verifyExcuse),
    catchAsync(AttendanceController.verifyExcuse)
);

// Get student attendance statistics
router.get(
    '/students/:studentId/stats',
    authorizeRoles('superAdmin', 'manager', 'secretary', 'teacher'),
    validate(attendanceValidation.getAttendanceStats),
    catchAsync(AttendanceController.getStudentAttendanceStats)
);

// Get class attendance statistics
router.get(
    '/classes/:classId/stats',
    authorizeRoles('superAdmin', 'manager', 'teacher'),
    validate(attendanceValidation.getAttendanceStats),
    catchAsync(AttendanceController.getClassAttendanceStats)
);

// Export attendance data
router.get(
    '/export',
    authorizeRoles('superAdmin', 'manager'),
    validate(attendanceValidation.exportAttendance),
    catchAsync(AttendanceController.exportAttendance)
);

// Get unified attendance for a student
router.get(
    '/students/:studentId/unified',
    authorizeRoles('superAdmin', 'manager', 'secretary', 'teacher'),
    validate(attendanceValidation.getUnifiedAttendance),
    catchAsync(AttendanceController.getUnifiedStudentAttendance)
);

// Apply rate limiting to all routes
router.use(apiLimiter);

export default router;