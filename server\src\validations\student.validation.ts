// server/src/validations/student.validation.ts
import Joi from 'joi';

export const studentValidation = {
    // GET /api/students query parameters
    getStudentsQuery: Joi.object({
        page: Joi.number().min(1),
        limit: Joi.number().min(1).max(100),
        sortBy: Joi.string().valid(
            'name',
            'registeredAt',
            'currentLevel',
            'status'
        ),
        sortOrder: Joi.string().valid('asc', 'desc'),
        status: Joi.string().valid('active', 'inactive'),
        level: Joi.string(),
        search: Joi.string().min(1).max(50),
        classId: Joi.string().pattern(/^[0-9a-fA-F]{24}$/),
        fromDate: Joi.date().iso(),
        toDate: Joi.date().iso().min(Joi.ref('fromDate')),
        paymentStatus: Joi.string().valid('paid', 'pending', 'overdue')
    }),

    // POST /api/students
    createStudent: Joi.object({
        name: Joi.string()
            .min(2)
            .max(100)
            .required()
            .messages({
                'string.min': 'Name must be at least 2 characters long',
                'string.max': 'Name cannot exceed 100 characters',
                'any.required': 'Name is required'
            }),
        contactInfo: Joi.object({
            phone: Joi.string()
                .pattern(/^\+?[\d\s-]+$/)
                .required()
                .messages({
                    'string.pattern.base': 'Invalid phone number format',
                    'any.required': 'Phone number is required'
                }),
            email: Joi.string()
                .email()
                .required()
                .messages({
                    'string.email': 'Invalid email format',
                    'any.required': 'Email is required'
                }),
            address: Joi.string()
                .min(5)
                .max(200)
                .required()
                .messages({
                    'string.min': 'Address must be at least 5 characters long',
                    'string.max': 'Address cannot exceed 200 characters',
                    'any.required': 'Address is required'
                })
        }).required(),
        // Make currentLevel conditional based on whether currentClass is provided
        currentLevel: Joi.alternatives().conditional('currentClass', {
            is: Joi.exist(), // If currentClass is provided
            then: Joi.string().optional(), // currentLevel becomes optional
            otherwise: Joi.string().required() // currentLevel is required if no class
        }).messages({
            'any.required': 'Current level is required when no class is assigned'
        }),
        currentClass: Joi.string()
            .pattern(/^[0-9a-fA-F]{24}$/)
            .optional()
            .messages({
                'string.pattern.base': 'Invalid class ID format'
            })
    }),

    // PATCH /api/students/:id
    updateStudent: Joi.object({
        name: Joi.string()
            .min(2)
            .max(100),
        contactInfo: Joi.object({
            phone: Joi.string()
                .pattern(/^\+?[\d\s-]+$/),
            email: Joi.string()
                .email(),
            address: Joi.string()
                .min(5)
                .max(200)
        }),
        currentLevel: Joi.string(),
        status: Joi.string()
            .valid('active', 'inactive')
    }).min(1),

    // POST /api/students/:id/payments
    recordPayment: Joi.object({
        amount: Joi.number()
            .positive()
            .required()
            .messages({
                'number.positive': 'Amount must be positive',
                'any.required': 'Amount is required'
            }),
        date: Joi.date()
            .max('now')
            .required()
            .messages({
                'date.max': 'Payment date cannot be in the future',
                'any.required': 'Payment date is required'
            }),
        nextDueDate: Joi.date()
            .greater('now')
            .required()
            .messages({
                'date.greater': 'Next due date must be in the future',
                'any.required': 'Next due date is required'
            }),
        notes: Joi.string()
            .max(500)
            .messages({
                'string.max': 'Notes cannot exceed 500 characters'
            })
    }),

    // POST /api/students/:id/transfer
    transferClass: Joi.object({
        toClass: Joi.string()
            .pattern(/^[0-9a-fA-F]{24}$/)
            .required()
            .messages({
                'string.pattern.base': 'Invalid class ID format',
                'any.required': 'Target class ID is required'
            }),
        reason: Joi.string()
            .min(10)
            .max(500)
            .required()
            .messages({
                'string.min': 'Reason must be at least 10 characters long',
                'string.max': 'Reason cannot exceed 500 characters',
                'any.required': 'Reason for transfer is required'
            }),
        transferDate: Joi.date()
            .min('now')
            .messages({
                'date.min': 'Transfer date cannot be in the past'
            })
    }),

    // POST /api/students/bulk
    bulkOperation: Joi.object({
        studentIds: Joi.array()
            .items(
                Joi.string()
                    .pattern(/^[0-9a-fA-F]{24}$/)
                    .messages({
                        'string.pattern.base': 'Invalid student ID format'
                    })
            )
            .min(1)
            .max(50)
            .unique()
            .required()
            .messages({
                'array.min': 'At least one student ID must be provided',
                'array.max': 'Cannot process more than 50 students at once',
                'array.unique': 'Duplicate student IDs are not allowed',
                'any.required': 'Student IDs are required'
            }),
        operation: Joi.string()
            .valid('activate', 'deactivate', 'changeLevel', 'assignClass')
            .required()
            .messages({
                'any.only': 'Invalid operation specified',
                'any.required': 'Operation type is required'
            }),
        reason: Joi.string()
            .min(10)
            .max(500)
            .required()
            .messages({
                'string.min': 'Reason must be at least 10 characters long',
                'string.max': 'Reason cannot exceed 500 characters',
                'any.required': 'Reason is required'
            }),
        newValue: Joi.when('operation', {
            is: Joi.string().valid('changeLevel', 'assignClass'),
            then: Joi.string().required(),
            otherwise: Joi.forbidden()
        }).messages({
            'any.required': 'New value is required for level change or class assignment'
        })
    }),

    // GET /api/students/export
    exportStudents: Joi.object({
        format: Joi.string()
            .valid('csv', 'json')
            .default('csv')
            .messages({
                'any.only': 'Export format must be either csv or json'
            }),
        fields: Joi.string()
            .pattern(/^[a-zA-Z_]+(,[a-zA-Z_]+)*$/)
            .messages({
                'string.pattern.base': 'Invalid fields format. Use comma-separated field names'
            }),
        includePaymentHistory: Joi.boolean(),
        includeClassHistory: Joi.boolean(),
        fromDate: Joi.date().iso(),
        toDate: Joi.date().iso().min(Joi.ref('fromDate'))
    })
};

export default studentValidation;