
import { useParams, useNavigate } from "react-router-dom";
import MainLayout from "@/components/layout/MainLayout";
import PaymentForm from "@/components/payments/PaymentForm";
import { Button } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";

export default function PaymentCreate() {
  const { studentId } = useParams<{ studentId?: string }>();
  const navigate = useNavigate();

  const handlePaymentSuccess = (paymentId: string) => {
    navigate(`/payments/${paymentId}`);
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header with navigation */}
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => navigate(-1)}
          >
            <ChevronLeft size={18} />
          </Button>
          <h1 className="text-2xl font-bold">Record Payment</h1>
        </div>

        <PaymentForm
          studentId={studentId}
          onSuccess={handlePaymentSuccess}
        />
      </div>
    </MainLayout>
  );
}
