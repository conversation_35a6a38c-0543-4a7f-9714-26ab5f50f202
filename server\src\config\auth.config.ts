// server/src/config/auth.config.ts
import { SignOptions } from 'jsonwebtoken';

export const AUTH_CONFIG = {
    JWT_SECRET: process.env.JWT_SECRET || 'your-secret-key',
    JWT_EXPIRATION: '24h' as SignOptions['expiresIn'],
    PASSWORD_SALT_ROUNDS: 12,
    MAX_LOGIN_ATTEMPTS: 5,
    LOGIN_ATTEMPT_WINDOW: 15 * 60 * 1000, // 15 minutes
    PASSWORD_REQUIREMENTS: {
        minLength: 8,
        requireUppercase: true,
        requireLowercase: true,
        requireNumbers: true,
        requireSpecialChars: true
    }
} as const;