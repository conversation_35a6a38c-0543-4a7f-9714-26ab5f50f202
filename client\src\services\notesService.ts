
// Mock function for fetching notes summary
export const fetchNotesSummary = async () => {
  // Simulate API call delay
  await new <PERSON>(resolve => setTimeout(resolve, 1200));
  
  return {
    totalNotes: 45,
    recentNotes: {
      student: [
        {
          id: "1",
          title: "Academic progress - <PERSON>",
          content: "Student is showing excellent progress in written assignments but needs more practice with speaking.",
          createdBy: {
            id: "t1",
            name: "<PERSON>",
            role: "Teacher"
          },
          createdAt: new Date().toISOString(),
          type: "academic",
          visibility: "teachers",
          student: {
            id: "s101",
            name: "<PERSON>"
          },
          relatedClass: {
            id: "c1",
            name: "English 101"
          },
          attachments: []
        },
        {
          id: "2",
          title: "Payment reminder - <PERSON>",
          content: "Outstanding balance for the current semester needs to be paid by end of month.",
          createdBy: {
            id: "s1",
            name: "<PERSON>",
            role: "Secretary"
          },
          createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(),
          type: "administrative",
          visibility: "all",
          student: {
            id: "s202",
            name: "<PERSON>"
          },
          attachments: []
        }
      ],
      class: [
        {
          id: "3",
          title: "Schedule change - Math 202",
          content: "Friday's class will be moved to room 204 due to maintenance.",
          createdBy: {
            id: "m1",
            name: "<PERSON> <PERSON>",
            role: "Manager"
          },
          createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(),
          type: "announcement",
          visibility: "all",
          relatedClass: {
            id: "c2",
            name: "Math 202"
          },
          attachments: []
        },
        {
          id: "4",
          title: "Mid-term progress - English 101",
          content: "The class is making good progress overall. We need to focus more on pronunciation next week.",
          createdBy: {
            id: "t1",
            name: "John Smith",
            role: "Teacher"
          },
          createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(),
          type: "general",
          visibility: "teachers",
          relatedClass: {
            id: "c1",
            name: "English 101"
          },
          attachments: []
        }
      ]
    },
    topStudents: [
      { id: "s101", name: "Mike Brown", noteCount: 8 },
      { id: "s202", name: "Jane Wilson", noteCount: 6 },
      { id: "s303", name: "Alex Smith", noteCount: 5 },
      { id: "s404", name: "Lisa Johnson", noteCount: 4 },
      { id: "s505", name: "Tom Davis", noteCount: 3 }
    ],
    topClasses: [
      { id: "c1", name: "English 101", noteCount: 12 },
      { id: "c2", name: "Math 202", noteCount: 9 },
      { id: "c3", name: "History 301", noteCount: 8 },
      { id: "c4", name: "Science 203", noteCount: 7 },
      { id: "c5", name: "Spanish 101", noteCount: 5 }
    ],
    activityByDate: [
      { date: "2023-06-01", count: 5 },
      { date: "2023-06-02", count: 3 },
      { date: "2023-06-03", count: 7 },
      { date: "2023-06-04", count: 2 },
      { date: "2023-06-05", count: 4 },
      { date: "2023-06-06", count: 6 },
      { date: "2023-06-07", count: 8 }
    ]
  };
};

// Mock function for fetching notes
export const fetchNotes = async (filters = {}) => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 800));
  
  // Mock data - in a real app, this would filter based on provided filters
  const notes = [
    {
      id: "1",
      title: "Academic progress - Mike Brown",
      content: "Student is showing excellent progress in written assignments but needs more practice with speaking.",
      createdBy: {
        id: "t1",
        name: "John Smith",
        role: "Teacher"
      },
      createdAt: new Date().toISOString(),
      type: "academic",
      visibility: "teachers",
      student: {
        id: "s101",
        name: "Mike Brown"
      },
      relatedClass: {
        id: "c1",
        name: "English 101"
      },
      attachments: []
    },
    // Additional notes would be here
  ];
  
  return {
    notes,
    pagination: {
      total: notes.length,
      page: 1,
      limit: 10,
      totalPages: 1
    }
  };
};

// Mock function for creating a note
export const createNote = async (noteData) => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // Generate an ID for the new note
  const newNote = {
    id: `note-${Date.now()}`,
    createdAt: new Date().toISOString(),
    ...noteData
  };
  
  return newNote;
};

// Mock function for updating a note
export const updateNote = async (noteId, updateData) => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // In a real app, this would find and update the note in the database
  const updatedNote = {
    id: noteId,
    ...updateData,
    updatedAt: new Date().toISOString()
  };
  
  return updatedNote;
};

// Mock function for deleting a note
export const deleteNote = async (noteId) => {
  // Simulate API call delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // In a real app, this would delete the note from the database
  return { success: true, message: "Note deleted successfully" };
};
