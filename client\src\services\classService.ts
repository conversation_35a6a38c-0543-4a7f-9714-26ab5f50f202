﻿import { Class, ClassesApiResponse, ClassFilter, ClassApiResponse } from "@/types/class";

export const fetchClasses = async (filters: Partial<ClassFilter> = {}): Promise<ClassesApiResponse> => {
  try {
    const params = new URLSearchParams();
    if (filters.search) params.append('search', filters.search);
    if (filters.status) params.append('status', filters.status);
    if (filters.level) params.append('level', filters.level);
    if (filters.teacherId) params.append('teacherId', filters.teacherId);
    if (filters.page) params.append('page', filters.page.toString());
    if (filters.limit) params.append('limit', filters.limit.toString());

    const response = await fetch(`http://localhost:3000/api/classes?${params}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      data: data.data || [],
      pagination: data.pagination || {
        total: 0,
        page: 1,
        limit: 10,
        pages: 0
      }
    };
  } catch (error) {
    console.error('Error fetching classes:', error);
    return {
      success: false,
      data: [],
      pagination: {
        total: 0,
        page: 1,
        limit: 10,
        pages: 0
      }
    };
  }
};

export const fetchClassById = async (classId: string): Promise<ClassApiResponse> => {
  try {
    const response = await fetch(`http://localhost:3000/api/classes/${classId}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      data: data.data || data
    };
  } catch (error) {
    console.error("Error fetching class:", error);
    throw error;
  }
};

export const createClass = async (classData: Partial<Class>): Promise<ClassApiResponse> => {
  try {
    const response = await fetch('http://localhost:3000/api/classes', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(classData),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      data: data.data || data
    };
  } catch (error) {
    console.error("Error creating class:", error);
    throw error;
  }
};

export const updateClass = async (classId: string, classData: Partial<Class>): Promise<ClassApiResponse> => {
  try {
    const response = await fetch(`http://localhost:3000/api/classes/${classId}`, {
      method: 'PUT',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(classData),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return {
      success: true,
      data: data.data || data
    };
  } catch (error) {
    console.error("Error updating class:", error);
    throw error;
  }
};

/**
 * Get available levels for filtering
 */
export const getAvailableLevels = async (): Promise<string[]> => {
  try {
    const response = await fetch('http://localhost:3000/api/classes/levels', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data.data || [];
  } catch (error) {
    console.error("Error fetching levels:", error);
    return [];
  }
};

/**
 * Get available rooms for filtering
 */
export const getAvailableRooms = async (): Promise<string[]> => {
  try {
    const response = await fetch('http://localhost:3000/api/classes/rooms', {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data.data || [];
  } catch (error) {
    console.error("Error fetching rooms:", error);
    return [];
  }
};
