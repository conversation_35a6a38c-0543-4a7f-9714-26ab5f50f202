// server/src/middleware/validate.middleware.ts
import { Request, Response, NextFunction } from 'express';
import <PERSON><PERSON> from 'joi';
import { AppError } from '../types/error.types';

export const validate = (schema: Joi.ObjectSchema) => {
    return (req: Request, res: Response, next: NextFunction): void => {
        try {
            const { error } = schema.validate({
                body: req.body,
                query: req.query,
                params: req.params
            }, {
                abortEarly: false,
                stripUnknown: true
            });

            if (error) {
                const errorMessage = error.details
                    .map((detail) => detail.message)
                    .join(', ');
                throw new AppError(400, errorMessage);
            }

            next();
        } catch (err) {
            next(err);
        }
    };
};