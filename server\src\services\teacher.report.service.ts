// server/src/services/teacher.report.service.ts
import mongoose from 'mongoose';
import { AppError } from '../types/error.types';
import { SystemLogger } from './logger.service';
import { Attendance } from '../models/attendance.model';
import { Note } from '../models/note.model';
import { Student } from '../models/student.model';
import { Class } from '../models/class.model';
import { ReportTemplate } from '../models/teacher.report-template.model';
import { ReportDateRange, TeacherReportRequestDTO } from '../types/teacher.report.types';
import Papa from 'papaparse';
import ExcelJS from 'exceljs';
import PDFDocument from 'pdfkit';

interface StudentProgressReport {
    studentName: string;
    period: ReportDateRange;
    attendanceSummary: {
        totalClasses: number;
        attended: number;
        attendanceRate: number;
    };
    notes?: Array<{
        createdAt: Date;
        type: string;
        content: string;
    }>;
}

interface NotesByStudent {
    [key: string]: {
        studentName: string;
        notes: Array<{
            date: Date;
            content: string;
        }>;
    };
}

interface MakeupClassQuery {
    isMakeupClass: boolean;
    teacherId: mongoose.Types.ObjectId;
    date: {
        $gte: Date;
        $lte: Date;
    };
    classId?: mongoose.Types.ObjectId;
}

type ReportFormat = 'json' | 'csv' | 'excel' | 'pdf';

interface FormatConfig {
    data: Buffer | string;
    contentType: string;
    ext: string;
}

export class ReportService {
    static async generateTeacherReport(
        request: TeacherReportRequestDTO,
        teacherId: string
    ): Promise<{ data: Buffer | string; contentType: string; filename: string }> {
        try {
            await this.validateTeacherAccess(request, teacherId);

            // Get template if specified
            const template = request.templateId ? 
                await ReportTemplate.findById(request.templateId) : null;

            // Generate report based on type
            const reportData = await this.generateReportData(request, teacherId);

            // Format and return the report
            const { data, contentType, filename } = await this.formatReport(
                reportData, 
                request.format,
                template
            );

            return { data, contentType, filename };
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error generating report');
        }
    }

    private static async generateReportData(request: TeacherReportRequestDTO, teacherId: string): Promise<any> {
        switch (request.type) {
            case 'class_attendance':
                return this.generateClassAttendanceReport(request);
            case 'student_progress':
                return this.generateStudentProgressReport(request);
            case 'class_behavior':
                return this.generateClassBehaviorReport(request);
            case 'makeup_classes':
                return this.generateMakeupClassesReport(request, teacherId);
            default:
                throw new AppError(400, 'Unsupported report type');
        }
    }

    private static async generateClassAttendanceReport(request: TeacherReportRequestDTO) {
        if (!request.classId) throw new AppError(400, 'Class ID is required');

        const [classDoc, attendanceRecords] = await Promise.all([
            Class.findById(request.classId),
            Attendance.find({
                classId: new mongoose.Types.ObjectId(request.classId),
                date: { $gte: request.dateRange.startDate, $lte: request.dateRange.endDate }
            }).populate('students.studentId', 'name')
        ]);

        if (!classDoc) throw new AppError(404, 'Class not found');

        const stats = { present: 0, absent: 0, late: 0, excused: 0 };
        const records = attendanceRecords.flatMap(record =>
            record.students.map(student => {
                stats[student.status]++;
                return {
                    date: record.date,
                    status: student.status,
                    studentName: (student.studentId as any).name,
                    excuse: student.excuse?.reason,
                    notes: student.notes
                };
            })
        );

        const totalRecords = Object.values(stats).reduce((a, b) => a + b, 0);

        return {
            className: classDoc.name,
            period: request.dateRange,
            totalClasses: attendanceRecords.length,
            attendanceRecords: records,
            summary: {
                ...stats,
                attendanceRate: totalRecords > 0 ? 
                    ((stats.present + stats.late) / totalRecords) * 100 : 0
            }
        };
    }

    private static async generateStudentProgressReport(request: TeacherReportRequestDTO) {
        if (!request.studentId) throw new AppError(400, 'Student ID is required');

        const [student, attendanceRecords] = await Promise.all([
            Student.findById(request.studentId),
            Attendance.find({
                'students.studentId': new mongoose.Types.ObjectId(request.studentId),
                date: { $gte: request.dateRange.startDate, $lte: request.dateRange.endDate }
            })
        ]);

        if (!student) throw new AppError(404, 'Student not found');

        const attended = attendanceRecords.reduce((count, record) => {
            const studentAttendance = record.students.find(
                s => s.studentId.toString() === request.studentId
            );
            return count + (['present', 'late'].includes(studentAttendance?.status || '') ? 1 : 0);
        }, 0);

        const report: StudentProgressReport = {
            studentName: student.name,
            period: request.dateRange,
            attendanceSummary: {
                totalClasses: attendanceRecords.length,
                attended,
                attendanceRate: attendanceRecords.length > 0 ?
                    (attended / attendanceRecords.length) * 100 : 0
            }
        };

        if (request.includeNotes) {
            report['notes'] = await Note.find({
                studentId: student._id,
                type: { $in: request.filters?.noteTypes || ['behavioral', 'academic'] },
                createdAt: { $gte: request.dateRange.startDate, $lte: request.dateRange.endDate }
            }).sort({ createdAt: -1 });
        }

        return report;
    }

    private static async generateClassBehaviorReport(request: TeacherReportRequestDTO) {
        if (!request.classId) throw new AppError(400, 'Class ID is required');

        const notes = await Note.find({
            classId: new mongoose.Types.ObjectId(request.classId),
            type: 'behavioral',
            createdAt: { $gte: request.dateRange.startDate, $lte: request.dateRange.endDate }
        }).populate('studentId', 'name');

        const notesByStudent = notes.reduce((acc: NotesByStudent, note) => {
            const studentId = note.studentId?.toString();
            if (!studentId) return acc;
        
            acc[studentId] = acc[studentId] || {
                studentName: (note.studentId as any)?.name || 'Unknown',
                notes: []
            };
            acc[studentId].notes.push({
                date: note.createdAt,
                content: note.content
            });
            return acc;
        }, {} as NotesByStudent);

        return {
            classId: request.classId,
            period: request.dateRange,
            behaviorNotes: Object.values(notesByStudent)
        };
    }

    private static async generateMakeupClassesReport(request: TeacherReportRequestDTO, teacherId: string) {
        const query: MakeupClassQuery = {
            isMakeupClass: true,
            teacherId: new mongoose.Types.ObjectId(teacherId),
            date: { 
                $gte: request.dateRange.startDate, 
                $lte: request.dateRange.endDate 
            }
        };
        
        if (request.classId) {
            query.classId = new mongoose.Types.ObjectId(request.classId);
        }

        const makeupClasses = await Attendance.find(query)
            .populate('students.studentId', 'name')
            .populate('classId', 'name');

        return {
            period: request.dateRange,
            makeupClasses: makeupClasses.map(makeup => ({
                date: makeup.date,
                className: (makeup.classId as any).name,
                students: makeup.students.map(student => ({
                    studentName: (student.studentId as any).name,
                    status: student.status,
                    notes: student.notes
                }))
            }))
        };
    }

    private static async validateTeacherAccess(request: TeacherReportRequestDTO, teacherId: string): Promise<void> {
        const checks = [];

        if (request.classId) {
            checks.push(Class.findOne({
                _id: new mongoose.Types.ObjectId(request.classId),
                'teachers.teacherId': new mongoose.Types.ObjectId(teacherId)
            }));
        }

        if (request.studentId) {
            checks.push(Student.findOne({
                _id: new mongoose.Types.ObjectId(request.studentId),
                currentClass: {
                    $in: await this.getTeacherClassIds(teacherId)
                }
            }));
        }

        const results = await Promise.all(checks);
        if (checks.length > 0 && results.every(r => !r)) {
            throw new AppError(403, 'Not authorized to access this data');
        }
    }

    private static async getTeacherClassIds(teacherId: string): Promise<mongoose.Types.ObjectId[]> {
        const classes = await Class.find({
            'teachers.teacherId': new mongoose.Types.ObjectId(teacherId)
        });
        return classes.map(c => c._id);
    }

    private static async formatReport(
        data: any,
        format: string,
        template?: any
    ): Promise<{ data: Buffer | string; contentType: string; filename: string }> {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const formats: Record<ReportFormat, FormatConfig> = {
            json: {
                data: JSON.stringify(data, null, 2),
                contentType: 'application/json',
                ext: 'json'
            },
            csv: {
                data: Papa.unparse(this.flattenData(data)),
                contentType: 'text/csv',
                ext: 'csv'
            },
            excel: {
                data: await this.createExcel(data, template),
                contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                ext: 'xlsx'
            },
            pdf: {
                data: await this.createPDF(data, template),
                contentType: 'application/pdf',
                ext: 'pdf'
            }
        };

        const { data: formatted, contentType, ext } = formats[format as ReportFormat] ?? formats.json;
        return {
            data: formatted,
            contentType,
            filename: `report_${timestamp}.${ext}`
        };
    }

    private static flattenData(data: any, prefix = ''): any {
        return Object.keys(data).reduce((acc: any, key: string) => {
            const value = data[key];
            const newKey = prefix ? `${prefix}.${key}` : key;

            if (value && typeof value === 'object' && !Array.isArray(value)) {
                Object.assign(acc, this.flattenData(value, newKey));
            } else if (Array.isArray(value)) {
                acc[newKey] = value.map(item => 
                    typeof item === 'object' ? JSON.stringify(item) : item
                ).join('; ');
            } else {
                acc[newKey] = value;
            }
            return acc;
        }, {});
    }

    private static async createExcel(data: any, template?: any): Promise<Buffer> {
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Report');

        if (template?.config?.header?.title) {
            worksheet.addRow([template.config.header.title]).font = { bold: true, size: 14 };
            worksheet.addRow([]);
        }

        const flattened = this.flattenData(data);
        worksheet.addRow(Object.keys(flattened));
        worksheet.addRow(Object.values(flattened));

        return workbook.xlsx.writeBuffer() as Promise<Buffer>;
    }

    private static createPDF(data: any, template?: any): Promise<Buffer> {
        return new Promise((resolve, reject) => {
            const chunks: Buffer[] = [];
            const doc = new PDFDocument({ margin: 50, size: 'A4' });

            doc.on('data', chunk => chunks.push(chunk));
            doc.on('end', () => resolve(Buffer.concat(chunks)));
            doc.on('error', reject);

            if (template?.config?.header?.title) {
                doc.fontSize(16)
                   .font('Helvetica-Bold')
                   .text(template.config.header.title, { align: 'center' })
                   .moveDown();
            }

            this.addPDFContent(doc, data);
            doc.end();
        });
    }

    private static addPDFContent(doc: PDFKit.PDFDocument, data: any, level = 0): void {
        Object.entries(data).forEach(([key, value]) => {
            const fontSize = Math.max(10, 14 - level);
            doc.fontSize(fontSize)
               .font(level === 0 ? 'Helvetica-Bold' : 'Helvetica')
               .text(key + ':', { continued: typeof value !== 'object' });

            if (typeof value !== 'object') {
                doc.text(` ${value}`);
            } else {
                doc.moveDown(0.5);
                this.addPDFContent(doc, value, level + 1);
            }
        });
        doc.moveDown();
    }

    // Template Management
    static async saveReportTemplate(templateConfig: any, userId: string): Promise<any> {
        return ReportTemplate.create({
            ...templateConfig,
            createdBy: new mongoose.Types.ObjectId(userId)
        });
    }

    static async getReportTemplates(userId: string): Promise<any> {
        return ReportTemplate.find({
            $or: [
                { createdBy: new mongoose.Types.ObjectId(userId) },
                { isDefault: true }
            ]
        });
    }

    static async deleteReportTemplate(templateId: string, userId: string): Promise<void> {
        const template = await ReportTemplate.findOne({
            _id: new mongoose.Types.ObjectId(templateId),
            createdBy: new mongoose.Types.ObjectId(userId)
        });

        if (!template) {
            throw new AppError(404, 'Template not found or unauthorized');
        }

        if (template.isDefault) {
            throw new AppError(400, 'Cannot delete default templates');
        }

        await template.deleteOne();
    }

    static async generateReportPreview(
        request: TeacherReportRequestDTO,
        teacherId: string
    ): Promise<any> {
        await this.validateTeacherAccess(request, teacherId);
        return this.generateReportData(request, teacherId);
    }
}