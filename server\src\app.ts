import express from 'express';
import cors from 'cors';
import { CORS_CONFIG } from './config/cors.config';
import { DatabaseService } from './services/database.service';
import { errorHandler } from './middleware/error.middleware';
import { apiLimiter } from './middleware/rateLimiter.middleware';
import { authenticateToken } from './middleware/auth.middleware';

// Route imports
import authRoutes from './routes/auth.routes';
import userRoutes from './routes/user.routes';
import studentRoutes from './routes/student.routes';
import classRoutes from './routes/class.routes';
import attendanceRoutes from './routes/attendance.routes';
import noteRoutes from './routes/note.routes';
import paymentRoutes from './routes/payment.routes';
// import systemRoutes from './routes/system.routes';
import reportRoutes from './routes/report.routes';
import roomRoutes from './routes/room.routes';
import secretaryReportRoutes from './routes/secretary.report.routes';
import systemLogsRoutes from './routes/system.logs.routes';
import dashboardRoutes from './routes/dashboard.routes';
import scheduleRoutes from './routes/schedule.routes';


const app = express();

// CORS configuration
app.use(
  cors({
    origin: (origin, callback) => {
      if (!origin) return callback(null, true);

      const isAllowed = CORS_CONFIG.ALLOWED_ORIGINS.some(allowed => {
        if (allowed instanceof RegExp) {
          return allowed.test(origin);
        }
        return allowed === origin;
      });

      if (isAllowed) {
        callback(null, true);
      } else {
        callback(new Error('CORS policy violation'));
      }
    },
    methods: CORS_CONFIG.ALLOWED_METHODS,
    allowedHeaders: CORS_CONFIG.ALLOWED_HEADERS,
    exposedHeaders: CORS_CONFIG.EXPOSE_HEADERS,
    maxAge: CORS_CONFIG.MAX_AGE,
    credentials: CORS_CONFIG.CREDENTIALS,
  })
);

// Body parser middleware
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'OK', timestamp: new Date().toISOString() });
});

// API routes with rate limiting
app.use('/api', apiLimiter);

// Public routes (no authentication required)
app.use('/api/auth', authRoutes);

// Protected routes (authentication required)
app.use('/api/users', authenticateToken, userRoutes);
app.use('/api/students', authenticateToken, studentRoutes);
app.use('/api/classes', authenticateToken, classRoutes);
app.use('/api/attendance', authenticateToken, attendanceRoutes);
app.use('/api/notes', authenticateToken, noteRoutes);
app.use('/api/payments', authenticateToken, paymentRoutes);
// app.use('/api/system', authenticateToken, systemRoutes);
app.use('/api/system/logs', systemLogsRoutes);
app.use('/api/reports', authenticateToken, reportRoutes);
app.use('/api/rooms', authenticateToken, roomRoutes);
app.use('/api/reports/secretary', secretaryReportRoutes);
app.use('/api/dashboard', authenticateToken, dashboardRoutes);
app.use('/api/schedule', authenticateToken, scheduleRoutes);

// Error handling middleware (should be last)
app.use(errorHandler);

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught Exception:', error);
  // Perform graceful shutdown
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
  // Perform graceful shutdown
  process.exit(1);
});

// Start the server only after a successful database connection
const PORT = process.env.PORT || 3000;
const dbService = DatabaseService.getInstance();

dbService
  .connect()
  .then(() => {
    console.log('Database initialized successfully');
    const server = app.listen(PORT, () => {
      console.log(`Server is running on port ${PORT}`);
    });

    // Graceful shutdown
    process.on('SIGTERM', () => {
      console.log('SIGTERM signal received. Closing HTTP server...');
      server.close(() => {
        console.log('HTTP server closed');
        dbService.disconnect().then(() => {
          console.log('Database connection closed');
          process.exit(0);
        });
      });
    });
  })
  .catch((error) => {
    console.error('Failed to initialize database:', error);
    process.exit(1);
  });

export default app;