
import { useState } from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import InteractiveChart from "../InteractiveChart";
import DashboardSection from "../DashboardSection";
import MetricCard from "../MetricCard";
import ActivityFeed from "../ActivityFeed";
import { 
  Users, 
  BookOpen, 
  DollarSign, 
  Home, 
  BarChart2,
  Percent as PercentIcon,
  Clock as ClockIcon,
  AlertTriangle as AlertTriangleIcon,
} from "lucide-react";
import { DashboardData } from "@/services/dashboardService";

interface SuperAdminDashboardProps {
  dashboardData: Partial<DashboardData>;
  isLoadingData: boolean;
  activeTab: string;
  setActiveTab: (tab: string) => void;
}

const SuperAdminDashboard = ({ 
  dashboardData, 
  isLoadingData,
  activeTab,
  setActiveTab
}: SuperAdminDashboardProps) => {
  return (
    <Tabs defaultValue="overview" className="space-y-4" value={activeTab} onValueChange={setActiveTab}>
      <TabsList className="grid w-full md:w-auto grid-cols-2 md:grid-cols-4">
        <TabsTrigger value="overview">Overview</TabsTrigger>
        <TabsTrigger value="students">Students</TabsTrigger>
        <TabsTrigger value="finance">Finance</TabsTrigger>
        <TabsTrigger value="system">System</TabsTrigger>
      </TabsList>
      
      <TabsContent value="overview" className="space-y-4">
        {/* Overview Tab */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <MetricCard 
            title="Total Students" 
            value={dashboardData?.studentStats?.totalStudents || 0}
            icon={Users}
            trend={{ value: 5.2, isPositive: true }}
            isLoading={isLoadingData}
          />
          <MetricCard 
            title="Active Classes" 
            value={dashboardData?.classStats?.activeClasses || 0}
            icon={BookOpen}
            trend={{ value: 2.1, isPositive: true }}
            isLoading={isLoadingData}
          />
          <MetricCard 
            title="Revenue" 
            value={dashboardData?.paymentStats?.totalRevenue || 0}
            prefix="$"
            icon={DollarSign}
            trend={{ value: 7.3, isPositive: true }}
            isLoading={isLoadingData}
          />
          <MetricCard 
            title="Room Utilization" 
            value={dashboardData?.roomUtilization?.roomUtilizationRate || 0}
            suffix="%"
            icon={Home}
            trend={{ value: 1.5, isPositive: false }}
            isLoading={isLoadingData}
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
          <InteractiveChart
            title="Student Enrollment"
            description="Enrollment trends over time"
            data={dashboardData?.studentStats?.registrationTrend || []}
            xKey="date"
            yKeys={[{ key: "count", name: "Students", color: "#4f46e5" }]}
            type="area"
            className="lg:col-span-2"
            isLoading={isLoadingData}
            actions={true}
          />
          <DashboardSection
            title="System Activity"
            description="Recent system activities"
            isLoading={isLoadingData}
          >
            <ActivityFeed 
              activities={dashboardData?.systemActivity?.recentActivities || []}
              isLoading={isLoadingData}
              title="Recent Activities"
            />
          </DashboardSection>
        </div>
      </TabsContent>

      <TabsContent value="students" className="space-y-4">
        {/* Students Tab */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <MetricCard 
            title="Total Students" 
            value={dashboardData?.studentStats?.totalStudents || 0}
            icon={Users}
            isLoading={isLoadingData}
          />
          <MetricCard 
            title="Active Students" 
            value={dashboardData?.studentStats?.activeStudents || 0}
            icon={Users}
            isLoading={isLoadingData}
          />
          <MetricCard 
            title="Retention Rate" 
            value={dashboardData?.studentStats?.retentionRate || 0}
            suffix="%"
            icon={PercentIcon}
            isLoading={isLoadingData}
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <InteractiveChart
            title="Student Distribution"
            description="Students by level"
            data={dashboardData?.studentStats?.studentsByLevel || []}
            xKey="level"
            yKeys={[{ key: "count", name: "Students", color: "#4f46e5" }]}
            type="bar"
            isLoading={isLoadingData}
            actions={true}
          />
          <InteractiveChart
            title="Registration Trend"
            description="New student registrations"
            data={dashboardData?.studentStats?.registrationTrend || []}
            xKey="date"
            yKeys={[{ key: "count", name: "Registrations", color: "#10b981" }]}
            type="area"
            isLoading={isLoadingData}
            actions={true}
          />
        </div>
      </TabsContent>

      <TabsContent value="finance" className="space-y-4">
        {/* Finance Tab */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <MetricCard 
            title="Total Revenue" 
            value={dashboardData?.paymentStats?.totalRevenue || 0}
            prefix="$"
            icon={DollarSign}
            isLoading={isLoadingData}
          />
          <MetricCard 
            title="Pending Payments" 
            value={dashboardData?.paymentStats?.pendingPayments || 0}
            prefix="$"
            icon={ClockIcon}
            isLoading={isLoadingData}
          />
          <MetricCard 
            title="Overdue Payments" 
            value={dashboardData?.paymentStats?.overduePayments || 0}
            prefix="$"
            icon={AlertTriangleIcon}
            isLoading={isLoadingData}
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
          <InteractiveChart
            title="Revenue Trend"
            description="Revenue by period"
            data={dashboardData?.paymentStats?.revenueByPeriod || []}
            xKey="period"
            yKeys={[{ key: "amount", name: "Revenue", color: "#f59e0b" }]}
            type="bar"
            isLoading={isLoadingData}
            actions={true}
          />
          <InteractiveChart
            title="Payment Methods"
            description="Revenue by payment method"
            data={dashboardData?.paymentStats?.revenueByMethod || []}
            xKey="method"
            yKeys={[{ key: "amount", name: "Amount", color: "#06b6d4" }]}
            type="pie"
            isLoading={isLoadingData}
            actions={true}
          />
        </div>
      </TabsContent>

      <TabsContent value="system" className="space-y-4">
        {/* System Tab */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <MetricCard 
            title="Active Rooms" 
            value={dashboardData?.roomUtilization?.activeRooms || 0}
            icon={Home}
            isLoading={isLoadingData}
          />
          <MetricCard 
            title="Room Utilization" 
            value={dashboardData?.roomUtilization?.roomUtilizationRate || 0}
            suffix="%"
            icon={BarChart2}
            isLoading={isLoadingData}
          />
          <MetricCard 
            title="Total Users" 
            value={dashboardData?.systemActivity?.userActivity?.length || 0}
            icon={Users}
            isLoading={isLoadingData}
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
          <InteractiveChart
            title="Room Utilization"
            description="Room usage by day"
            data={dashboardData?.roomUtilization?.roomUtilizationByDay || []}
            xKey="day"
            yKeys={[{ key: "rate", name: "Utilization", color: "#0ea5e9" }]}
            type="bar"
            className="lg:col-span-2"
            isLoading={isLoadingData}
            actions={true}
          />
          <DashboardSection
            title="User Activity"
            description="Recent user actions"
            isLoading={isLoadingData}
          >
            <div className="space-y-2">
              {dashboardData?.systemActivity?.userActivity?.slice(0, 5).map((user, index) => (
                <div key={index} className="flex justify-between items-center p-2 text-sm rounded hover:bg-muted">
                  <span>{user.userName}</span>
                  <span className="text-muted-foreground text-xs">{user.actionCount} actions</span>
                </div>
              ))}
            </div>
          </DashboardSection>
        </div>
      </TabsContent>
    </Tabs>
  );
};

export default SuperAdminDashboard;
