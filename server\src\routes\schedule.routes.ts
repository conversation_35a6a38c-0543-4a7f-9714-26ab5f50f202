// server/src/routes/schedule.routes.ts
import { Router } from 'express';
import { ScheduleController } from '../controllers/schedule.controller';
import { authorizeRoles } from '../middleware/auth.middleware';
import { validate } from '../middleware/validate.middleware';
import { scheduleValidation } from '../validations/schedule.validation';
import { catchAsync } from '../middleware/error.middleware';
import { apiLimiter } from '../middleware/rateLimiter.middleware';

const router = Router();

// Get daily schedule
router.get(
    '/daily',
    authorizeRoles('superAdmin', 'manager', 'secretary', 'teacher'),
    validate(scheduleValidation.getDailySchedule),
    catchAsync(ScheduleController.getDailySchedule)
);

// Get weekly schedule
router.get(
    '/weekly',
    authorizeRoles('superAdmin', 'manager', 'secretary', 'teacher'),
    validate(scheduleValidation.getWeeklySchedule),
    catchAsync(ScheduleController.getWeeklySchedule)
);

// Get schedule by date range
router.get(
    '/range',
    authorizeRoles('superAdmin', 'manager', 'secretary', 'teacher'),
    validate(scheduleValidation.getScheduleByRange),
    catchAsync(ScheduleController.getScheduleByRange)
);

// Get teacher schedule
router.get(
    '/teacher/:teacherId',
    authorizeRoles('superAdmin', 'manager', 'secretary', 'teacher'),
    validate(scheduleValidation.getTeacherSchedule),
    catchAsync(ScheduleController.getTeacherSchedule)
);

// Get room schedule
router.get(
    '/room/:roomId',
    authorizeRoles('superAdmin', 'manager', 'secretary', 'teacher'),
    validate(scheduleValidation.getRoomSchedule),
    catchAsync(ScheduleController.getRoomSchedule)
);

// Get class schedule
router.get(
    '/class/:classId',
    authorizeRoles('superAdmin', 'manager', 'secretary', 'teacher'),
    validate(scheduleValidation.getClassSchedule),
    catchAsync(ScheduleController.getClassSchedule)
);

// Apply rate limiting to all routes
router.use(apiLimiter);

export default router;
