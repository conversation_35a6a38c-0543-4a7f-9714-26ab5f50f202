// server/src/controllers/dashboard.controller.ts
import { Request, Response } from 'express';
import { DashboardService } from '../services/dashboard.service';
import { AppError } from '../types/error.types';

export class DashboardController {
    static async getDashboardData(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { fromDate, toDate } = req.query;
        const userRole = currentUser.role;

        const dashboardData = await DashboardService.getDashboardData(
            userRole,
            {
                fromDate: fromDate ? new Date(fromDate as string) : undefined,
                toDate: toDate ? new Date(toDate as string) : undefined
            },
            currentUser._id.toString()
        );

        res.json({
            success: true,
            data: dashboardData
        });
    }

    static async getClassStatistics(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { fromDate, toDate, level } = req.query;

        const statistics = await DashboardService.getClassStatistics(
            {
                fromDate: fromDate ? new Date(fromDate as string) : undefined,
                toDate: toDate ? new Date(toDate as string) : undefined,
                level: level as string
            },
            currentUser._id.toString()
        );

        res.json({
            success: true,
            data: statistics
        });
    }

    static async getStudentStatistics(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { fromDate, toDate, level } = req.query;

        const statistics = await DashboardService.getStudentStatistics(
            {
                fromDate: fromDate ? new Date(fromDate as string) : undefined,
                toDate: toDate ? new Date(toDate as string) : undefined,
                level: level as string
            },
            currentUser._id.toString()
        );

        res.json({
            success: true,
            data: statistics
        });
    }

    static async getAttendanceStatistics(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { fromDate, toDate } = req.query;

        const statistics = await DashboardService.getAttendanceStatistics(
            {
                fromDate: fromDate ? new Date(fromDate as string) : undefined,
                toDate: toDate ? new Date(toDate as string) : undefined
            },
            currentUser._id.toString()
        );

        res.json({
            success: true,
            data: statistics
        });
    }

    static async getPaymentStatistics(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { startDate, endDate } = req.query;

        const statistics = await DashboardService.getPaymentStatistics(
            {
                startDate: startDate ? new Date(startDate as string) : undefined,
                endDate: endDate ? new Date(endDate as string) : undefined
            },
            currentUser._id.toString()
        );

        res.json({
            success: true,
            data: statistics
        });
    }

    static async getRoomUtilization(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { startDate, endDate } = req.query;

        const utilization = await DashboardService.getRoomUtilization(
            {
                startDate: startDate ? new Date(startDate as string) : undefined,
                endDate: endDate ? new Date(endDate as string) : undefined
            },
            currentUser._id.toString()
        );

        res.json({
            success: true,
            data: utilization
        });
    }

    static async getSystemActivity(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { fromDate, toDate, limit } = req.query;

        const activity = await DashboardService.getSystemActivity(
            {
                fromDate: fromDate ? new Date(fromDate as string) : undefined,
                toDate: toDate ? new Date(toDate as string) : undefined,
                limit: limit ? parseInt(limit as string) : 10
            },
            currentUser._id.toString()
        );

        res.json({
            success: true,
            data: activity
        });
    }
}
