// server/src/controllers/payment.controller.ts
import { Request, Response } from 'express';
import { PaymentService } from '../services/payment.service';
import { AppError } from '../types/error.types';
import { SystemLogger } from '../services/logger.service';
import {
    PaymentQueryOptions,
    CreatePaymentDTO,
    UpdatePaymentDTO,
    VoidPaymentDTO,
    PaymentExportOptions
} from '../types/payment.types';

export class PaymentController {
    static async getPayments(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const {
            page,
            limit,
            sortBy,
            sortOrder,
            status,
            method,
            studentId,
            dateRange,
            amountRange,
            dueDateRange,
            period,
            search
        } = req.query;

        const options: PaymentQueryOptions = {
            page: page ? parseInt(page as string) : undefined,
            limit: limit ? parseInt(limit as string) : undefined,
            sortBy: sortBy as 'date' | 'amount' | 'nextDueDate',
            sortOrder: sortOrder as 'asc' | 'desc',
            status: status as any,
            method: method as any,
            studentId: studentId as string,
            dateRange: dateRange ? {
                start: new Date((dateRange as any).start),
                end: new Date((dateRange as any).end)
            } : undefined,
            amountRange: amountRange ? {
                min: parseFloat((amountRange as any).min),
                max: parseFloat((amountRange as any).max)
            } : undefined,
            dueDateRange: dueDateRange ? {
                start: new Date((dueDateRange as any).start),
                end: new Date((dueDateRange as any).end)
            } : undefined,
            period: period as any,
            search: search as string
        };

        const result = await PaymentService.getPayments(
            options,
            currentUser._id.toString(),
            currentUser.role
        );

        res.json({
            success: true,
            data: result.payments,
            pagination: result.pagination
        });
    }

    static async getPaymentById(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { id } = req.params;
        const payment = await PaymentService.getPaymentById(
            id,
            currentUser._id.toString(),
            currentUser.role
        );

        res.json({
            success: true,
            data: payment
        });
    }

    static async createPayment(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const paymentData: CreatePaymentDTO = req.body;
        const payment = await PaymentService.createPayment(
            paymentData,
            currentUser._id.toString()
        );

        await SystemLogger.log({
            severity: 'info',
            category: 'payments',
            action: 'create_payment',
            performedBy: currentUser._id.toString(),
            details: {
                paymentId: payment.id,
                studentId: paymentData.studentId,
                amount: paymentData.amount
            },
            status: 'success',
            timestamp: new Date()
        });

        res.status(201).json({
            success: true,
            message: 'Payment recorded successfully',
            data: payment
        });
    }

    static async updatePayment(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { id } = req.params;
        const updateData: UpdatePaymentDTO = req.body;

        const payment = await PaymentService.updatePayment(
            id,
            updateData,
            currentUser._id.toString(),
            currentUser.role
        );

        await SystemLogger.log({
            severity: 'info',
            category: 'payments',
            action: 'update_payment',
            performedBy: currentUser._id.toString(),
            targetId: id,
            details: {
                updates: updateData
            },
            status: 'success',
            timestamp: new Date()
        });

        res.json({
            success: true,
            message: 'Payment updated successfully',
            data: payment
        });
    }

    // static async voidPayment(req: Request, res: Response) {
    //     const currentUser = req.user;
    //     if (!currentUser?._id) {
    //         throw new AppError(401, 'User not authenticated');
    //     }

    //     const { id } = req.params;
    //     const voidData: VoidPaymentDTO = req.body;

    //     const payment = await PaymentService.voidPayment(
    //         id,
    //         voidData,
    //         currentUser._id.toString(),
    //         currentUser.role
    //     );

    //     await SystemLogger.log({
    //         severity: 'info',
    //         category: 'payments',
    //         action: 'void_payment',
    //         performedBy: currentUser._id.toString(),
    //         targetId: id,
    //         details: {
    //             reason: voidData.reason
    //         },
    //         status: 'success',
    //         timestamp: new Date()
    //     });

    //     res.json({
    //         success: true,
    //         message: 'Payment voided successfully',
    //         data: payment
    //     });
    // }

    static async voidPayment(req: Request, res: Response) {
        console.log('VoidPayment controller reached:', {
            params: req.params,
            body: req.body,
            userId: req.user?._id
        });
    
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }
    
        const { id } = req.params;
        const voidData: VoidPaymentDTO = req.body;
    
        console.log('Calling payment service voidPayment:', {
            id,
            voidData,
            userRole: currentUser.role
        });
    
        try {
            const payment = await PaymentService.voidPayment(
                id,
                voidData,
                currentUser._id.toString(),
                currentUser.role
            );
    
            await SystemLogger.log({
                severity: 'info',
                category: 'payments',
                action: 'void_payment',
                performedBy: currentUser._id.toString(),
                targetId: id,
                details: {
                    reason: voidData.reason
                },
                status: 'success',
                timestamp: new Date()
            });
    
            res.json({
                success: true,
                message: 'Payment voided successfully',
                data: payment
            });
        } catch (error) {
            console.error('Error in voidPayment controller:', {
                error,
                stack: error instanceof Error ? error.stack : undefined
            });
            throw error;
        }
    }

    static async getStudentPaymentHistory(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { studentId } = req.params;
        const { startDate, endDate } = req.query;

        const history = await PaymentService.getStudentPaymentHistory(
            studentId,
            startDate ? new Date(startDate as string) : undefined,
            endDate ? new Date(endDate as string) : undefined,
            currentUser.role
        );

        res.json({
            success: true,
            data: history
        });
    }

    // static async getPaymentStatistics(req: Request, res: Response) {
    //     const currentUser = req.user;
    //     if (!currentUser?._id) {
    //         throw new AppError(401, 'User not authenticated');
    //     }

    //     const { startDate, endDate, groupBy } = req.query;

    //     const stats = await PaymentService.getPaymentStatistics(
    //         {
    //             startDate: startDate ? new Date(startDate as string) : undefined,
    //             endDate: endDate ? new Date(endDate as string) : undefined,
    //             groupBy: groupBy as 'daily' | 'monthly' | 'method' | 'status'
    //         },
    //         currentUser.role
    //     );

    //     res.json({
    //         success: true,
    //         data: stats
    //     });
    // }
    static async getPaymentStatistics(req: Request, res: Response) {
        try {
            const currentUser = req.user;
            if (!currentUser?._id) {
                throw new AppError(401, 'User not authenticated');
            }
    
            // Log incoming request
            console.log('Payment statistics request:', {
                query: req.query,
                user: currentUser._id.toString()
            });
    
            // Parse and validate query parameters
            const { 
                startDate: startDateString, 
                endDate: endDateString, 
                groupBy = 'monthly',
                includeVoided = 'true'
            } = req.query;
    
            const startDate = startDateString ? new Date(startDateString as string) : undefined;
            const endDate = endDateString ? new Date(endDateString as string) : undefined;
    
            // Validate groupBy
            if (!['daily', 'monthly', 'method', 'status'].includes(groupBy as string)) {
                throw new AppError(400, 'Invalid groupBy parameter. Must be one of: daily, monthly, method, status');
            }
    
            // Validate dates if provided
            if (startDate && endDate && startDate > endDate) {
                throw new AppError(400, 'Start date must be before end date');
            }
    
            const stats = await PaymentService.getPaymentStatistics(
                {
                    startDate,
                    endDate,
                    groupBy: groupBy as 'daily' | 'monthly' | 'method' | 'status',
                    includeVoided: includeVoided === 'true'
                },
                currentUser.role
            );
    
            console.log('Generated statistics:', stats);
    
            res.json({
                success: true,
                data: stats
            });
        } catch (error) {
            console.error('Error in getPaymentStatistics:', error);
            if (error instanceof AppError) {
                throw error;
            }
            throw new AppError(500, 'Error generating payment statistics');
        }
    }

    static async exportPayments(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const {
            format = 'csv',
            dateRange,
            includeVoided,
            groupBy,
            fields
        } = req.query;

        const options: PaymentExportOptions = {
            format: format as 'csv' | 'json',
            dateRange: dateRange ? {
                start: new Date((dateRange as any).start),
                end: new Date((dateRange as any).end)
            } : undefined,
            includeVoided: includeVoided === 'true',
            groupBy: groupBy as any,
            fields: fields ? (fields as string).split(',') : undefined
        };

        const exportData = await PaymentService.exportPayments(
            options,
            currentUser._id.toString(),
            currentUser.role
        );

        const filename = `payments_export_${new Date().toISOString()}.${format}`;
        res.setHeader('Content-Disposition', `attachment; filename=${filename}`);
        res.setHeader('Content-Type', format === 'csv' ? 'text/csv' : 'application/json');
        res.send(exportData);
    }

    static async generatePaymentReceipt(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { id } = req.params;
        const { format = 'pdf' } = req.query;

        const receipt = await PaymentService.generateReceipt(
            id,
            format as 'pdf' | 'html',
            currentUser._id.toString()
        );

        const filename = `receipt_${receipt.receiptNumber}.${format}`;
        res.setHeader('Content-Disposition', `attachment; filename=${filename}`);
        res.setHeader('Content-Type', format === 'pdf' ? 'application/pdf' : 'text/html');
        res.send(receipt.content);
    }
}