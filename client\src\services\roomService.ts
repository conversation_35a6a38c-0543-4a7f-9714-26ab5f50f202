
import {
  Room,
  RoomStatus,
  MaintenancePeriod,
  RoomScheduleItem,
  RoomUtilization,
  RoomFilters,
  PaginatedRoomsResponse,
  RoomAvailabilityResponse,
  AvailableRoomsResponse
} from "@/types/room";
import { toast } from "sonner";
import { roomsApi } from "@/lib/api";

// Room features for filtering (these should match backend features)
export const ROOM_FEATURES = [
  { value: "projector", label: "Projector" },
  { value: "whiteboard", label: "Whiteboard" },
  { value: "smartboard", label: "Smart Board" },
  { value: "computers", label: "Computers" },
  { value: "wifi", label: "WiFi" },
  { value: "audioSystem", label: "Audio System" },
  { value: "videoConference", label: "Video Conference" },
  { value: "airConditioning", label: "Air Conditioning" },
  { value: "laboratoryEquipment", label: "Laboratory Equipment" },
  { value: "specializedSoftware", label: "Specialized Software" },
  { value: "accessibleFacilities", label: "Accessible Facilities" }
];

// Function to fetch rooms with filters
export const fetchRooms = async (filters: RoomFilters = {}): Promise<PaginatedRoomsResponse> => {
  try {
    const response = await roomsApi.getRooms(filters);

    return {
      success: response.success,
      data: response.data,
      pagination: response.pagination
    };
  } catch (error: any) {
    console.error("Error fetching rooms:", error);
    const errorMessage = error?.response?.data?.message || "Failed to fetch rooms";
    toast.error(errorMessage);
    throw error;
  }
};

// Function to fetch a specific room
export const fetchRoom = async (roomId: string): Promise<Room> => {
  try {
    const response = await roomsApi.getRoomById(roomId);
    return response.data;
  } catch (error: any) {
    console.error(`Error fetching room ${roomId}:`, error);
    const errorMessage = error?.response?.data?.message || "Failed to fetch room details";
    toast.error(errorMessage);
    throw error;
  }
};

// Function to create a new room
export const createRoom = async (roomData: Partial<Room>): Promise<Room> => {
  try {
    // Validate required fields
    if (!roomData.name || !roomData.capacity || !roomData.building || roomData.floor === undefined) {
      throw new Error("Missing required room information");
    }

    const response = await roomsApi.createRoom({
      name: roomData.name,
      capacity: roomData.capacity,
      building: roomData.building,
      floor: roomData.floor,
      features: roomData.features || []
    });

    toast.success(`Room ${roomData.name} created successfully`);
    return response.data;
  } catch (error: any) {
    console.error("Error creating room:", error);
    const errorMessage = error?.response?.data?.message || "Failed to create room";
    toast.error(errorMessage);
    throw error;
  }
};

// Function to update a room
export const updateRoom = async (roomId: string, roomData: Partial<Room>): Promise<Room> => {
  try {
    const response = await roomsApi.updateRoom(roomId, roomData);

    toast.success(`Room updated successfully`);
    return response.data;
  } catch (error: any) {
    console.error(`Error updating room ${roomId}:`, error);
    const errorMessage = error?.response?.data?.message || "Failed to update room";
    toast.error(errorMessage);
    throw error;
  }
};

// Function to check room availability
export const checkRoomAvailability = async (
  roomId: string,
  date: string,
  timeStart: string,
  timeEnd: string
): Promise<RoomAvailabilityResponse> => {
  try {
    const response = await roomsApi.checkRoomAvailability(roomId, {
      date,
      timeStart,
      timeEnd
    });

    return response.data;
  } catch (error: any) {
    console.error(`Error checking availability for room ${roomId}:`, error);
    const errorMessage = error?.response?.data?.message || "Failed to check room availability";
    toast.error(errorMessage);
    throw error;
  }
};

// Function to find available rooms for a time slot
export const findAvailableRooms = async (
  date: string,
  timeStart: string,
  timeEnd: string,
  filters: RoomFilters = {}
): Promise<AvailableRoomsResponse> => {
  try {
    const response = await roomsApi.findAvailableRooms({
      date,
      timeStart,
      timeEnd,
      ...filters
    });

    return {
      success: response.success,
      data: response.data,
      pagination: response.pagination
    };
  } catch (error: any) {
    console.error("Error finding available rooms:", error);
    const errorMessage = error?.response?.data?.message || "Failed to find available rooms";
    toast.error(errorMessage);
    throw error;
  }
};

// Function to schedule room maintenance
export const scheduleRoomMaintenance = async (
  roomId: string,
  startDate: string,
  endDate: string,
  reason: string
): Promise<MaintenancePeriod> => {
  try {
    // Check if dates are valid
    const start = new Date(startDate);
    const end = new Date(endDate);

    if (isNaN(start.getTime()) || isNaN(end.getTime()) || start >= end) {
      throw new Error("Invalid date range provided");
    }

    const response = await roomsApi.scheduleMaintenance(roomId, {
      startDate,
      endDate,
      reason
    });

    toast.success("Maintenance scheduled successfully");
    return response.data;
  } catch (error: any) {
    console.error(`Error scheduling maintenance for room ${roomId}:`, error);
    const errorMessage = error?.response?.data?.message || "Failed to schedule maintenance";
    toast.error(errorMessage);
    throw error;
  }
};

// Function to fetch room schedule
export const fetchRoomSchedule = async (
  roomId: string,
  startDate: string,
  endDate: string
): Promise<RoomScheduleItem[]> => {
  try {
    const response = await roomsApi.getRoomSchedule(roomId, {
      startDate,
      endDate
    });

    return response.data;
  } catch (error: any) {
    console.error(`Error fetching schedule for room ${roomId}:`, error);
    const errorMessage = error?.response?.data?.message || "Failed to fetch room schedule";
    toast.error(errorMessage);
    throw error;
  }
};

// Function to get room utilization statistics
export const fetchRoomUtilization = async (
  roomId: string,
  startDate: string,
  endDate: string
): Promise<RoomUtilization> => {
  try {
    const response = await roomsApi.getRoomUtilization(roomId, {
      startDate,
      endDate
    });

    return response.data;
  } catch (error: any) {
    console.error(`Error fetching utilization for room ${roomId}:`, error);
    const errorMessage = error?.response?.data?.message || "Failed to fetch room utilization data";
    toast.error(errorMessage);
    throw error;
  }
};
