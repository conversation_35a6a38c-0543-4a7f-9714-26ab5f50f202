
import { Teacher } from "@/types/class";

// Function to fetch teachers
export const fetchTeachers = async (): Promise<Teacher[]> => {
  try {
    console.log("Fetching teachers");
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Mock data for teachers
    const teachers: Teacher[] = [
      {
        id: "teacher1",
        name: "<PERSON>",
        schedule: [
          {
            day: "monday",
            timeStart: "09:00",
            timeEnd: "11:00"
          },
          {
            day: "wednesday",
            timeStart: "13:00",
            timeEnd: "15:00"
          }
        ]
      },
      {
        id: "teacher2",
        name: "<PERSON>",
        schedule: [
          {
            day: "tuesday",
            timeStart: "10:00",
            timeEnd: "12:00"
          },
          {
            day: "thursday",
            timeStart: "14:00",
            timeEnd: "16:00"
          }
        ]
      },
      {
        id: "teacher3",
        name: "<PERSON>",
        schedule: [
          {
            day: "friday",
            timeStart: "09:00",
            timeEnd: "11:00"
          }
        ]
      },
      {
        id: "teacher4",
        name: "<PERSON>",
        schedule: []
      },
      {
        id: "teacher5",
        name: "<PERSON>",
        schedule: [
          {
            day: "monday",
            timeStart: "13:00",
            timeEnd: "15:00"
          }
        ]
      }
    ];
    
    return teachers;
  } catch (error) {
    console.error("Error fetching teachers:", error);
    throw error;
  }
};
