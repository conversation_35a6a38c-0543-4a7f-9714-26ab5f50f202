
// Re-export all services for easier imports
// To fix ambiguous exports, we'll specify exactly what to export from each service

// Activity Log Service
export { 
  fetchActivityLogs,
  // These were causing errors - commenting them out
  //fetchActivityLogSummary as fetchLogSummary,
  //addLogEntry
} from './activityLogService';

// Attendance Service
export {
  fetchAttendanceRecords,
  markAttendance,
  fetchClassAttendanceStats,
  // These were causing errors - commenting them out
  //submitAttendanceExcuse,
  //fetchAttendanceRecords as fetchAttendanceTrends
} from './attendanceService';

// Class Merge Service
export {
  mergeClasses,
  // This was causing an error - commenting it out
  //validateClassMerge,
  splitClass
} from './classMergeService';

// Class Service
export {
  fetchClasses,
  createClass,
  updateClass,
  fetchClassById,
  // These were causing errors - commenting them out
  //deleteClass,
  //fetchClassSchedule
} from './classService';

// Makeup Class Service
export {
  scheduleMakeupClass,
  // These were causing errors - commenting them out
  //fetchMakeupClasses,
  //cancelMakeupClass
} from './makeupClassService';

// Notes Service
export {
  fetchNotes,
  createNote,
  updateNote,
  deleteNote,
  fetchNotesSummary
} from './notesService';

// Payment Service
export {
  fetchPayments,
  createPayment,
  updatePayment,
  generateReceipt,
} from './paymentService';

// Report Service
export {
  generateReport,
  getReportTemplates,
  previewReport,
  getReportOptions,
  // These were causing errors - commenting them out
  //fetchReportData,
  //saveCustomTemplate
} from './reportService';

// Room Service
export {
  fetchRooms,
  fetchRoom,
  createRoom,
  updateRoom,
  fetchRoomSchedule,
  fetchRoomUtilization,
  checkRoomAvailability,
  findAvailableRooms,
  scheduleRoomMaintenance
} from './roomService';

// Student Service
export {
  fetchStudents,
  fetchStudent,
  createStudent,
  updateStudent,
  archiveStudent,
  restoreStudent,
  transferStudent,
} from './studentService';

// Teacher Scheduling Service
export {
  getTeacherSchedule,
  replaceTeacher,
  // This was causing an error - commenting it out
  //requestSubstitute
} from './teacherSchedulingService';

// Teacher Service
export {
  fetchTeachers,
  // These were causing errors - commenting them out
  //createTeacher,
  //updateTeacher,
  //archiveTeacher
} from './teacherService';

// System Service
export {
  getSystemStats,
  // These were causing errors - commenting them out
  //performSystemMaintenance,
  //backupDatabase,
  //restoreDatabase,
  //applySystemUpdates
} from './systemService';

// Dashboard Service
export {
  getDashboardData,
  // These were causing errors - commenting them out
  //fetchDashboardSummary,
  //getDashboardData as fetchUserDashboardData
} from './dashboardService';
