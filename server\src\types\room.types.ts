// server/src/types/room.types.ts
import { Types } from 'mongoose';

// Basic types
export type RoomStatus = 'active' | 'maintenance' | 'inactive';

export interface TimeSlot {
    start: string;  // Format: "HH:mm"
    end: string;    // Format: "HH:mm"
    classId?: Types.ObjectId;
}

export interface MaintenanceSchedule {
    startDate: Date;
    endDate: Date;
    reason: string;
    scheduledBy: Types.ObjectId;
}

// Main room interface
export interface IRoom {
    _id?: Types.ObjectId;
    name: string;
    capacity: number;
    building: string;
    floor: number;
    features: string[];
    status: RoomStatus;
    maintenanceSchedule: MaintenanceSchedule[];
    availability: Array<{
        date: Date;
        timeSlots: TimeSlot[];
    }>;
    lastModifiedAt: Date;
    lastModifiedBy: Types.ObjectId;
}

// Query options for fetching rooms
export interface RoomQueryOptions {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    status?: RoomStatus;
    building?: string;
    floor?: number;
    minCapacity?: number;
    features?: string[];
    search?: string;
    availableAt?: {
        date: Date;
        timeSlot: TimeSlot;
    };
}

// DTOs for creating/updating rooms
export interface CreateRoomDTO {
    name: string;
    capacity: number;
    building: string;
    floor: number;
    features?: string[];
}

export interface UpdateRoomDTO {
    name?: string;
    capacity?: number;
    building?: string;
    floor?: number;
    features?: string[];
    status?: RoomStatus;
}

export interface ScheduleMaintenanceDTO {
    startDate: Date;
    endDate: Date;
    reason: string;
}

// Response DTOs
export interface RoomResponseDTO {
    id: string;
    name: string;
    capacity: number;
    building: string;
    floor: number;
    features: string[];
    status: RoomStatus;
    currentSchedule?: {
        date: string;
        timeSlots: Array<{
            start: string;
            end: string;
            class: {
                id: string;
                name: string;
            };
        }>;
    };
    maintenanceSchedule: Array<{
        startDate: string;
        endDate: string;
        reason: string;
        scheduledBy: {
            id: string;
            username: string;
        };
    }>;
    lastModified: {
        at: Date;
        by: {
            id: string;
            username: string;
        };
    };
}

// Operation error types
export interface RoomOperationError {
    operation: string;
    roomId: string;
    reason: string;
    details?: any;
}

// Statistics and reporting
export interface RoomUtilizationStats {
    roomId: string;
    roomName: string;
    totalHours: number;
    utilizationRate: number;
    classesHeld: number;
    periodicUsage: Array<{
        period: string;
        hours: number;
        rate: number;
    }>;
}

// Export options
export interface RoomExportOptions {
    format: 'csv' | 'json';
    dateRange?: {
        start: Date;
        end: Date;
    };
    includeSchedule?: boolean;
    includeMaintenance?: boolean;
    fields?: string[];
}

export interface AvailabilitySlot {
    date: Date;
    timeSlots: TimeSlot[];
}

// Update the IRoom interface to include the availability property
export interface IRoom {
    _id?: Types.ObjectId;
    name: string;
    capacity: number;
    building: string;
    floor: number;
    features: string[];
    status: RoomStatus;
    maintenanceSchedule: MaintenanceSchedule[];
    availability: AvailabilitySlot[]; // Added this property
    lastModifiedAt: Date;
    lastModifiedBy: Types.ObjectId;
}