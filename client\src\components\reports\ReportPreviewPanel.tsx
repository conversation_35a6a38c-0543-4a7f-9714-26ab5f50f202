
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Edit2 } from "lucide-react";
import { ReportPreviewData, ReportType } from "@/types/reports";

interface ReportPreviewPanelProps {
  reportType: string;
  previewData: ReportPreviewData;
  onEdit: () => void;
}

export default function ReportPreviewPanel({
  reportType,
  previewData,
  onEdit
}: ReportPreviewPanelProps) {
  // Format column header from accessor (snake_case to Title Case)
  const formatHeader = (header: string) => {
    return header
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Render summary cards if available
  const renderSummary = () => {
    if (!previewData.summary || previewData.summary.length === 0) {
      return null;
    }

    return (
      <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-4 mb-6">
        {previewData.summary.map((item, index) => (
          <Card key={index}>
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">{item.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{item.value}</div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  // Render data table
  const renderDataTable = () => {
    if (!previewData.data || previewData.data.length === 0) {
      return (
        <div className="p-6 text-center text-muted-foreground">
          No data available for preview
        </div>
      );
    }

    return (
      <div className="overflow-x-auto rounded-md border">
        <table className="w-full">
          <thead>
            <tr className="bg-muted/50">
              {previewData.columns.map((column, index) => (
                <th key={index} className="p-3 text-left font-medium">
                  {column.header || formatHeader(column.accessor)}
                </th>
              ))}
            </tr>
          </thead>
          <tbody>
            {previewData.data.map((row, rowIndex) => (
              <tr key={rowIndex} className="border-t">
                {previewData.columns.map((column, colIndex) => (
                  <td key={colIndex} className="p-3 align-top">
                    {row[column.accessor] !== undefined ? String(row[column.accessor]) : "-"}
                  </td>
                ))}
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  // Render charts if available
  const renderCharts = () => {
    if (!previewData.charts || previewData.charts.length === 0) {
      return null;
    }

    return (
      <div className="space-y-6 mb-6">
        {previewData.charts.map((chart, index) => (
          <Card key={index}>
            <CardHeader>
              <CardTitle className="text-base">{chart.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-64 w-full">
                {/* Placeholder for chart visualization */}
                <div className="flex items-center justify-center h-full bg-muted/30 rounded-md">
                  <p className="text-muted-foreground">
                    Chart visualization placeholder
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">
          {reportType.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')} Preview
        </h2>
        <Button 
          variant="outline" 
          onClick={onEdit}
          size="sm"
        >
          <Edit2 className="h-4 w-4 mr-2" />
          Edit Parameters
        </Button>
      </div>

      {renderSummary()}
      {renderCharts()}
      {renderDataTable()}
    </div>
  );
}
