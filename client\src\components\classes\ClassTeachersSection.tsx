
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { Teacher } from "@/types/class";
import { capitalizeFirstLetter } from "@/lib/utils";
import CustomCard from "@/components/ui/CustomCard";

interface ClassTeachersSectionProps {
  teachers: Teacher[];
}

const ClassTeachersSection = ({ teachers }: ClassTeachersSectionProps) => {
  if (!teachers || teachers.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Teachers</CardTitle>
          <CardDescription>
            Teachers assigned to this class
          </CardDescription>
        </CardHeader>
        <CardContent className="text-center py-4 text-muted-foreground">
          No teachers have been assigned to this class.
        </CardContent>
      </Card>
    );
  }

  // Sort days for display
  const dayOrder = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"];
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Teachers</CardTitle>
        <CardDescription>
          Teachers assigned to this class
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4 sm:grid-cols-2 lg:grid-cols-3">
          {teachers.map((teacher) => (
            <CustomCard key={teacher.id} className="h-full">
              <div className="flex flex-col h-full">
                <h3 className="text-lg font-semibold mb-2">{teacher.name}</h3>
                
                <div className="mt-3">
                  <h4 className="text-sm font-medium text-muted-foreground mb-2">Schedule:</h4>
                  <div className="space-y-2">
                    {/* Sort schedule items by day of week */}
                    {teacher.schedule
                      .sort((a, b) => dayOrder.indexOf(a.day.toLowerCase()) - dayOrder.indexOf(b.day.toLowerCase()))
                      .map((scheduleItem, index) => (
                        <div key={index} className="text-sm">
                          <span className="font-medium">{capitalizeFirstLetter(scheduleItem.day)}</span>:{' '}
                          {scheduleItem.timeStart} - {scheduleItem.timeEnd}
                        </div>
                      ))
                    }
                  </div>
                </div>
              </div>
            </CustomCard>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default ClassTeachersSection;
