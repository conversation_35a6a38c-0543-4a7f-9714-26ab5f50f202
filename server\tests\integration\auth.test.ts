// server/src/tests/integration/auth.test.ts
import request from 'supertest';
import app from '../../src/app';
import { User } from '../../src/models/user.model';
import { createTestUser } from '../fixtures/auth.fixtures';
import { AuthService } from '../../src/services/auth.service';

describe('Authentication API', () => {
  let superAdminToken: string;
  let superAdminId: string;

  beforeEach(async () => {
    // Create initial superAdmin
    const superAdmin = await createTestUser(
      'superadmin',
      'SuperAdmin123!',
      'superAdmin',
      'system'
    );
    superAdminId = superAdmin._id.toString();
    superAdminToken = AuthService.generateToken(superAdminId);
  });

  describe('POST /api/auth/login', () => {
    it('should login successfully with valid credentials', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'superadmin',
          password: 'SuperAdmin123!'
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('token');
      expect(response.body.user).toHaveProperty('username', 'superadmin');
      expect(response.body.user).toHaveProperty('role', 'superAdmin');
    });

    it('should fail with invalid credentials', async () => {
      const response = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'superadmin',
          password: 'wrongpassword'
        });

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('message', 'Invalid credentials');
    });

    it('should fail with inactive account', async () => {
      const inactiveUser = await createTestUser(
        'inactive',
        'Password123!',
        'teacher',
        superAdminId
      );
      inactiveUser.status = 'inactive';
      await inactiveUser.save();

      const response = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'inactive',
          password: 'Password123!'
        });

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('message', 'Account is inactive');
    });
  });

  describe('POST /api/auth/users', () => {
    it('should create a new user when superAdmin requests', async () => {
      const response = await request(app)
        .post('/api/auth/users')
        .set('Authorization', `Bearer ${superAdminToken}`)
        .send({
          username: 'newteacher',
          password: 'Teacher123!',
          role: 'teacher'
        });

      expect(response.status).toBe(201);
      expect(response.body.user).toHaveProperty('username', 'newteacher');
      expect(response.body.user).toHaveProperty('role', 'teacher');

      // Verify user was created in database
      const user = await User.findOne({ username: 'newteacher' });
      expect(user).toBeTruthy();
      expect(user!.role).toBe('teacher');
    });

    it('should fail to create user with existing username', async () => {
      await createTestUser(
        'existinguser',
        'Password123!',
        'teacher',
        superAdminId
      );

      const response = await request(app)
        .post('/api/auth/users')
        .set('Authorization', `Bearer ${superAdminToken}`)
        .send({
          username: 'existinguser',
          password: 'NewPassword123!',
          role: 'teacher'
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('message', 'Username already exists');
    });

    it('should fail when non-superAdmin tries to create user', async () => {
      const teacher = await createTestUser(
        'teacher',
        'Teacher123!',
        'teacher',
        superAdminId
      );
      const teacherToken = AuthService.generateToken(teacher._id.toString());

      const response = await request(app)
        .post('/api/auth/users')
        .set('Authorization', `Bearer ${teacherToken}`)
        .send({
          username: 'newuser',
          password: 'Password123!',
          role: 'teacher'
        });

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('message', 'Unauthorized access');
    });
  });

  describe('POST /api/auth/change-password', () => {
    it('should change password successfully', async () => {
      const user = await createTestUser(
        'testuser',
        'OldPass123!',
        'teacher',
        superAdminId
      );
      const token = AuthService.generateToken(user._id.toString());

      const response = await request(app)
        .post('/api/auth/change-password')
        .set('Authorization', `Bearer ${token}`)
        .send({
          currentPassword: 'OldPass123!',
          newPassword: 'NewPass123!',
          confirmPassword: 'NewPass123!'
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', 'Password changed successfully');

      // Verify new password works for login
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'testuser',
          password: 'NewPass123!'
        });

      expect(loginResponse.status).toBe(200);
    });

    it('should fail with incorrect current password', async () => {
      const user = await createTestUser(
        'testuser',
        'Current123!',
        'teacher',
        superAdminId
      );
      const token = AuthService.generateToken(user._id.toString());

      const response = await request(app)
        .post('/api/auth/change-password')
        .set('Authorization', `Bearer ${token}`)
        .send({
          currentPassword: 'Wrong123!',
          newPassword: 'New123!',
          confirmPassword: 'New123!'
        });

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('message', 'Current password is incorrect');
    });
  });

  describe('POST /api/auth/reset-password', () => {
    it('should reset password when superAdmin requests', async () => {
      const user = await createTestUser(
        'resetuser',
        'Original123!',
        'teacher',
        superAdminId
      );

      const response = await request(app)
        .post('/api/auth/reset-password')
        .set('Authorization', `Bearer ${superAdminToken}`)
        .send({
          username: 'resetuser'
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', 'Password reset successful');
      expect(response.body).toHaveProperty('newPassword');

      // Verify new password works
      const loginResponse = await request(app)
        .post('/api/auth/login')
        .send({
          username: 'resetuser',
          password: response.body.newPassword
        });

      expect(loginResponse.status).toBe(200);
    });

    it('should fail to reset password for non-existent user', async () => {
      const response = await request(app)
        .post('/api/auth/reset-password')
        .set('Authorization', `Bearer ${superAdminToken}`)
        .send({
          username: 'nonexistentuser'
        });

      expect(response.status).toBe(404);
      expect(response.body).toHaveProperty('message', 'User not found');
    });
  });

  describe('PATCH /api/auth/users/:id/role', () => {
    it('should update user role successfully', async () => {
      const user = await createTestUser(
        'roleupdate',
        'Password123!',
        'teacher',
        superAdminId
      );

      const response = await request(app)
        .patch(`/api/auth/users/${user._id}/role`)
        .set('Authorization', `Bearer ${superAdminToken}`)
        .send({
          role: 'manager',
          reason: 'Promotion based on performance'
        });

      expect(response.status).toBe(200);
      expect(response.body.user).toHaveProperty('role', 'manager');

      // Verify role history
      const updatedUser = await User.findById(user._id);
      expect(updatedUser!.roleHistory).toHaveLength(2);
      expect(updatedUser!.roleHistory[1].role).toBe('manager');
      expect(updatedUser!.roleHistory[1].reason).toBe('Promotion based on performance');
    });

    it('should prevent self-role update', async () => {
      const response = await request(app)
        .patch(`/api/auth/users/${superAdminId}/role`)
        .set('Authorization', `Bearer ${superAdminToken}`)
        .send({
          role: 'manager',
          reason: 'Attempting self-update'
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('message', 'Cannot update own role');
    });

    it('should prevent removing last superAdmin', async () => {
      const response = await request(app)
        .patch(`/api/auth/users/${superAdminId}/role`)
        .set('Authorization', `Bearer ${superAdminToken}`)
        .send({
          role: 'manager',
          reason: 'Attempting to remove last superAdmin'
        });

      expect(response.status).toBe(400);
      expect(response.body).toHaveProperty('message', 'Cannot change role: At least one SuperAdmin must exist');
    });
  });

  describe('GET /api/auth/me', () => {
    it('should return current user information', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', `Bearer ${superAdminToken}`);

      expect(response.status).toBe(200);
      expect(response.body.user).toHaveProperty('username', 'superadmin');
      expect(response.body.user).toHaveProperty('role', 'superAdmin');
      expect(response.body.user).not.toHaveProperty('password');
      expect(response.body.user).not.toHaveProperty('loginAttempts');
    });

    it('should fail with invalid token', async () => {
      const response = await request(app)
        .get('/api/auth/me')
        .set('Authorization', 'Bearer invalid-token');

      expect(response.status).toBe(403);
      expect(response.body).toHaveProperty('message', 'Invalid token');
    });
  });

  describe('POST /api/auth/logout', () => {
    it('should logout successfully', async () => {
      const response = await request(app)
        .post('/api/auth/logout')
        .set('Authorization', `Bearer ${superAdminToken}`);

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('message', 'Logged out successfully');
    });
  });
});