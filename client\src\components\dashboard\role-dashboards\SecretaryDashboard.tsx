
import InteractiveChart from "../InteractiveChart";
import DashboardSection from "../DashboardSection";
import MetricCard from "../MetricCard";
import QuickActionButton from "../QuickActionButton";
import ActivityFeed from "../ActivityFeed";
import { 
  Users, 
  Calendar, 
  AlertCircle, 
  UserPlus, 
  DollarSign,
  CheckSquare,
  Edit,
} from "lucide-react";
import { DashboardData } from "@/services/dashboardService";

interface SecretaryDashboardProps {
  dashboardData: Partial<DashboardData>;
  isLoadingData: boolean;
}

const SecretaryDashboard = ({ dashboardData, isLoadingData }: SecretaryDashboardProps) => {
  return (
    <div className="space-y-6">
      {/* Metric Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard 
          title="Active Students" 
          value={dashboardData?.studentStats?.activeStudents || 0}
          icon={Users}
          isLoading={isLoadingData}
        />
        <MetricCard 
          title="Today's Classes" 
          value={dashboardData?.classStats?.activeClasses || 0} // In a real app, would be filtered to today
          icon={Calendar}
          isLoading={isLoadingData}
        />
        <MetricCard 
          title="Pending Payments" 
          value={dashboardData?.paymentStats?.pendingPayments || 0}
          prefix="$"
          icon={AlertCircle}
          isLoading={isLoadingData}
        />
        <MetricCard 
          title="New Registrations" 
          value={dashboardData?.studentStats?.registrationTrend?.[dashboardData?.studentStats?.registrationTrend.length - 1]?.count || 0}
          icon={UserPlus}
          isLoading={isLoadingData}
        />
      </div>

      {/* Quick Actions and Recent Activities */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        <DashboardSection
          title="Quick Actions"
          description="Common tasks"
          isLoading={isLoadingData}
        >
          <div className="grid grid-cols-2 gap-4">
            <QuickActionButton
              label="Register Student"
              icon={UserPlus}
              href="/students/create"
            />
            <QuickActionButton
              label="Record Payment"
              icon={DollarSign}
              href="/payments/new"
            />
            <QuickActionButton
              label="Mark Attendance"
              icon={CheckSquare}
              href="/attendance"
            />
            <QuickActionButton
              label="Add Note"
              icon={Edit}
              href="/notes"
            />
          </div>
        </DashboardSection>
        
        <DashboardSection
          title="Recent Activities"
          description="Latest system events"
          isLoading={isLoadingData}
          className="lg:col-span-2"
        >
          <ActivityFeed 
            activities={dashboardData?.systemActivity?.recentActivities?.slice(0, 5) || []}
            isLoading={isLoadingData}
            title="Recent Activities"
          />
        </DashboardSection>
      </div>

      {/* Payment and Class Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <InteractiveChart
          title="Recent Payments"
          description="Payment amounts by date"
          data={dashboardData?.paymentStats?.recentPayments?.map(payment => ({
            date: payment.date,
            amount: payment.amount,
            student: payment.studentName
          })) || []}
          xKey="date"
          yKeys={[{ key: "amount", name: "Amount", color: "#10b981" }]}
          type="bar"
          isLoading={isLoadingData}
          actions={true}
        />
        <InteractiveChart
          title="Students by Level"
          description="Distribution of students"
          data={dashboardData?.studentStats?.studentsByLevel || []}
          xKey="level"
          yKeys={[{ key: "count", name: "Students", color: "#4f46e5" }]}
          type="pie"
          isLoading={isLoadingData}
          actions={true}
        />
      </div>
    </div>
  );
};

export default SecretaryDashboard;
