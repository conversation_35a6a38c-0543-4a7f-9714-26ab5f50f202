// serve/src/types/class.types.ts
import { Types } from 'mongoose';

// Schedule types
export type DayOfWeek = 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday';

export const validClassFields = [
    'name',
    'level',
    'room',
    'capacity',
    'status',
    'startDate',
    'endDate',
    'currentStudentCount'
] as const;

export type ValidClassField = typeof validClassFields[number];

export interface TeacherReplacementHistory {
    originalTeacherId: Types.ObjectId;
    newTeacherId: Types.ObjectId;
    replacementDate: Date;
    reason: string;
    approvedBy: Types.ObjectId;
    scheduleAffected: TeacherSchedule[];
}

export interface TeacherSchedule {
    day: DayOfWeek;
    timeStart: string; // Format: "HH:mm"
    timeEnd: string;   // Format: "HH:mm"
}

export interface TeacherAssignment {
    teacherId: Types.ObjectId;
    schedule: TeacherSchedule[];
}

// Student history tracking
export interface StudentHistoryEntry {
    studentId: Types.ObjectId;
    joinDate: Date;
    leaveDate?: Date;
    reason?: string;
}

// Makeup classes
export interface MakeupClass {
    originalDate: Date;
    makeupDate: Date;
    reason: string;
    approvedBy: Types.ObjectId;
    status: 'scheduled' | 'completed' | 'cancelled';
}

// Class merging
export interface ClassMergeHistory {
    mergedWith: Types.ObjectId;
    date: Date;
    reason: string;
    approvedBy: Types.ObjectId;
}

export interface TeacherTransition {
    oldTeacherId: Types.ObjectId;
    newTeacherId: Types.ObjectId;
    startDate: Date;
    endDate?: Date;
    status: 'pending' | 'in_progress' | 'completed';
    notes?: string;
    materials: Array<{
        note?: string;
        documentUrl?: string;
        uploadedAt: Date;
    }>;
}

// Main class interface
export interface IClass {
    _id?: Types.ObjectId;
    name: string;
    level: string;
    teachers: TeacherAssignment[];
    room: string;
    capacity: number;
    currentStudentCount: number;
    startDate: Date;
    endDate: Date;
    status: 'active' | 'inactive' | 'merged';
    mergeHistory: ClassMergeHistory[];
    studentHistory: StudentHistoryEntry[];
    makeupClasses: MakeupClass[];
    teacherReplacementHistory: TeacherReplacementHistory[];
    teacherTransitions: TeacherTransition[];
    createdAt?: Date;
    updatedAt?: Date;
}

// Query options for fetching classes
export interface ClassQueryOptions {
    page?: number;
    limit?: number;
    sortBy?: 'name' | 'level' | 'startDate' | 'endDate' | 'currentStudentCount' | 'status';  // Explicit list of sortable fields
    sortOrder?: 'asc' | 'desc';
    status?: 'active' | 'inactive' | 'merged';
    level?: string;
    teacherId?: string;
    search?: string;
    room?: string;
    fromDate?: Date;
    toDate?: Date;
    hasCapacity?: boolean;
}

// DTO for creating a new class
export interface CreateClassDTO {
    name: string;
    level: string;
    teachers: {
        teacherId: string;
        schedule: {
            day: DayOfWeek;
            timeStart: string;
            timeEnd: string;
        }[];
    }[];
    room: string;
    capacity: number;
    startDate: Date;
    endDate: Date;
}

// DTO for updating a class
export interface UpdateClassDTO {
    name?: string;
    level?: string;
    teachers?: {
        teacherId: string;
        schedule: {
            day: DayOfWeek;
            timeStart: string;
            timeEnd: string;
        }[];
    }[];
    room?: string;
    capacity?: number;
    startDate?: Date;
    endDate?: Date;
    status?: 'active' | 'inactive';
}

// DTO for scheduling makeup classes
export interface ScheduleMakeupClassDTO {
    originalDate: Date;
    makeupDate: Date;
    reason: string;
    teacherId: string;
}

// DTO for class merging
export interface MergeClassDTO {
    targetClassId: string;
    reason: string;
}

// New DTO for teacher replacement
export interface TeacherReplacementDTO {
    originalTeacherId: string;
    newTeacherId: string;
    reason: string;
    effectiveDate: Date;
    scheduleToReplace: TeacherSchedule[];
}

export interface SplitClassDTO {
    name: string;
    level: string;
    room: string;
    capacity: number;
    reason: string;
    studentIds: string[]; // Students to move to the new class
    teacherAssignments?: TeacherAssignment[]; // Optional new teacher assignments
}

export interface TeacherTransitionDTO {
    oldTeacherId: string;
    newTeacherId: string;
    schedulesToTransfer: TeacherSchedule[];
    notes: string;
    transitionStartDate: Date;
    transitionEndDate: Date;
    notifyStudents: boolean;
    materialTransfer?: {
        notes: string;
        documentUrls?: string[];
    };
}

// Add to ClassOperationError interface
export interface ClassSplitError {
    operation: 'split';
    sourceClassId: string;
    reason: string;
    details?: any;
}

// Types for class operations responses
export interface ClassCapacityInfo {
    total: number;
    current: number;
    available: number;
    isFull: boolean;
}

export interface ClassScheduleConflict {
    teacherId: string;
    conflictingClass: {
        classId: string;
        className: string;
        schedule: TeacherSchedule;
    };
}

// Export options for class data
export interface ClassExportOptions {
    format: 'csv' | 'json';
    fields?: ValidClassField[];
    includeStudentHistory?: boolean;
    includeMakeupClasses?: boolean;
    dateRange?: {
        start: Date;
        end: Date;
    };
}

// Bulk operations
export interface ClassBulkOperationDTO {
    classIds: string[];
    operation: 'activate' | 'deactivate' | 'changeLevel' | 'changeRoom';
    newValue?: string;
    reason: string;
}

// Response DTOs
export interface ClassResponseDTO {
    id: string;
    name: string;
    level: string;
    teachers: {
        id: string;
        name: string;
        schedule: TeacherSchedule[];
    }[];
    room: string;
    capacity: {
        total: number;
        current: number;
        available: number;
    };
    schedule: {
        startDate: Date;
        endDate: Date;
        days: {
            day: DayOfWeek;
            times: {
                start: string;
                end: string;
                teacher: string;
            }[];
        }[];
    };
    status: IClass['status'];
}

// Statistics and Analytics
export interface ClassStatistics {
    totalClasses: number;
    activeClasses: number;
    averageCapacityUtilization: number;
    classesAtCapacity: number;
    classesNearCapacity: number;
    averageClassSize: number;
    levelDistribution: {
        level: string;
        count: number;
    }[];
}

// Room Management
export interface RoomAvailability {
    room: string;
    schedule: {
        day: DayOfWeek;
        slots: {
            timeStart: string;
            timeEnd: string;
            isAvailable: boolean;
            classId?: string;  // Make it optional
            className?: string;  // Make it optional
        }[];
    }[];
    date: Date;  // Add date property
}

// Teacher Schedule
export interface TeacherClassSchedule {
    teacherId: string;
    schedule: {
        day: DayOfWeek;
        classes: {
            classId: string;
            className: string;
            timeStart: string;
            timeEnd: string;
            room: string;
        }[];
    }[];
    totalHours: number;
    classCount: number;
}

// Error types specific to class operations
export interface ClassValidationError {
    field: string;
    message: string;
    code: string;
}

export interface ClassOperationError {
    operation: string;
    classId: string;
    reason: string;
    details?: any;
}

// Event types for class-related notifications
export interface ClassEvent {
    type: 'creation' | 'update' | 'merge' | 'makeup' | 'cancellation';
    classId: string;
    timestamp: Date;
    details: any;
    affectedUsers: string[];
}