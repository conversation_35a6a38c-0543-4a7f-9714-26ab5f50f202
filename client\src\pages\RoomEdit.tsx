
import MainLayout from "@/components/layout/MainLayout";
import RoomCreateForm from "@/components/rooms/RoomCreateForm";
import { hasRole } from "@/lib/auth";
import { Navigate, useParams } from "react-router-dom";

const RoomEdit = () => {
  const { id } = useParams<{ id: string }>();
  
  // Check if user has proper permissions
  const canEditRoom = hasRole(['SuperAdmin', 'Manager']);

  if (!canEditRoom) {
    return <Navigate to="/rooms" replace />;
  }

  return (
    <MainLayout>
      <RoomCreateForm isEditMode={true} roomId={id} />
    </MainLayout>
  );
};

export default RoomEdit;
