import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

export const DB_CONFIG = {
    URI: process.env.MONGODB_URI || 'mongodb://localhost:27017/vertex',
    OPTIONS: {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      autoIndex: true,
      serverSelectionTimeoutMS: 5000,
      socketTimeoutMS: 45000,
    },
    BACKUP: {
      ENABLED: false,
      INTERVAL: 24 * 60 * 60 * 1000, // 24 hours
      PATH: process.env.BACKUP_PATH || './backups',
      RETENTION_DAYS: 30,
      COMPRESSION: true
    }
};
