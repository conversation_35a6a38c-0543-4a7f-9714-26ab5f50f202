
export type StudentStatus = 'active' | 'inactive' | 'pending' | 'archived';
export type PaymentStatus = 'paid' | 'pending' | 'overdue';

export interface ContactInfo {
  phone: string;
  email: string;
  address: string;
}

export interface ParentInfo {
  fullName: string;
  relationship: 'mother' | 'father' | 'guardian' | 'other';
  phone: string;
  email?: string;
  occupation?: string;
  isEmergencyContact: boolean;
}

export interface Payment {
  id: string;
  amount: number;
  date: string;
  nextDueDate?: string;
  method: 'cash' | 'card' | 'transfer' | 'check' | 'other';
  notes?: string;
  status: 'completed' | 'pending' | 'cancelled';
  receiptNumber?: string;
}

export interface LevelChange {
  id: string;
  fromLevel: string;
  toLevel: string;
  date: string;
  reason: string;
  changedBy: string;
}

export interface ClassAssignment {
  id: string;
  classId: string;
  className: string;
  fromDate: string;
  toDate?: string;
  reason?: string;
}

export interface StudentNote {
  id: string;
  content: string;
  createdAt: string;
  createdBy: string;
  isPrivate: boolean;
}

export interface Student {
  id: string;
  firstName: string;
  lastName: string;
  contactInfo: ContactInfo;
  parents?: ParentInfo[];
  status: StudentStatus;
  currentLevel: string;
  currentClass?: {
    id: string;
    name: string;
  };
  registeredAt: string;
  registeredBy: string;
  payments?: Payment[];
  levelHistory?: LevelChange[];
  classHistory?: ClassAssignment[];
  notes?: StudentNote[];
  paymentStatus?: PaymentStatus;
}

export interface StudentFilter {
  page: number;
  limit: number;
  sortBy?: 'name' | 'level' | 'registeredAt' | 'status' | 'paymentStatus';
  sortOrder?: 'asc' | 'desc';
  status?: StudentStatus;
  level?: string;
  search?: string;
  classId?: string;
  fromDate?: string;
  toDate?: string;
  paymentStatus?: PaymentStatus;
}

export interface StudentApiResponse {
  success: boolean;
  data: Student[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}

export interface StudentDetailsApiResponse {
  success: boolean;
  data: Student;
}
