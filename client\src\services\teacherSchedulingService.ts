
import { TeacherScheduleData, TeacherReplacement, TeacherTransition } from "@/types/teacherScheduling";
import { Teacher, TeacherScheduleItem } from "@/types/class";

// Get teacher's schedule
export const getTeacherSchedule = async (teacherId: string): Promise<TeacherScheduleData> => {
  try {
    console.log(`Fetching schedule for teacher: ${teacherId}`);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Mock data for teacher schedule
    const schedule: TeacherScheduleData = {
      teacherId,
      name: "<PERSON>", // This would normally come from the API
      schedule: [
        {
          day: "monday",
          timeStart: "09:00",
          timeEnd: "11:00"
        },
        {
          day: "wednesday",
          timeStart: "13:00",
          timeEnd: "15:00"
        }
      ]
    };
    
    return schedule;
  } catch (error) {
    console.error("Error fetching teacher schedule:", error);
    throw error;
  }
};

// Replace a teacher
export const replaceTeacher = async (
  classId: string, 
  replacementData: TeacherReplacement
): Promise<boolean> => {
  try {
    console.log(`Replacing teacher in class ${classId}:`, replacementData);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 700));
    
    // In a real app, this would be an API call
    return true;
  } catch (error) {
    console.error("Error replacing teacher:", error);
    throw error;
  }
};

// Initiate a teacher transition
export const initiateTeacherTransition = async (
  classId: string,
  transitionData: TeacherTransition
): Promise<boolean> => {
  try {
    console.log(`Initiating teacher transition in class ${classId}:`, transitionData);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 700));
    
    // In a real app, this would be an API call
    return true;
  } catch (error) {
    console.error("Error initiating teacher transition:", error);
    throw error;
  }
};

// Check for scheduling conflicts
export const checkScheduleConflicts = async (
  teacherId: string,
  scheduleItems: TeacherScheduleItem[]
): Promise<TeacherScheduleItem[]> => {
  try {
    console.log(`Checking schedule conflicts for teacher ${teacherId}:`, scheduleItems);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // Mock response - return some conflicts for demonstration
    if (teacherId === "teacher1" && scheduleItems.some(item => item.day === "tuesday")) {
      return scheduleItems.filter(item => item.day === "tuesday");
    }
    
    return [];
  } catch (error) {
    console.error("Error checking schedule conflicts:", error);
    throw error;
  }
};

// Get teacher availability
export const getTeacherAvailability = async (teacherId: string): Promise<TeacherScheduleItem[]> => {
  try {
    console.log(`Fetching availability for teacher: ${teacherId}`);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 400));
    
    // Mock data for teacher availability
    const availability: TeacherScheduleItem[] = [
      {
        day: "monday",
        timeStart: "08:00",
        timeEnd: "17:00"
      },
      {
        day: "tuesday",
        timeStart: "08:00",
        timeEnd: "17:00"
      },
      {
        day: "wednesday",
        timeStart: "08:00",
        timeEnd: "17:00"
      },
      {
        day: "thursday",
        timeStart: "08:00", 
        timeEnd: "17:00"
      },
      {
        day: "friday",
        timeStart: "08:00",
        timeEnd: "15:00"
      }
    ];
    
    return availability;
  } catch (error) {
    console.error("Error fetching teacher availability:", error);
    throw error;
  }
};
