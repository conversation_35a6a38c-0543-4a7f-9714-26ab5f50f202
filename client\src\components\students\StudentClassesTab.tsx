
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Link } from "react-router-dom";
import { cn } from "@/lib/utils";
import { Calendar, Users, ArrowRight, AlertCircle, Plus } from "lucide-react";
import { ClassAssignment } from "@/types/student";

interface StudentClassesTabProps {
  studentId: string;
  currentClass?: {
    id: string;
    name: string;
  };
  classHistory: ClassAssignment[];
}

const StudentClassesTab = ({ 
  studentId, 
  currentClass, 
  classHistory 
}: StudentClassesTabProps) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="space-y-6">
      {/* Current Class */}
      <Card>
        <CardHeader>
          <CardTitle>Current Class</CardTitle>
          <CardDescription>
            Student's current class assignment
          </CardDescription>
        </CardHeader>
        <CardContent>
          {currentClass ? (
            <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Users size={20} className="text-primary" />
                  <h3 className="text-xl font-semibold">{currentClass.name}</h3>
                </div>
                <p className="text-muted-foreground">
                  {classHistory.length > 0 && (
                    <>Assigned since {formatDate(classHistory[classHistory.length - 1].fromDate)}</>
                  )}
                </p>
              </div>
              <Button asChild>
                <Link to={`/students/${studentId}/transfer`}>
                  <ArrowRight size={16} className="mr-2" />
                  Transfer Student
                </Link>
              </Button>
            </div>
          ) : (
            <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
              <div className="p-4 bg-muted/30 rounded-lg flex items-center gap-3 text-muted-foreground">
                <AlertCircle size={20} />
                <span>Student is not currently assigned to any class</span>
              </div>
              <Button asChild>
                <Link to={`/students/${studentId}/transfer`}>
                  <Plus size={16} className="mr-2" />
                  Assign Class
                </Link>
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
      
      {/* Class History */}
      <Card>
        <CardHeader>
          <CardTitle>Class History</CardTitle>
          <CardDescription>
            Record of all class assignments
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Class</TableHead>
                <TableHead>From Date</TableHead>
                <TableHead>To Date</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Reason</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {classHistory.length > 0 ? (
                [...classHistory].reverse().map((assignment) => {
                  const fromDate = new Date(assignment.fromDate);
                  const toDate = assignment.toDate ? new Date(assignment.toDate) : null;
                  
                  // Calculate duration in days, months
                  let duration = "Current";
                  if (toDate) {
                    const diffTime = Math.abs(toDate.getTime() - fromDate.getTime());
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                    
                    if (diffDays < 30) {
                      duration = `${diffDays} day${diffDays !== 1 ? 's' : ''}`;
                    } else {
                      const diffMonths = Math.floor(diffDays / 30);
                      duration = `${diffMonths} month${diffMonths !== 1 ? 's' : ''}`;
                    }
                  }
                  
                  return (
                    <TableRow key={assignment.id}>
                      <TableCell className="font-medium">
                        <Link 
                          to={`/classes/${assignment.classId}`}
                          className="text-primary hover:underline flex items-center gap-2"
                        >
                          <Users size={16} />
                          {assignment.className}
                        </Link>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Calendar size={16} className="text-muted-foreground" />
                          {formatDate(assignment.fromDate)}
                        </div>
                      </TableCell>
                      <TableCell>
                        {assignment.toDate ? (
                          <div className="flex items-center gap-2">
                            <Calendar size={16} className="text-muted-foreground" />
                            {formatDate(assignment.toDate)}
                          </div>
                        ) : (
                          "Current"
                        )}
                      </TableCell>
                      <TableCell>
                        {duration}
                      </TableCell>
                      <TableCell>
                        {assignment.reason || "—"}
                      </TableCell>
                    </TableRow>
                  );
                })
              ) : (
                <TableRow>
                  <TableCell colSpan={5} className="h-24 text-center">
                    No class history records found.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default StudentClassesTab;
