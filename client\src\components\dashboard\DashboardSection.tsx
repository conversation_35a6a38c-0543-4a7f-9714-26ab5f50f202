
import { ReactNode } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";

interface DashboardSectionProps {
  title: string;
  description?: string;
  children: ReactNode;
  className?: string;
  actions?: ReactNode;
  isLoading?: boolean;
}

const DashboardSection = ({
  title,
  description,
  children,
  className,
  actions,
  isLoading = false,
}: DashboardSectionProps) => {
  return (
    <Card className={cn("transition-all duration-200", className)}>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <div>
          {isLoading ? (
            <>
              <Skeleton className="h-6 w-48 mb-1" />
              {description && <Skeleton className="h-4 w-64" />}
            </>
          ) : (
            <>
              <CardTitle>{title}</CardTitle>
              {description && <CardDescription>{description}</CardDescription>}
            </>
          )}
        </div>
        {actions && <div className="flex items-center space-x-2">{actions}</div>}
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="w-full h-full min-h-[100px] flex items-center justify-center">
            <Skeleton className="h-full w-full min-h-[100px]" />
          </div>
        ) : (
          children
        )}
      </CardContent>
    </Card>
  );
};

export default DashboardSection;
