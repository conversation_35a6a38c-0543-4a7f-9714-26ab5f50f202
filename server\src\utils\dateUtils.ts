// server/src/utils/dateUtils.ts

/**
 * Get date range based on a period string
 * @param period The period name (today, yesterday, thisWeek, etc.)
 * @returns Object with startDate and endDate
 */
export function getDateRangeFromPeriod(period: string): { startDate: Date; endDate: Date } {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    switch (period) {
        case 'today':
            return { startDate: today, endDate: tomorrow };
        case 'yesterday': {
            const yesterday = new Date(today);
            yesterday.setDate(yesterday.getDate() - 1);
            return { startDate: yesterday, endDate: today };
        }
        case 'thisWeek': {
            const firstDayOfWeek = new Date(today);
            const day = today.getDay();
            const diff = today.getDate() - day + (day === 0 ? -6 : 1); // Adjust for Sunday
            firstDayOfWeek.setDate(diff);
            return { startDate: firstDayOfWeek, endDate: tomorrow };
        }
        case 'lastWeek': {
            const firstDayOfLastWeek = new Date(today);
            firstDayOfLastWeek.setDate(today.getDate() - today.getDay() - 6);
            const firstDayOfThisWeek = new Date(today);
            firstDayOfThisWeek.setDate(today.getDate() - today.getDay() + 1);
            return { startDate: firstDayOfLastWeek, endDate: firstDayOfThisWeek };
        }
        case 'thisMonth': {
            const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
            return { startDate: firstDayOfMonth, endDate: tomorrow };
        }
        case 'lastMonth': {
            const firstDayOfLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
            const firstDayOfThisMonth = new Date(today.getFullYear(), today.getMonth(), 1);
            return { startDate: firstDayOfLastMonth, endDate: firstDayOfThisMonth };
        }
        case 'thisYear': {
            const firstDayOfYear = new Date(today.getFullYear(), 0, 1);
            return { startDate: firstDayOfYear, endDate: tomorrow };
        }
        default:
            return { startDate: today, endDate: tomorrow };
    }
}

/**
 * Safely convert a value to a Date object
 * @param value The value to convert
 * @returns A valid Date object or the current date if conversion fails
 */
export function toValidDate(value: any): Date {
    if (value instanceof Date) {
        return isNaN(value.getTime()) ? new Date() : value;
    }
    
    if (typeof value === 'string' || typeof value === 'number') {
        const date = new Date(value);
        return isNaN(date.getTime()) ? new Date() : date;
    }
    
    return new Date();
}

/**
 * Check if a value is a valid date string or Date object
 * @param value The value to check
 * @returns True if the value represents a valid date
 */
export function isValidDate(value: any): boolean {
    if (value instanceof Date) {
        return !isNaN(value.getTime());
    }
    
    if (typeof value === 'string' || typeof value === 'number') {
        const date = new Date(value);
        return !isNaN(date.getTime());
    }
    
    return false;
}