//server/src/models/class.model.ts
import mongoose, { Schema, Document, Types, Model } from 'mongoose';
import { IClass, TeacherSchedule, MakeupClass, ClassMergeHistory } from '../types/class.types';
import { AppError } from '../types/error.types';

// Interface for the document (instance methods)
export interface IClassDocument extends Document {
  _id: Types.ObjectId;  // Explicitly declare _id
  name: string;
  level: string;
  teachers: {
    teacherId: Types.ObjectId;
    schedule: TeacherSchedule[];
  }[];
  room: string;
  capacity: number;
  currentStudentCount: number;
  startDate: Date;
  endDate: Date;
  status: 'active' | 'inactive' | 'merged';
  mergeHistory: ClassMergeHistory[];
  studentHistory: {
    studentId: Types.ObjectId;
    joinDate: Date;
    leaveDate?: Date;
    reason?: string;
  }[];
  makeupClasses: (MakeupClass & { status: 'scheduled' | 'completed' | 'cancelled' })[];
  teacherReplacementHistory: {
    originalTeacherId: Types.ObjectId;
    newTeacherId: Types.ObjectId;
    replacementDate: Date;
    reason: string;
    approvedBy: Types.ObjectId;
    scheduleAffected: TeacherSchedule[];
  }[];
  teacherTransitions: Array<{
    oldTeacherId: Types.ObjectId;
    newTeacherId: Types.ObjectId;
    startDate: Date;
    endDate?: Date;
    status: 'pending' | 'in_progress' | 'completed';
    notes?: string;
    materials: Array<{
      note?: string;
      documentUrl?: string;
      uploadedAt: Date;
    }>;
  }>;

  // Instance methods
  checkCapacity(): boolean;
  isTeacherAvailable(teacherId: Types.ObjectId, schedule: TeacherSchedule): Promise<boolean>;
  addStudent(studentId: Types.ObjectId): Promise<void>;
  removeStudent(studentId: Types.ObjectId): Promise<void>;
  scheduleMakeupClass(makeupData: Omit<MakeupClass, 'status'>): Promise<void>;
  merge(targetClassId: Types.ObjectId, reason: string): Promise<void>;
}

// Interface for the model (static methods)
interface IClassModel extends Model<IClassDocument> {
  validateScheduleConflicts(
    teacherId: Types.ObjectId,
    schedule: TeacherSchedule[],
    excludeClassId?: Types.ObjectId
  ): Promise<boolean>;
  findAvailableRooms(schedule: TeacherSchedule[]): Promise<string[]>;
}

const classSchema = new Schema<IClassDocument>({
  name: {
    type: String,
    required: true,
    trim: true,
    index: true
  },
  level: {
    type: String,
    required: true,
    index: true
  },
  teachers: [{
    teacherId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    schedule: [{
      day: {
        type: String,
        enum: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'],
        required: true
      },
      timeStart: {
        type: String,
        required: true,
        validate: {
          validator: (v: string) => /^([01]\d|2[0-3]):([0-5]\d)$/.test(v),
          message: 'Time must be in HH:mm format'
        }
      },
      timeEnd: {
        type: String,
        required: true,
        validate: {
          validator: (v: string) => /^([01]\d|2[0-3]):([0-5]\d)$/.test(v),
          message: 'Time must be in HH:mm format'
        }
      }
    }]
  }],
  room: {
    type: String,
    required: true,
    index: true
  },
  capacity: {
    type: Number,
    required: true,
    min: 1
  },
  currentStudentCount: {
    type: Number,
    default: 0,
    min: 0
  },
  startDate: {
    type: Date,
    required: true
  },
  endDate: {
    type: Date,
    required: true
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'merged'],
    default: 'active',
    index: true
  },
  mergeHistory: [{
    mergedWith: {
      type: Schema.Types.ObjectId,
      ref: 'Class',
      required: true
    },
    date: {
      type: Date,
      required: true
    },
    reason: {
      type: String,
      required: true
    },
    approvedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    }
  }],
  studentHistory: [{
    studentId: {
      type: Schema.Types.ObjectId,
      ref: 'Student',
      required: true
    },
    joinDate: {
      type: Date,
      required: true
    },
    leaveDate: Date,
    reason: String
  }],
  makeupClasses: [{
    originalDate: {
      type: Date,
      required: true
    },
    makeupDate: {
      type: Date,
      required: true
    },
    reason: {
      type: String,
      required: true
    },
    approvedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    status: {
      type: String,
      enum: ['scheduled', 'completed', 'cancelled'],
      default: 'scheduled'
    }
  }],
  teacherReplacementHistory: [{
    originalTeacherId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    newTeacherId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    replacementDate: {
      type: Date,
      required: true
    },
    reason: {
      type: String,
      required: true
    },
    approvedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    scheduleAffected: [{
      day: {
        type: String,
        enum: ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'],
        required: true
      },
      timeStart: {
        type: String,
        required: true
      },
      timeEnd: {
        type: String,
        required: true
      }
    }]
  }],
  teacherTransitions: [{
    oldTeacherId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    newTeacherId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    startDate: {
      type: Date,
      required: true
    },
    endDate: Date,
    status: {
      type: String,
      enum: ['pending', 'in_progress', 'completed'],
      default: 'pending'
    },
    notes: String,
    materials: [{
      note: String,
      documentUrl: String,
      uploadedAt: {
        type: Date,
        default: Date.now
      }
    }]
  }]
}, {
  timestamps: true
});

// Indexes
classSchema.index({ 'teachers.teacherId': 1 });
classSchema.index({ startDate: 1, endDate: 1 });
classSchema.index({ status: 1, level: 1 });
classSchema.index({ 'studentHistory.studentId': 1 });

// Instance methods
classSchema.methods.checkCapacity = function (this: IClassDocument): boolean {
  return this.currentStudentCount < this.capacity;
};

classSchema.methods.isTeacherAvailable = async function (
  this: IClassDocument,
  teacherId: Types.ObjectId,
  schedule: TeacherSchedule
): Promise<boolean> {
  const exists = await (this.constructor as IClassModel).validateScheduleConflicts(
    teacherId,
    [schedule],
    this._id as Types.ObjectId
  );
  return !exists;
};

classSchema.methods.addStudent = async function (
  this: IClassDocument,
  studentId: Types.ObjectId
): Promise<void> {
  if (!this.checkCapacity()) {
    throw new AppError(400, 'Class has reached maximum capacity');
  }

  // Check if student is already in class
  const existingStudent = this.studentHistory.find(
    history => history.studentId.equals(studentId) && !history.leaveDate
  );

  if (existingStudent) {
    throw new AppError(400, 'Student is already in this class');
  }

  // Add student to history
  this.studentHistory.push({
    studentId,
    joinDate: new Date()
  });

  this.currentStudentCount += 1;
  await this.save();
};

classSchema.methods.removeStudent = async function (
  this: IClassDocument,
  studentId: Types.ObjectId
): Promise<void> {
  const activeEnrollment = this.studentHistory.find(
    history => history.studentId.equals(studentId) && !history.leaveDate
  );

  if (!activeEnrollment) {
    throw new AppError(404, 'Student not found in this class');
  }

  activeEnrollment.leaveDate = new Date();
  activeEnrollment.reason = 'Student removed from class';

  this.currentStudentCount -= 1;
  await this.save();
};

classSchema.methods.scheduleMakeupClass = async function (
  this: IClassDocument,
  makeupData: Omit<MakeupClass, 'status'>
): Promise<void> {
  // Validate makeup date is in the future
  if (makeupData.makeupDate <= new Date()) {
    throw new AppError(400, 'Makeup class must be scheduled for a future date');
  }

  // Check for existing makeup class on the same date
  const existingMakeup = this.makeupClasses.find(
    makeup => makeup.makeupDate.getTime() === makeupData.makeupDate.getTime()
  );

  if (existingMakeup) {
    throw new AppError(400, 'A makeup class is already scheduled for this date');
  }

  this.makeupClasses.push({
    ...makeupData,
    status: 'scheduled'
  });

  await this.save();
};

classSchema.methods.merge = async function (
  this: IClassDocument,
  targetClassId: Types.ObjectId,
  reason: string
): Promise<void> {
  const targetClass = await (this.constructor as IClassModel).findById(targetClassId);

  if (!targetClass) {
    throw new AppError(404, 'Target class not found');
  }

  if (targetClass.status !== 'active') {
    throw new AppError(400, 'Cannot merge with an inactive or already merged class');
  }

  // Check combined capacity
  if (this.currentStudentCount + targetClass.currentStudentCount > this.capacity) {
    throw new AppError(400, 'Combined student count exceeds class capacity');
  }

  // Update target class status and history
  targetClass.status = 'merged';
  targetClass.mergeHistory.push({
    mergedWith: this._id as Types.ObjectId,
    date: new Date(),
    reason,
    approvedBy: new Types.ObjectId() // This should be set from the context
  });

  // Move students from target class to this class
  const activeStudents = targetClass.studentHistory.filter(history => !history.leaveDate);

  for (const student of activeStudents) {
    await this.addStudent(student.studentId);
  }

  await Promise.all([this.save(), targetClass.save()]);
};

// Static methods
classSchema.static('validateScheduleConflicts', async function (
  teacherId: Types.ObjectId,
  schedule: TeacherSchedule[],
  excludeClassId?: Types.ObjectId
): Promise<boolean> {
  const query: any = {
    'teachers.teacherId': teacherId,
    status: 'active'
  };

  if (excludeClassId) {
    query._id = { $ne: excludeClassId };
  }

  const classes = await this.find(query);

  for (const classDoc of classes) {
    for (const teacher of classDoc.teachers) {
      if (teacher.teacherId.equals(teacherId)) {
        for (const proposedSlot of schedule) {
          for (const existingSlot of teacher.schedule) {
            if (
              existingSlot.day === proposedSlot.day &&
              ((existingSlot.timeStart <= proposedSlot.timeStart && existingSlot.timeEnd > proposedSlot.timeStart) ||
                (existingSlot.timeStart < proposedSlot.timeEnd && existingSlot.timeEnd >= proposedSlot.timeEnd))
            ) {
              return true; // Conflict found
            }
          }
        }
      }
    }
  }

  return false;
});

classSchema.static('findAvailableRooms', async function (
  schedule: TeacherSchedule[]
): Promise<string[]> {
  const occupiedRooms = await this.find({
    status: 'active',
    'teachers.schedule': {
      $elemMatch: {
        $or: schedule.map(slot => ({
          day: slot.day,
          $or: [
            {
              timeStart: { $lte: slot.timeStart },
              timeEnd: { $gt: slot.timeStart }
            },
            {
              timeStart: { $lt: slot.timeEnd },
              timeEnd: { $gte: slot.timeEnd }
            }
          ]
        }))
      }
    }
  }).distinct('room');

  // Query the actual Rooms collection
  const Room = mongoose.model('Room');
  const allRooms = await Room.find({ status: 'active' }).distinct('name');

  return allRooms.filter(room => !occupiedRooms.includes(room));
});

// Middleware
classSchema.pre('save', async function (this: IClassDocument) {
  if (this.isModified('teachers')) {
    // Validate teacher schedules don't conflict
    for (const teacher of this.teachers) {
      const hasConflict = await (this.constructor as IClassModel).validateScheduleConflicts(
        teacher.teacherId,
        teacher.schedule,
        this._id as Types.ObjectId // it was just this._id so you know what I changed
      );

      if (hasConflict) {
        throw new AppError(400, 'Teacher schedule conflicts with existing classes');
      }
    }
  }

  if (this.isModified('currentStudentCount') && this.currentStudentCount > this.capacity) {
    throw new AppError(400, 'Current student count cannot exceed capacity');
  }
});

export const Class = mongoose.model<IClassDocument, IClassModel>('Class', classSchema);