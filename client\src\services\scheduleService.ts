// client/src/services/scheduleService.ts
import { apiClient } from '@/lib/api';

export interface ScheduleItem {
  id: string;
  className: string;
  classLevel: string;
  timeStart: string;
  timeEnd: string;
  room: string;
  roomId: string;
  teacher: string;
  teacherId: string;
  date: string;
  status: string;
}

export interface ScheduleFilters {
  teacherId?: string;
  roomId?: string;
  classLevel?: string;
}

// Get daily schedule
export const getDailySchedule = async (
  date: string,
  filters?: ScheduleFilters
): Promise<ScheduleItem[]> => {
  try {
    const params: any = { date };
    if (filters?.teacherId) params.teacherId = filters.teacherId;
    if (filters?.roomId) params.roomId = filters.roomId;
    if (filters?.classLevel) params.classLevel = filters.classLevel;

    const response = await apiClient.get<{ success: boolean; data: ScheduleItem[] }>(
      '/schedule/daily',
      params
    );

    return response.data;
  } catch (error) {
    console.error('Error fetching daily schedule:', error);
    throw error;
  }
};

// Get weekly schedule
export const getWeeklySchedule = async (
  startDate: string,
  filters?: ScheduleFilters
): Promise<ScheduleItem[]> => {
  try {
    const params: any = { startDate };
    if (filters?.teacherId) params.teacherId = filters.teacherId;
    if (filters?.roomId) params.roomId = filters.roomId;
    if (filters?.classLevel) params.classLevel = filters.classLevel;

    const response = await apiClient.get<{ success: boolean; data: ScheduleItem[] }>(
      '/schedule/weekly',
      params
    );

    return response.data;
  } catch (error) {
    console.error('Error fetching weekly schedule:', error);
    throw error;
  }
};

// Get schedule by date range
export const getScheduleByRange = async (
  startDate: string,
  endDate: string,
  filters?: ScheduleFilters
): Promise<ScheduleItem[]> => {
  try {
    const params: any = { startDate, endDate };
    if (filters?.teacherId) params.teacherId = filters.teacherId;
    if (filters?.roomId) params.roomId = filters.roomId;
    if (filters?.classLevel) params.classLevel = filters.classLevel;

    const response = await apiClient.get<{ success: boolean; data: ScheduleItem[] }>(
      '/schedule/range',
      params
    );

    return response.data;
  } catch (error) {
    console.error('Error fetching schedule by range:', error);
    throw error;
  }
};

// Get teacher schedule
export const getTeacherSchedule = async (
  teacherId: string,
  startDate?: string,
  endDate?: string
): Promise<ScheduleItem[]> => {
  try {
    const params: any = {};
    if (startDate) params.startDate = startDate;
    if (endDate) params.endDate = endDate;

    const response = await apiClient.get<{ success: boolean; data: ScheduleItem[] }>(
      `/schedule/teacher/${teacherId}`,
      params
    );

    return response.data;
  } catch (error) {
    console.error('Error fetching teacher schedule:', error);
    throw error;
  }
};

// Get room schedule
export const getRoomSchedule = async (
  roomId: string,
  startDate?: string,
  endDate?: string
): Promise<ScheduleItem[]> => {
  try {
    const params: any = {};
    if (startDate) params.startDate = startDate;
    if (endDate) params.endDate = endDate;

    const response = await apiClient.get<{ success: boolean; data: ScheduleItem[] }>(
      `/schedule/room/${roomId}`,
      params
    );

    return response.data;
  } catch (error) {
    console.error('Error fetching room schedule:', error);
    throw error;
  }
};

// Get class schedule
export const getClassSchedule = async (
  classId: string,
  startDate?: string,
  endDate?: string,
  includeMakeup?: boolean
): Promise<ScheduleItem[]> => {
  try {
    const params: any = {};
    if (startDate) params.startDate = startDate;
    if (endDate) params.endDate = endDate;
    if (includeMakeup !== undefined) params.includeMakeup = includeMakeup;

    const response = await apiClient.get<{ success: boolean; data: ScheduleItem[] }>(
      `/schedule/class/${classId}`,
      params
    );

    return response.data;
  } catch (error) {
    console.error('Error fetching class schedule:', error);
    throw error;
  }
};
