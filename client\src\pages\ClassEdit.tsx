
import MainLayout from "@/components/layout/MainLayout";
import ClassCreateForm from "@/components/classes/ClassCreateForm";
import { hasRole } from "@/lib/auth";
import { Navigate } from "react-router-dom";

const ClassEdit = () => {
  // Check if user has proper permissions
  const canEditClass = hasRole(['SuperAdmin', 'Manager']);

  if (!canEditClass) {
    return <Navigate to="/classes" replace />;
  }

  return (
    <MainLayout>
      <ClassCreateForm isEditMode={true} />
    </MainLayout>
  );
};

export default ClassEdit;
