import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Bar<PERSON><PERSON>, Users, BookOpen, CalendarClock } from "lucide-react"; // Replace Chart with BarChart or another appropriate icon
import RecentNotesList from "./RecentNotesList";
import CreateNoteDialog from "./CreateNoteDialog";
import { getCurrentUser } from "@/lib/auth";
import { canCreateNotes } from "@/lib/noteUtils";

interface NotesSummary {
  totalNotes: number;
  recentNotes: {
    student: any[];
    class: any[];
  };
  topStudents: {
    id: string;
    name: string;
    noteCount: number;
  }[];
  topClasses: {
    id: string;
    name: string;
    noteCount: number;
  }[];
  activityByDate: {
    date: string;
    count: number;
  }[];
}

interface NotesDashboardProps {
  summary?: NotesSummary;
  isLoading: boolean;
  onStudentSelect: (studentId: string) => void;
  onClassSelect: (classId: string) => void;
}

const NotesDashboard = ({ 
  summary, 
  isLoading, 
  onStudentSelect, 
  onClassSelect 
}: NotesDashboardProps) => {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const user = getCurrentUser();
  
  // Handle note creation
  const handleNoteCreated = () => {
    setIsCreateDialogOpen(false);
  };
  
  return (
    <div className="space-y-6">
      {/* Top Row - Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Total Notes Card */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Notes</p>
                {isLoading ? (
                  <Skeleton className="h-9 w-16 mt-1" />
                ) : (
                  <p className="text-3xl font-bold">{summary?.totalNotes || 0}</p>
                )}
              </div>
              <div className="bg-primary/10 p-3 rounded-full">
                <BarChart className="h-5 w-5 text-primary" /> {/* Changed from ChartIcon */}
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Top Students Card */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Students with Notes</p>
                {isLoading ? (
                  <Skeleton className="h-9 w-16 mt-1" />
                ) : (
                  <p className="text-3xl font-bold">{summary?.topStudents?.length || 0}</p>
                )}
              </div>
              <div className="bg-blue-500/10 p-3 rounded-full">
                <Users className="h-5 w-5 text-blue-500" />
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Top Classes Card */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Classes with Notes</p>
                {isLoading ? (
                  <Skeleton className="h-9 w-16 mt-1" />
                ) : (
                  <p className="text-3xl font-bold">{summary?.topClasses?.length || 0}</p>
                )}
              </div>
              <div className="bg-green-500/10 p-3 rounded-full">
                <BookOpen className="h-5 w-5 text-green-500" />
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Recent Notes */}
        <Card className="lg:col-span-2">
          <CardHeader className="pb-3">
            <div className="flex justify-between items-center">
              <CardTitle>Recent Notes</CardTitle>
              {user && canCreateNotes(user.role) && (
                <Button size="sm" onClick={() => setIsCreateDialogOpen(true)}>
                  Add Note
                </Button>
              )}
            </div>
            <CardDescription>
              Most recent notes added to the system
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="all">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="student">Student</TabsTrigger>
                <TabsTrigger value="class">Class</TabsTrigger>
              </TabsList>
              <TabsContent value="all" className="pt-4">
                <RecentNotesList 
                  notes={[
                    ...(summary?.recentNotes?.student || []), 
                    ...(summary?.recentNotes?.class || [])
                  ].sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()).slice(0, 5)}
                  isLoading={isLoading}
                  onStudentSelect={onStudentSelect}
                  onClassSelect={onClassSelect}
                />
              </TabsContent>
              <TabsContent value="student" className="pt-4">
                <RecentNotesList 
                  notes={summary?.recentNotes?.student || []}
                  isLoading={isLoading}
                  onStudentSelect={onStudentSelect}
                  onClassSelect={onClassSelect}
                />
              </TabsContent>
              <TabsContent value="class" className="pt-4">
                <RecentNotesList 
                  notes={summary?.recentNotes?.class || []}
                  isLoading={isLoading}
                  onStudentSelect={onStudentSelect}
                  onClassSelect={onClassSelect}
                />
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
        
        {/* Top Tables */}
        <div className="space-y-6">
          {/* Top Students */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Students with Most Notes</CardTitle>
              <CardDescription>
                Students with the highest number of notes
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-2">
                  {Array(3).fill(0).map((_, i) => (
                    <Skeleton key={i} className="h-10 w-full" />
                  ))}
                </div>
              ) : (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Student</TableHead>
                        <TableHead className="text-right">Notes</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {(summary?.topStudents || []).length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={2} className="text-center py-4 text-muted-foreground">
                            No data available
                          </TableCell>
                        </TableRow>
                      ) : (
                        (summary?.topStudents || []).slice(0, 5).map((student) => (
                          <TableRow key={student.id}>
                            <TableCell>
                              <button 
                                className="text-blue-600 hover:underline"
                                onClick={() => onStudentSelect(student.id)}
                              >
                                {student.name}
                              </button>
                            </TableCell>
                            <TableCell className="text-right">{student.noteCount}</TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
          
          {/* Top Classes */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle>Classes with Most Notes</CardTitle>
              <CardDescription>
                Classes with the highest number of notes
              </CardDescription>
            </CardHeader>
            <CardContent>
              {isLoading ? (
                <div className="space-y-2">
                  {Array(3).fill(0).map((_, i) => (
                    <Skeleton key={i} className="h-10 w-full" />
                  ))}
                </div>
              ) : (
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Class</TableHead>
                        <TableHead className="text-right">Notes</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {(summary?.topClasses || []).length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={2} className="text-center py-4 text-muted-foreground">
                            No data available
                          </TableCell>
                        </TableRow>
                      ) : (
                        (summary?.topClasses || []).slice(0, 5).map((classItem) => (
                          <TableRow key={classItem.id}>
                            <TableCell>
                              <button 
                                className="text-blue-600 hover:underline"
                                onClick={() => onClassSelect(classItem.id)}
                              >
                                {classItem.name}
                              </button>
                            </TableCell>
                            <TableCell className="text-right">{classItem.noteCount}</TableCell>
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
      
      <CreateNoteDialog
        open={isCreateDialogOpen}
        onOpenChange={setIsCreateDialogOpen}
        onNoteCreated={handleNoteCreated}
      />
    </div>
  );
};

export default NotesDashboard;
