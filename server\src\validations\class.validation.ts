// server/src/validations/class.validation.ts
import <PERSON><PERSON> from 'joi';
import { DayOfWeek } from '../types/class.types';

// Reusable sub-schemas
const timePattern = /^([01]\d|2[0-3]):([0-5]\d)$/;

const scheduleSchema = Joi.object({
  day: Joi.string()
    .valid('monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday')
    .required()
    .messages({
      'any.only': 'Invalid day of week',
      'any.required': 'Day is required'
    }),
  timeStart: Joi.string()
    .pattern(timePattern)
    .required()
    .messages({
      'string.pattern.base': 'Time must be in HH:mm format (24-hour)',
      'any.required': 'Start time is required'
    }),
  timeEnd: Joi.string()
    .pattern(timePattern)
    .required()
    .custom((value, helpers) => {
      const { timeStart } = helpers.state.ancestors[0];
      if (timeStart && value <= timeStart) {
        return helpers.error('any.invalid');
      }
      return value;
    })
    .messages({
      'string.pattern.base': 'Time must be in HH:mm format (24-hour)',
      'any.required': 'End time is required',
      'any.invalid': 'End time must be after start time'
    })
});

const teacherAssignmentSchema = Joi.object({
  teacherId: Joi.string()
    .pattern(/^[0-9a-fA-F]{24}$/)
    .required()
    .messages({
      'string.pattern.base': 'Invalid teacher ID format',
      'any.required': 'Teacher ID is required'
    }),
  schedule: Joi.array()
    .items(scheduleSchema)
    .min(1)
    .unique((a, b) => a.day === b.day &&
      ((a.timeStart <= b.timeStart && a.timeEnd > b.timeStart) ||
        (a.timeStart < b.timeEnd && a.timeEnd >= b.timeEnd)))
    .required()
    .messages({
      'array.min': 'At least one schedule entry is required',
      'array.unique': 'Schedule contains overlapping time slots',
      'any.required': 'Schedule is required'
    })
});

export const classValidation = {
  // GET /api/classes query parameters
  getClassesQuery: Joi.object({
    page: Joi.number()
      .min(1)
      .messages({
        'number.min': 'Page number must be greater than 0'
      }),
    limit: Joi.number()
      .min(1)
      .max(100)
      .messages({
        'number.min': 'Limit must be greater than 0',
        'number.max': 'Limit cannot exceed 100'
      }),
    sortBy: Joi.string()
      .valid('name', 'level', 'startDate', 'endDate', 'currentStudentCount', 'status')
      .messages({
        'any.only': 'Invalid sort field'
      }),
    sortOrder: Joi.string()
      .valid('asc', 'desc')
      .messages({
        'any.only': 'Sort order must be either asc or desc'
      }),
    status: Joi.string()
      .valid('active', 'inactive', 'merged')
      .messages({
        'any.only': 'Invalid status'
      }),
    level: Joi.string(),
    teacherId: Joi.string()
      .pattern(/^[0-9a-fA-F]{24}$/)
      .messages({
        'string.pattern.base': 'Invalid teacher ID format'
      }),
    search: Joi.string()
      .min(1)
      .max(50)
      .messages({
        'string.min': 'Search term must not be empty',
        'string.max': 'Search term cannot exceed 50 characters'
      }),
    room: Joi.string(),
    fromDate: Joi.date()
      .iso()
      .messages({
        'date.base': 'Invalid date format',
        'date.format': 'Date must be in ISO format'
      }),
    toDate: Joi.date()
      .iso()
      .min(Joi.ref('fromDate'))
      .messages({
        'date.base': 'Invalid date format',
        'date.format': 'Date must be in ISO format',
        'date.min': 'End date must be after start date'
      }),
    hasCapacity: Joi.boolean()
  }),

  // POST /api/classes
  createClass: Joi.object({
    name: Joi.string()
      .min(3)
      .max(100)
      .required()
      .messages({
        'string.min': 'Name must be at least 3 characters long',
        'string.max': 'Name cannot exceed 100 characters',
        'any.required': 'Class name is required'
      }),
    level: Joi.string()
      .required()
      .messages({
        'any.required': 'Level is required'
      }),
    teachers: Joi.array()
      .items(teacherAssignmentSchema)
      .min(1)
      .required()
      .messages({
        'array.min': 'At least one teacher must be assigned',
        'any.required': 'Teachers are required'
      }),
    room: Joi.string()
      .required()
      .messages({
        'any.required': 'Room is required'
      }),
    capacity: Joi.number()
      .min(1)
      .max(100)
      .required()
      .messages({
        'number.min': 'Capacity must be at least 1',
        'number.max': 'Capacity cannot exceed 100',
        'any.required': 'Capacity is required'
      }),
    startDate: Joi.date()
      .iso()
      .min('now')
      .required()
      .messages({
        'date.base': 'Invalid start date',
        'date.min': 'Start date must be in the future',
        'any.required': 'Start date is required'
      }),
    endDate: Joi.date()
      .iso()
      .min(Joi.ref('startDate'))
      .required()
      .messages({
        'date.base': 'Invalid end date',
        'date.min': 'End date must be after start date',
        'any.required': 'End date is required'
      })
  }),

  // PATCH /api/classes/:id
  updateClass: Joi.object({
    name: Joi.string()
      .min(3)
      .max(100),
    level: Joi.string(),
    teachers: Joi.array()
      .items(teacherAssignmentSchema)
      .min(1),
    room: Joi.string(),
    capacity: Joi.number()
      .min(1)
      .max(100)
      .custom((value, helpers) => {
        const { currentStudentCount } = helpers.state.ancestors[0];
        if (currentStudentCount && value < currentStudentCount) {
          return helpers.error('number.min');
        }
        return value;
      })
      .messages({
        'number.min': 'New capacity cannot be less than current student count'
      }),
    startDate: Joi.date().iso(),
    endDate: Joi.date()
      .iso()
      .min(Joi.ref('startDate')),
    status: Joi.string()
      .valid('active', 'inactive')
  }).min(1),

  // POST /api/classes/:id/makeup
  scheduleMakeupClass: Joi.object({
    originalDate: Joi.date()
      .iso()
      .required()
      .messages({
        'date.base': 'Invalid original date',
        'any.required': 'Original date is required'
      }),
    makeupDate: Joi.date()
      .iso()
      .min('now')
      .required()
      .messages({
        'date.base': 'Invalid makeup date',
        'date.min': 'Makeup date must be in the future',
        'any.required': 'Makeup date is required'
      }),
    reason: Joi.string()
      .min(10)
      .max(500)
      .required()
      .messages({
        'string.min': 'Reason must be at least 10 characters long',
        'string.max': 'Reason cannot exceed 500 characters',
        'any.required': 'Reason is required'
      }),
    teacherId: Joi.string()
      .pattern(/^[0-9a-fA-F]{24}$/)
      .required()
      .messages({
        'string.pattern.base': 'Invalid teacher ID format',
        'any.required': 'Teacher ID is required'
      })
  }),

  // POST /api/classes/:id/merge
  mergeClass: Joi.object({
    targetClassId: Joi.string()
      .pattern(/^[0-9a-fA-F]{24}$/)
      .required()
      .messages({
        'string.pattern.base': 'Invalid target class ID format',
        'any.required': 'Target class ID is required'
      }),
    reason: Joi.string()
      .min(10)
      .max(500)
      .required()
      .messages({
        'string.min': 'Reason must be at least 10 characters long',
        'string.max': 'Reason cannot exceed 500 characters',
        'any.required': 'Reason is required'
      })
  }),

  // POST /api/classes/bulk
  bulkOperation: Joi.object({
    classIds: Joi.array()
      .items(
        Joi.string()
          .pattern(/^[0-9a-fA-F]{24}$/)
          .messages({
            'string.pattern.base': 'Invalid class ID format'
          })
      )
      .min(1)
      .max(50)
      .unique()
      .required()
      .messages({
        'array.min': 'At least one class ID must be provided',
        'array.max': 'Cannot process more than 50 classes at once',
        'array.unique': 'Duplicate class IDs are not allowed',
        'any.required': 'Class IDs are required'
      }),
    operation: Joi.string()
      .valid('activate', 'deactivate', 'changeLevel', 'changeRoom')
      .required()
      .messages({
        'any.only': 'Invalid operation specified',
        'any.required': 'Operation type is required'
      }),
    newValue: Joi.when('operation', {
      is: Joi.string().valid('changeLevel', 'changeRoom'),
      then: Joi.string().required(),
      otherwise: Joi.forbidden()
    }).messages({
      'any.required': 'New value is required for level or room change'
    }),
    reason: Joi.string()
      .min(10)
      .max(500)
      .required()
      .messages({
        'string.min': 'Reason must be at least 10 characters long',
        'string.max': 'Reason cannot exceed 500 characters',
        'any.required': 'Reason is required'
      })
  }),

  // GET /api/classes/export
  exportClasses: Joi.object({
    format: Joi.string()
      .valid('csv', 'json')
      .default('csv')
      .messages({
        'any.only': 'Export format must be either csv or json'
      }),
    fields: Joi.string()
      .pattern(/^[a-zA-Z_]+(,[a-zA-Z_]+)*$/)
      .messages({
        'string.pattern.base': 'Invalid fields format. Use comma-separated field names'
      }),
    includeStudentHistory: Joi.boolean(),
    includeMakeupClasses: Joi.boolean(),
    fromDate: Joi.date().iso(),
    toDate: Joi.date()
      .iso()
      .min(Joi.ref('fromDate'))
  }),

  // Common id parameter validation
  idParam: Joi.object({
    id: Joi.string()
      .pattern(/^[0-9a-fA-F]{24}$/)
      .required()
      .messages({
        'string.pattern.base': 'Invalid ID format',
        'any.required': 'ID is required'
      })
  }),

  teacherReplacement: Joi.object({
    originalTeacherId: Joi.string()
      .pattern(/^[0-9a-fA-F]{24}$/)
      .required()
      .messages({
        'string.pattern.base': 'Invalid original teacher ID format',
        'any.required': 'Original teacher ID is required'
      }),
    newTeacherId: Joi.string()
      .pattern(/^[0-9a-fA-F]{24}$/)
      .required()
      .messages({
        'string.pattern.base': 'Invalid new teacher ID format',
        'any.required': 'New teacher ID is required'
      }),
    reason: Joi.string()
      .min(10)
      .max(500)
      .required()
      .messages({
        'string.min': 'Reason must be at least 10 characters long',
        'string.max': 'Reason cannot exceed 500 characters',
        'any.required': 'Reason for teacher replacement is required'
      }),
    effectiveDate: Joi.date()
      .iso()
      .min('now')
      .required()
      .messages({
        'date.base': 'Invalid effective date',
        'date.min': 'Effective date must be in the future',
        'any.required': 'Effective date is required'
      }),
    scheduleToReplace: Joi.array()
      .items(scheduleSchema) // Make sure to use the scheduleSchema defined above
      .min(1)
      .required()
      .messages({
        'array.min': 'At least one schedule entry is required',
        'any.required': 'Schedules to replace are required'
      })
  }),

  // POST /api/classes/:id/split
  splitClass: Joi.object({
    name: Joi.string()
      .min(3)
      .max(100)
      .required()
      .messages({
        'string.min': 'Name must be at least 3 characters long',
        'string.max': 'Name cannot exceed 100 characters',
        'any.required': 'New class name is required'
      }),
    level: Joi.string(),
    room: Joi.string()
      .required()
      .messages({
        'any.required': 'Room is required'
      }),
    capacity: Joi.number()
      .min(1)
      .max(100)
      .required()
      .messages({
        'number.min': 'Capacity must be at least 1',
        'number.max': 'Capacity cannot exceed 100',
        'any.required': 'Capacity is required'
      }),
    reason: Joi.string()
      .min(10)
      .max(500)
      .required()
      .messages({
        'string.min': 'Reason must be at least 10 characters long',
        'string.max': 'Reason cannot exceed 500 characters',
        'any.required': 'Reason is required'
      }),
    studentIds: Joi.array()
      .items(
        Joi.string()
          .pattern(/^[0-9a-fA-F]{24}$/)
          .messages({
            'string.pattern.base': 'Invalid student ID format'
          })
      )
      .min(1)
      .required()
      .messages({
        'array.min': 'At least one student must be selected',
        'any.required': 'Student IDs are required'
      }),
    teacherAssignments: Joi.array()
      .items(teacherAssignmentSchema)
      .required()
      .messages({
        'any.required': 'Teacher assignments are required when splitting a class'
      })
  }),

  initiateTeacherTransition: Joi.object({
    oldTeacherId: Joi.string()
      .pattern(/^[0-9a-fA-F]{24}$/)
      .required()
      .messages({
        'string.pattern.base': 'Invalid old teacher ID format',
        'any.required': 'Old teacher ID is required'
      }),
    newTeacherId: Joi.string()
      .pattern(/^[0-9a-fA-F]{24}$/)
      .required()
      .messages({
        'string.pattern.base': 'Invalid new teacher ID format',
        'any.required': 'New teacher ID is required'
      }),
    schedulesToTransfer: Joi.array()
      .items(scheduleSchema)
      .min(1)
      .required()
      .messages({
        'array.min': 'At least one schedule must be transferred',
        'any.required': 'Schedules to transfer are required'
      }),
    notes: Joi.string()
      .min(10)
      .max(500)
      .required()
      .messages({
        'string.min': 'Notes must be at least 10 characters long',
        'string.max': 'Notes cannot exceed 500 characters',
        'any.required': 'Notes are required'
      }),
    transitionStartDate: Joi.date()
      .iso()
      .required()
      .messages({
        'date.base': 'Invalid transition start date',
        'any.required': 'Transition start date is required'
      }),
    transitionEndDate: Joi.date()
      .iso()
      .min(Joi.ref('transitionStartDate'))
      .required()
      .messages({
        'date.base': 'Invalid transition end date',
        'date.min': 'Transition end date must be after start date',
        'any.required': 'Transition end date is required'
      }),
    notifyStudents: Joi.boolean()
      .default(true),
    materialTransfer: Joi.object({
      notes: Joi.string()
        .min(10)
        .max(1000)
        .required(),
      documentUrls: Joi.array()
        .items(Joi.string().uri())
    })
  }),

};


export default classValidation;