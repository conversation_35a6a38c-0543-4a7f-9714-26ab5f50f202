import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { TeacherScheduleItem } from "@/types/class";
import { TeacherScheduleData, TeacherReplacement } from "@/types/teacherScheduling";
import { capitalizeFirstLetter } from "@/lib/utils";
import { format } from "date-fns";
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon, Check, AlertCircle } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";

interface TeacherReplacementInterfaceProps {
  classId: string;
  teachers: TeacherScheduleData[];
  availableTeachers: TeacherScheduleData[];
  onSave: (replacementData: TeacherReplacement) => void;
  onCancel: () => void;
}

const TeacherReplacementInterface = ({
  classId,
  teachers,
  availableTeachers,
  onSave,
  onCancel,
}: TeacherReplacementInterfaceProps) => {
  const [originalTeacherId, setOriginalTeacherId] = useState("");
  const [newTeacherId, setNewTeacherId] = useState("");
  const [effectiveDate, setEffectiveDate] = useState<Date | undefined>(new Date());
  const [reason, setReason] = useState("");
  const [selectedScheduleSlots, setSelectedScheduleSlots] = useState<Map<string, boolean>>(new Map());
  const [validationErrors, setValidationErrors] = useState<string[]>([]);
  
  useEffect(() => {
    if (originalTeacherId) {
      const teacher = teachers.find(t => t.teacherId === originalTeacherId);
      if (teacher) {
        const newScheduleSlots = new Map<string, boolean>();
        teacher.schedule.forEach((slot, index) => {
          const key = `${slot.day}-${slot.timeStart}-${slot.timeEnd}`;
          newScheduleSlots.set(key, false);
        });
        setSelectedScheduleSlots(newScheduleSlots);
      }
    }
  }, [originalTeacherId, teachers]);
  
  const toggleScheduleSlot = (slot: TeacherScheduleItem) => {
    const key = `${slot.day}-${slot.timeStart}-${slot.timeEnd}`;
    const newSelectedScheduleSlots = new Map(selectedScheduleSlots);
    newSelectedScheduleSlots.set(key, !newSelectedScheduleSlots.get(key));
    setSelectedScheduleSlots(newSelectedScheduleSlots);
  };
  
  const validateForm = (): boolean => {
    const errors: string[] = [];
    
    if (!originalTeacherId) {
      errors.push("Original teacher is required");
    }
    
    if (!newTeacherId) {
      errors.push("New teacher is required");
    }
    
    if (!effectiveDate) {
      errors.push("Effective date is required");
    }
    
    if (!reason.trim()) {
      errors.push("Reason is required");
    }
    
    const hasSelectedSlots = Array.from(selectedScheduleSlots.values()).some(selected => selected);
    if (!hasSelectedSlots) {
      errors.push("At least one schedule slot must be selected");
    }
    
    setValidationErrors(errors);
    return errors.length === 0;
  };
  
  const handleSubmit = () => {
    if (validateForm()) {
      const teacher = teachers.find(t => t.teacherId === originalTeacherId);
      if (!teacher) return;
      
      const scheduleToReplace: TeacherScheduleItem[] = teacher.schedule.filter(slot => {
        const key = `${slot.day}-${slot.timeStart}-${slot.timeEnd}`;
        return selectedScheduleSlots.get(key);
      });
      
      const replacementData: TeacherReplacement = {
        originalTeacherId,
        newTeacherId,
        affectedDays: [],
        scheduleToReplace,
        effectiveDate: effectiveDate ? format(effectiveDate, 'yyyy-MM-dd') : '',
        reason,
      };
      
      onSave(replacementData);
    }
  };
  
  const getAvailableReplacements = () => {
    return availableTeachers.filter(t => t.teacherId !== originalTeacherId);
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Teacher Replacement</CardTitle>
        <CardDescription>
          Replace a teacher with another for specific schedule slots
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div>
          <Label htmlFor="originalTeacher">Original Teacher</Label>
          <Select value={originalTeacherId} onValueChange={setOriginalTeacherId}>
            <SelectTrigger id="originalTeacher">
              <SelectValue placeholder="Select original teacher" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                {teachers.map((teacher) => (
                  <SelectItem key={teacher.teacherId} value={teacher.teacherId}>
                    {teacher.name}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
        
        <div>
          <Label htmlFor="newTeacher">New Teacher</Label>
          <Select 
            value={newTeacherId} 
            onValueChange={setNewTeacherId}
            disabled={!originalTeacherId}
          >
            <SelectTrigger id="newTeacher">
              <SelectValue placeholder="Select replacement teacher" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                {getAvailableReplacements().map((teacher) => (
                  <SelectItem key={teacher.teacherId} value={teacher.teacherId}>
                    {teacher.name}
                  </SelectItem>
                ))}
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
        
        <div className="grid gap-2">
          <Label htmlFor="effectiveDate">Effective Date</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                id="effectiveDate"
                variant={"outline"}
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !effectiveDate && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {effectiveDate ? format(effectiveDate, "PPP") : <span>Pick a date</span>}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={effectiveDate}
                onSelect={setEffectiveDate}
                initialFocus
                className="pointer-events-auto"
              />
            </PopoverContent>
          </Popover>
        </div>
        
        <div>
          <Label htmlFor="reason">Reason for Replacement</Label>
          <Textarea
            id="reason"
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            placeholder="Enter the reason for teacher replacement"
            className="min-h-[100px]"
          />
        </div>
        
        <div>
          <Label className="mb-2 block">Schedule Slots to Replace</Label>
          {originalTeacherId ? (
            <div className="space-y-2 max-h-[200px] overflow-y-auto pr-2">
              {teachers
                .find(t => t.teacherId === originalTeacherId)
                ?.schedule.map((slot, index) => {
                  const key = `${slot.day}-${slot.timeStart}-${slot.timeEnd}`;
                  return (
                    <div
                      key={index}
                      className="flex items-center space-x-2 p-2 rounded-md border hover:bg-accent"
                    >
                      <Checkbox
                        id={`slot-${index}`}
                        checked={selectedScheduleSlots.get(key) || false}
                        onCheckedChange={() => toggleScheduleSlot(slot)}
                      />
                      <Label
                        htmlFor={`slot-${index}`}
                        className="flex-1 cursor-pointer"
                      >
                        <span className="font-medium">{capitalizeFirstLetter(slot.day)}</span>:{' '}
                        {slot.timeStart} - {slot.timeEnd}
                      </Label>
                    </div>
                  );
                })}
            </div>
          ) : (
            <div className="text-muted-foreground text-sm py-2">
              Select an original teacher to see their schedule slots
            </div>
          )}
        </div>
        
        {validationErrors.length > 0 && (
          <div className="p-3 rounded-md bg-destructive/10 text-destructive space-y-1">
            {validationErrors.map((error, index) => (
              <div key={index} className="flex items-center">
                <AlertCircle className="h-4 w-4 mr-2" />
                <span>{error}</span>
              </div>
            ))}
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button onClick={handleSubmit}>
          <Check className="mr-2 h-4 w-4" />
          Complete Replacement
        </Button>
      </CardFooter>
    </Card>
  );
};

export default TeacherReplacementInterface;
