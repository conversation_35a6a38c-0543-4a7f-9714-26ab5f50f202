// server/src/models/payment.model.ts
import mongoose, { Schema, Document, Types, Model } from 'mongoose';
import { 
    IPayment, 
    PaymentStatus, 
    PaymentMethod, 
    PaymentPeriod 
} from '../types/payment.types';
import { AppError } from '../types/error.types';

// Interface for the document (instance methods)
export interface IPaymentDocument extends Document, Omit<IPayment, '_id'> {
    _id: Types.ObjectId;
    generateReceiptNumber(): Promise<string>;
    validatePayment(): Promise<boolean>;
    voidPayment(userId: Types.ObjectId, reason: string): Promise<void>;
    addModificationRecord(userId: Types.ObjectId, changes: Record<string, any>): Promise<void>;
    calculateRemainingBalance(): number;
}

// Interface for the model (static methods)
interface IPaymentModel extends Model<IPaymentDocument> {
    getStudentPaymentHistory(studentId: Types.ObjectId, startDate?: Date, endDate?: Date): Promise<IPaymentDocument[]>;
    validateDuplicatePayment(studentId: Types.ObjectId, date: Date, amount: number): Promise<boolean>;
    generateUniqueReceiptNumber(): Promise<string>;
}

const paymentSchema = new Schema<IPaymentDocument>(
    {
        studentId: {
            type: Schema.Types.ObjectId,
            ref: 'Student',
            required: true,
            index: true
        },
        amount: {
            type: Number,
            required: true,
            min: [0, 'Amount cannot be negative'],
            validate: {
                validator: Number.isFinite,
                message: 'Invalid amount'
            }
        },
        remainingBalance: {
            type: Number,
            required: true,
            min: [0, 'Remaining balance cannot be negative'],
            validate: {
                validator: Number.isFinite,
                message: 'Invalid remaining balance'
            }
        },
        status: {
            type: String,
            required: true,
            enum: ['pending', 'completed', 'voided'],
            default: 'completed',
            index: true
        },
        method: {
            type: String,
            required: true,
            enum: ['cash', 'bank_transfer', 'other'],
            index: true
        },
        date: {
            type: Date,
            required: true,
            index: true,
            validate: {
                validator: function(value: Date) {
                    return value <= new Date();
                },
                message: 'Payment date cannot be in the future'
            }
        },
        nextDueDate: {
            type: Date,
            validate: {
                validator: function(this: IPaymentDocument, value: Date) {
                    return !value || value > this.date;
                },
                message: 'Next due date must be after payment date'
            },
            index: true
        },
        description: {
            type: String,
            required: true,
            trim: true,
            maxlength: 500
        },
        period: {
            type: String,
            enum: ['monthly', 'quarterly', 'semi_annual', 'annual'],
            index: true
        },
        recordedBy: {
            type: Schema.Types.ObjectId,
            ref: 'User',
            required: true,
            index: true
        },
        recordedAt: {
            type: Date,
            default: Date.now,
            required: true
        },
        modifiedBy: {
            type: Schema.Types.ObjectId,
            ref: 'User'
        },
        modifiedAt: Date,
        modificationHistory: [{
            modifiedBy: {
                type: Schema.Types.ObjectId,
                ref: 'User',
                required: true
            },
            timestamp: {
                type: Date,
                default: Date.now,
                required: true
            },
            changes: {
                field: String,
                oldValue: Schema.Types.Mixed,
                newValue: Schema.Types.Mixed
            }
        }],
        voidedBy: {
            type: Schema.Types.ObjectId,
            ref: 'User'
        },
        voidedAt: Date,
        voidReason: {
            type: String,
            maxlength: 500
        },
        receiptNumber: {
            type: String,
            required: true,
            unique: true,
            index: true
        },
        notes: {
            type: String,
            trim: true,
            maxlength: 1000
        },
        attachments: [{
            type: String,
            validate: {
                validator: function(v: string) {
                    // Basic URL validation
                    try {
                        new URL(v);
                        return true;
                    } catch {
                        return false;
                    }
                },
                message: 'Invalid attachment URL'
            }
        }]
    },
    {
        timestamps: true,
        toJSON: { virtuals: true },
        toObject: { virtuals: true }
    }
);

// Indexes
paymentSchema.index({ studentId: 1, date: -1 });
paymentSchema.index({ studentId: 1, status: 1 });
paymentSchema.index({ nextDueDate: 1, status: 1 });
paymentSchema.index({ 'modificationHistory.modifiedBy': 1 });
paymentSchema.index({ status: 1, date: -1 });

// Virtual for formatted amount
paymentSchema.virtual('formattedAmount').get(function(this: IPaymentDocument) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(this.amount);
});

// Instance methods
paymentSchema.methods.generateReceiptNumber = async function(this: IPaymentDocument): Promise<string> {
    const prefix = 'RCPT';
    const date = new Date().toISOString().slice(0,10).replace(/-/g, '');
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    const receiptNumber = `${prefix}-${date}-${random}`;
    
    // Ensure uniqueness
    const existing = await (this.constructor as IPaymentModel).findOne({ receiptNumber });
    if (existing) {
        return this.generateReceiptNumber(); // Recursively try again
    }
    
    return receiptNumber;
};

paymentSchema.methods.validatePayment = async function(this: IPaymentDocument): Promise<boolean> {
    if (this.status === 'voided') {
        throw new AppError(400, 'Cannot modify a voided payment');
    }

    if (this.amount < 0) {
        throw new AppError(400, 'Payment amount cannot be negative');
    }

    // Check for duplicate payment
    const isDuplicate = await (this.constructor as IPaymentModel).validateDuplicatePayment(
        this.studentId,
        this.date,
        this.amount
    );

    if (isDuplicate) {
        throw new AppError(400, 'Possible duplicate payment detected');
    }

    return true;
};

// paymentSchema.methods.voidPayment = async function(
//     userId: Types.ObjectId,
//     reason: string
// ): Promise<void> {
//     if (this.status === 'voided') {
//         throw new AppError(400, 'Payment is already voided');
//     }

//     const oldStatus = this.status;
//     this.status = 'voided';
//     this.voidedBy = userId;
//     this.voidedAt = new Date();
//     this.voidReason = reason;

//     this.modificationHistory.push({
//         modifiedBy: userId,
//         timestamp: new Date(),
//         changes: {
//             field: 'status',
//             oldValue: oldStatus,
//             newValue: 'voided'
//         }
//     });

//     await this.save();
// };

paymentSchema.methods.voidPayment = async function(
    userId: Types.ObjectId,
    reason: string
): Promise<void> {
    // Don't check status here, let the service handle that
    const oldStatus = this.status;
    
    // Set void-related fields
    this.status = 'voided';
    this.voidedBy = userId;
    this.voidedAt = new Date();
    this.voidReason = reason;

    // Add to modification history
    this.modificationHistory.push({
        modifiedBy: userId,
        timestamp: new Date(),
        changes: {
            field: 'status',
            oldValue: oldStatus,
            newValue: 'voided'
        }
    });

    // Save changes
    await this.save();
};

paymentSchema.methods.addModificationRecord = async function(
    userId: Types.ObjectId,
    changes: Record<string, any>
): Promise<void> {
    this.modificationHistory.push({
        modifiedBy: userId,
        timestamp: new Date(),
        changes: Object.entries(changes).map(([field, { oldValue, newValue }]) => ({
            field,
            oldValue,
            newValue
        }))
    });
    
    this.modifiedBy = userId;
    this.modifiedAt = new Date();
    
    await this.save();
};

paymentSchema.methods.calculateRemainingBalance = function(this: IPaymentDocument): number {
    return Math.max(0, this.remainingBalance);
};

// Static methods
paymentSchema.static('getStudentPaymentHistory', async function(
    studentId: Types.ObjectId,
    startDate?: Date,
    endDate?: Date
): Promise<IPaymentDocument[]> {
    const query: any = { studentId };
    
    if (startDate || endDate) {
        query.date = {};
        if (startDate) query.date.$gte = startDate;
        if (endDate) query.date.$lte = endDate;
    }
    
    return this.find(query)
        .sort({ date: -1 })
        .populate('recordedBy', 'username')
        .populate('modifiedBy', 'username')
        .populate('voidedBy', 'username');
});

paymentSchema.static('validateDuplicatePayment', async function(
    studentId: Types.ObjectId,
    date: Date,
    amount: number
): Promise<boolean> {
    const startOfDay = new Date(date);
    startOfDay.setHours(0, 0, 0, 0);
    
    const endOfDay = new Date(date);
    endOfDay.setHours(23, 59, 59, 999);
    
    const existingPayment = await this.findOne({
        studentId,
        date: { $gte: startOfDay, $lte: endOfDay },
        amount,
        status: { $ne: 'voided' }
    });
    
    return !!existingPayment;
});

paymentSchema.static('generateUniqueReceiptNumber', async function(): Promise<string> {
    const payment = new this();
    return payment.generateReceiptNumber();
});


// paymentSchema.pre('save', async function(this: IPaymentDocument, next) {
//     try {
//         // For new payments
//         if (this.isNew) {
//             if (!this.receiptNumber) {
//                 this.receiptNumber = await this.generateReceiptNumber();
//             }
//             if (this.remainingBalance === undefined) {
//                 this.remainingBalance = this.amount;
//             }
//         }

//         // Validate dates
//         if (this.isModified('date') || this.isModified('nextDueDate')) {
//             if (this.nextDueDate && this.nextDueDate <= this.date) {
//                 throw new AppError(400, 'Next due date must be after payment date');
//             }
//         }

//         // Prevent modifications to voided payments
//         if (!this.isNew && 
//             this.status === 'voided' && 
//             this.isModified() &&
//             !this.isModified('status')) { // Allow status modification for voiding
//             const modifiedFields = this.modifiedPaths();
//             // Allow only specific fields to be modified for voided payments
//             const allowedFields = ['modificationHistory', 'notes', 'voidedBy', 'voidedAt', 'voidReason'];
//             const hasDisallowedModifications = modifiedFields.some(
//                 field => !allowedFields.includes(field)
//             );
            
//             if (hasDisallowedModifications) {
//                 throw new AppError(400, 'Cannot modify voided payment');
//             }
//         }

//         next();
//     } catch (error: any) {
//         if (error instanceof AppError) {
//             next(error);
//         } else if (error instanceof Error) {
//             next(error);
//         } else {
//             next(new Error('Unknown error occurred'));
//         }
//     }
// });

paymentSchema.pre('save', async function(this: IPaymentDocument, next) {
    try {
        // For new payments
        if (this.isNew) {
            if (!this.receiptNumber) {
                this.receiptNumber = await this.generateReceiptNumber();
            }
            if (this.remainingBalance === undefined) {
                this.remainingBalance = this.amount;
            }
        }

        // Validate dates
        if (this.isModified('date') || this.isModified('nextDueDate')) {
            if (this.nextDueDate && this.nextDueDate <= this.date) {
                throw new AppError(400, 'Next due date must be after payment date');
            }
        }

        // For voided payments, only allow specific modifications
        if (this.status === 'voided' && !this.isNew) {
            // Allow modifications during the voiding process
            if (this.isModified('status') && this.modifiedPaths().includes('voidedBy')) {
                return next();
            }

            // Otherwise, prevent modifications to voided payments
            throw new AppError(400, 'Cannot modify voided payment');
        }

        next();
    } catch (error: any) {
        next(error);
    }
});
export const Payment = mongoose.model<IPaymentDocument, IPaymentModel>('Payment', paymentSchema);