
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { 
  Popover,
  PopoverContent,
  PopoverTrigger
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";
import { 
  ChevronDown, 
  Filter, 
  X, 
  Calendar as CalendarIcon
} from "lucide-react";
import { StudentFilter } from "@/types/student";

interface StudentFiltersProps {
  filters: Partial<StudentFilter>;
  onFilterChange: (filters: Partial<StudentFilter>) => void;
}

const StudentFilters = ({ filters, onFilterChange }: StudentFiltersProps) => {
  const [fromDate, setFromDate] = useState<Date | undefined>(
    filters.fromDate ? new Date(filters.fromDate) : undefined
  );
  const [toDate, setToDate] = useState<Date | undefined>(
    filters.toDate ? new Date(filters.toDate) : undefined
  );
  const [isOpen, setIsOpen] = useState(false);

  const handleStatusChange = (status: string) => {
    if (status === "") {
      // Remove status filter if empty
      const { status, ...rest } = filters;
      onFilterChange(rest);
    } else {
      onFilterChange({ ...filters, status: status as any });
    }
  };

  const handleLevelChange = (level: string) => {
    if (level === "") {
      // Remove level filter if empty
      const { level, ...rest } = filters;
      onFilterChange(rest);
    } else {
      onFilterChange({ ...filters, level });
    }
  };

  const handlePaymentStatusChange = (paymentStatus: string) => {
    if (paymentStatus === "") {
      // Remove paymentStatus filter if empty
      const { paymentStatus, ...rest } = filters;
      onFilterChange(rest);
    } else {
      onFilterChange({ ...filters, paymentStatus: paymentStatus as any });
    }
  };

  const handleFromDateChange = (date: Date | undefined) => {
    setFromDate(date);
    if (date) {
      onFilterChange({ ...filters, fromDate: date.toISOString() });
    } else {
      const { fromDate, ...rest } = filters;
      onFilterChange(rest);
    }
  };

  const handleToDateChange = (date: Date | undefined) => {
    setToDate(date);
    if (date) {
      onFilterChange({ ...filters, toDate: date.toISOString() });
    } else {
      const { toDate, ...rest } = filters;
      onFilterChange(rest);
    }
  };

  const handleSearchChange = (search: string) => {
    if (search.trim() === "") {
      const { search, ...rest } = filters;
      onFilterChange(rest);
    } else {
      onFilterChange({ ...filters, search });
    }
  };

  const handleSortChange = (sortBy: string) => {
    onFilterChange({ ...filters, sortBy: sortBy as any });
  };

  const handleSortOrderChange = (sortOrder: "asc" | "desc") => {
    onFilterChange({ ...filters, sortOrder });
  };

  const clearFilters = () => {
    setFromDate(undefined);
    setToDate(undefined);
    onFilterChange({
      page: 1,
      limit: filters.limit,
      sortBy: "name",
      sortOrder: "asc"
    });
    setIsOpen(false);
  };

  // Count active filters (excluding pagination, sorting, and search)
  const activeFilterCount = [
    filters.status,
    filters.level,
    filters.paymentStatus,
    filters.fromDate,
    filters.toDate,
    filters.classId
  ].filter(Boolean).length;

  return (
    <div className="flex flex-col sm:flex-row gap-3 flex-wrap">
      {/* Search Input */}
      <div className="relative flex-1 min-w-[240px]">
        <Input
          placeholder="Search students..."
          value={filters.search || ""}
          onChange={(e) => handleSearchChange(e.target.value)}
          className="w-full"
        />
        {filters.search && (
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-0 top-0 h-full"
            onClick={() => handleSearchChange("")}
          >
            <X size={16} />
          </Button>
        )}
      </div>

      {/* Filter Popover */}
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button 
            variant="outline" 
            className="flex gap-1 items-center whitespace-nowrap"
          >
            <Filter size={16} />
            <span>Filter</span>
            {activeFilterCount > 0 && (
              <span className="ml-1 flex h-5 w-5 items-center justify-center rounded-full bg-primary text-[10px] font-medium text-primary-foreground">
                {activeFilterCount}
              </span>
            )}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-4" align="start">
          <div className="space-y-4">
            <h4 className="font-medium">Filter Students</h4>
            
            {/* Status Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Status</label>
              <Select
                value={filters.status || ""}
                onValueChange={handleStatusChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All Statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Statuses</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="archived">Archived</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {/* Level Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Level</label>
              <Select
                value={filters.level || ""}
                onValueChange={handleLevelChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All Levels" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Levels</SelectItem>
                  <SelectItem value="Beginner">Beginner</SelectItem>
                  <SelectItem value="Intermediate">Intermediate</SelectItem>
                  <SelectItem value="Advanced">Advanced</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {/* Payment Status Filter */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Payment Status</label>
              <Select
                value={filters.paymentStatus || ""}
                onValueChange={handlePaymentStatusChange}
              >
                <SelectTrigger>
                  <SelectValue placeholder="All Payment Statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">All Payment Statuses</SelectItem>
                  <SelectItem value="paid">Paid</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                  <SelectItem value="overdue">Overdue</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            {/* Date Filters */}
            <div className="space-y-2">
              <label className="text-sm font-medium">Registration Date Range</label>
              <div className="grid grid-cols-2 gap-2">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {fromDate ? format(fromDate, 'PP') : <span>From Date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={fromDate}
                      onSelect={handleFromDateChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full justify-start text-left font-normal"
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {toDate ? format(toDate, 'PP') : <span>To Date</span>}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={toDate}
                      onSelect={handleToDateChange}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
            
            {/* Actions */}
            <div className="flex justify-between pt-2">
              <Button 
                variant="outline" 
                size="sm" 
                onClick={clearFilters}
              >
                Clear Filters
              </Button>
              <Button 
                size="sm" 
                onClick={() => setIsOpen(false)}
              >
                Apply Filters
              </Button>
            </div>
          </div>
        </PopoverContent>
      </Popover>

      {/* Sort Options */}
      <Select
        value={filters.sortBy || "name"}
        onValueChange={handleSortChange}
      >
        <SelectTrigger className="w-[160px]">
          <SelectValue placeholder="Sort by" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="name">Name</SelectItem>
          <SelectItem value="level">Level</SelectItem>
          <SelectItem value="registeredAt">Registration Date</SelectItem>
          <SelectItem value="status">Status</SelectItem>
          <SelectItem value="paymentStatus">Payment Status</SelectItem>
        </SelectContent>
      </Select>

      <Button
        variant="ghost"
        size="icon"
        onClick={() => handleSortOrderChange(
          filters.sortOrder === "asc" ? "desc" : "asc"
        )}
        title={filters.sortOrder === "asc" ? "Ascending" : "Descending"}
      >
        <ChevronDown className={`h-4 w-4 transition-transform ${filters.sortOrder === "desc" ? "rotate-180" : ""}`} />
      </Button>
    </div>
  );
};

export default StudentFilters;
