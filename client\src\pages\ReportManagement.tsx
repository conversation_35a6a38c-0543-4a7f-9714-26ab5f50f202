
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import MainLayout from "@/components/layout/MainLayout";
import ReportDashboard from "@/components/reports/ReportDashboard";
import TemplatesManagement from "@/components/reports/TemplatesManagement";
import { getReportOptions, getSavedReports, getReportTemplates } from "@/services/reportService";
import { ReportOption, SavedReport, ReportTemplate } from "@/types/reports";
import { useToast } from "@/components/ui/use-toast";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function ReportManagement() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [reportOptions, setReportOptions] = useState<ReportOption[]>([]);
  const [savedReports, setSavedReports] = useState<SavedReport[]>([]);
  const [templates, setTemplates] = useState<ReportTemplate[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("dashboard");

  // Mock user role - in a real application, this would come from authentication
  const userRole = "superadmin";

  useEffect(() => {
    const loadReportData = async () => {
      try {
        setIsLoading(true);
        const [options, reports, templateList] = await Promise.all([
          getReportOptions(userRole),
          getSavedReports(),
          getReportTemplates()
        ]);
        
        setReportOptions(options);
        setSavedReports(reports);
        setTemplates(templateList);
      } catch (error) {
        console.error("Error loading report data:", error);
        toast({
          title: "Error loading reports",
          description: "Failed to load report data. Please try again.",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };

    loadReportData();
  }, [toast, userRole]);

  const handleCreateReport = (reportId: string) => {
    navigate(`/reports/create?type=${reportId}`);
  };

  const handleViewSavedReport = (reportId: string) => {
    navigate(`/reports/view/${reportId}`);
  };

  const handleUseTemplate = (templateId: string) => {
    navigate(`/reports/create?template=${templateId}`);
  };

  const handleUpdateTemplate = (updatedTemplate: ReportTemplate) => {
    // In a real application, this would call an API to update the template
    setTemplates(prevTemplates => 
      prevTemplates.map(template => 
        template.id === updatedTemplate.id ? updatedTemplate : template
      )
    );
    
    toast({
      title: "Template updated",
      description: "The report template has been updated successfully.",
    });
  };

  const handleDeleteTemplate = (templateId: string) => {
    // In a real application, this would call an API to delete the template
    setTemplates(prevTemplates => 
      prevTemplates.filter(template => template.id !== templateId)
    );
    
    toast({
      title: "Template deleted",
      description: "The report template has been deleted successfully.",
    });
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Reports Management</h1>
          <p className="text-muted-foreground">
            Generate, view, and manage your reports
          </p>
        </div>

        <Tabs
          defaultValue="dashboard"
          value={activeTab}
          onValueChange={setActiveTab}
          className="space-y-4"
        >
          <TabsList>
            <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
            <TabsTrigger value="teacher">Teacher Reports</TabsTrigger>
            <TabsTrigger value="secretary">Secretary Reports</TabsTrigger>
            <TabsTrigger value="manager">Manager Reports</TabsTrigger>
            <TabsTrigger value="templates">Templates</TabsTrigger>
          </TabsList>

          <TabsContent value="dashboard" className="space-y-4">
            <ReportDashboard 
              reportOptions={reportOptions}
              savedReports={savedReports}
              isLoading={isLoading}
              onCreateReport={handleCreateReport}
              onViewSavedReport={handleViewSavedReport}
              userRole={userRole}
            />
          </TabsContent>

          <TabsContent value="teacher" className="space-y-4">
            {activeTab === "teacher" && (
              <ReportTypeSection 
                reportOptions={reportOptions.filter(opt => opt.role && opt.role.includes("teacher"))}
                onCreateReport={handleCreateReport}
                isLoading={isLoading}
              />
            )}
          </TabsContent>

          <TabsContent value="secretary" className="space-y-4">
            {activeTab === "secretary" && (
              <ReportTypeSection 
                reportOptions={reportOptions.filter(opt => opt.role && opt.role.includes("secretary"))}
                onCreateReport={handleCreateReport}
                isLoading={isLoading}
              />
            )}
          </TabsContent>

          <TabsContent value="manager" className="space-y-4">
            {activeTab === "manager" && (
              <ReportTypeSection 
                reportOptions={reportOptions.filter(opt => opt.role && opt.role.includes("manager"))}
                onCreateReport={handleCreateReport}
                isLoading={isLoading}
              />
            )}
          </TabsContent>

          <TabsContent value="templates" className="space-y-4">
            {activeTab === "templates" && (
              <div className="bg-background rounded-lg border p-6">
                <h2 className="text-xl font-semibold mb-6">Report Templates</h2>
                <TemplatesManagement 
                  templates={templates}
                  onUseTemplate={handleUseTemplate}
                  onUpdateTemplate={handleUpdateTemplate}
                  onDeleteTemplate={handleDeleteTemplate}
                />
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
}

interface ReportTypeSectionProps {
  reportOptions: ReportOption[];
  onCreateReport: (id: string) => void;
  isLoading: boolean;
}

function ReportTypeSection({ reportOptions, onCreateReport, isLoading }: ReportTypeSectionProps) {
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {reportOptions.map(option => (
        <ReportOptionCard 
          key={option.id}
          option={option}
          onCreateReport={onCreateReport}
        />
      ))}
    </div>
  );
}

interface ReportOptionCardProps {
  option: ReportOption;
  onCreateReport: (id: string) => void;
}

function ReportOptionCard({ option, onCreateReport }: ReportOptionCardProps) {
  const navigate = useNavigate();
  
  return (
    <div className="bg-card rounded-lg border shadow-sm transition-all hover:shadow-md">
      <div className="p-6 space-y-4">
        <div className="flex items-center gap-2">
          <span className="text-3xl text-primary">
            {/* Use emoji representations instead of icon props for simplicity */}
            {option.icon === "users" && "👥"}
            {option.icon === "graduation-cap" && "🎓"}
            {option.icon === "clipboard-list" && "📋"}
            {option.icon === "calendar" && "📅"}
            {option.icon === "user-plus" && "👤+"}
            {option.icon === "credit-card" && "💳"}
            {option.icon === "alert-circle" && "⚠️"}
            {option.icon === "bar-chart-2" && "📊"}
            {option.icon === "activity" && "📈"}
            {!option.icon && "📄"}
          </span>
          <h3 className="text-lg font-semibold">{option.name}</h3>
        </div>
        
        <p className="text-sm text-muted-foreground">{option.description}</p>
        
        <div className="flex flex-wrap gap-2">
          {option.formats.map(format => (
            <span key={format} className="text-xs bg-muted px-2 py-1 rounded">
              {format.toUpperCase()}
            </span>
          ))}
        </div>
        
        <button
          onClick={() => onCreateReport(option.id)}
          className="w-full bg-primary text-primary-foreground hover:bg-primary/90 py-2 px-4 rounded-md text-sm font-medium"
        >
          Generate Report
        </button>
      </div>
    </div>
  );
}
