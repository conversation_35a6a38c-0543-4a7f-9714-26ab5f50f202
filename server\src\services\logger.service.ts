import mongoose from 'mongoose';

interface ILogEntry {
  severity: 'info' | 'warning' | 'error';
  category: string;
  action: string;
  performedBy: string;
  targetCollection?: string;
  targetId?: string;
  details: any;
  timestamp: Date;
  ipAddress?: string;
  userAgent?: string;
  status: 'success' | 'failed' | 'warning';
}

interface ISystemLog extends ILogEntry, mongoose.Document {}

const systemLogSchema = new mongoose.Schema({
  severity: {
    type: String,
    required: true,
    enum: ['info', 'warning', 'error']
  },
  category: {
    type: String,
    required: true,
    index: true
  },
  action: {
    type: String,
    required: true,
    index: true
  },
  performedBy: {
    type: String,
    required: true,
    index: true
  },
  targetCollection: String,
  targetId: {
    type: String,
    index: true
  },
  details: mongoose.Schema.Types.Mixed,
  timestamp: {
    type: Date,
    required: true,
    index: true
  },
  ipAddress: String,
  userAgent: String,
  status: {
    type: String,
    required: true,
    enum: ['success', 'failed', 'warning']
  }
});

// Add indexes for common queries
systemLogSchema.index({ timestamp: -1, category: 1 });
systemLogSchema.index({ performedBy: 1, timestamp: -1 });
systemLogSchema.index({ category: 1, action: 1, timestamp: -1 });

const SystemLog = mongoose.model<ISystemLog>('SystemLog', systemLogSchema);

export class SystemLogger {
  static async log(entry: ILogEntry): Promise<void> {
    try {
      await SystemLog.create(entry);
    } catch (error) {
      console.error('Failed to write to system log:', error);
      // If logging fails, write to file as backup
      this.writeToBackupLog(entry);
    }
  }

  static async getUserActivities(
    userId: string,
    options: {
      fromDate?: Date;
      toDate?: Date;
      categories?: string[];
      page?: number;
      limit?: number;
      search?: string;
    } = {}
  ) {
    const {
      fromDate,
      toDate,
      categories,
      page = 1,
      limit = 50,
      search
    } = options;

    const query: any = { performedBy: userId };

    if (fromDate || toDate) {
      query.timestamp = {};
      if (fromDate) query.timestamp.$gte = fromDate;
      if (toDate) query.timestamp.$lte = toDate;
    }

    if (categories?.length) {
      query.category = { $in: categories };
    }

    if (search) {
      query.$or = [
        { category: { $regex: search, $options: 'i' } },
        { action: { $regex: search, $options: 'i' } },
        { performedBy: { $regex: search, $options: 'i' } },
        { 'details.message': { $regex: search, $options: 'i' } }
      ];
    }

    const skip = (page - 1) * limit;

    const [activities, total] = await Promise.all([
      SystemLog.find(query)
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(limit)
        .lean(),
      SystemLog.countDocuments(query)
    ]);

    return {
      activities,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    };
  }

  static async getSystemActivities(
    options: {
      severity?: ('info' | 'warning' | 'error')[];
      categories?: string[];
      fromDate?: Date;
      toDate?: Date;
      page?: number;
      limit?: number;
      search?: string;
    } = {}
  ) {
    const {
      severity,
      categories,
      fromDate,
      toDate,
      page = 1,
      limit = 50
    } = options;

    const query: any = {};

    if (severity?.length) {
      query.severity = { $in: severity };
    }

    if (categories?.length) {
      query.category = { $in: categories };
    }

    if (fromDate || toDate) {
      query.timestamp = {};
      if (fromDate) query.timestamp.$gte = fromDate;
      if (toDate) query.timestamp.$lte = toDate;
    }

    const skip = (page - 1) * limit;

    const [activities, total] = await Promise.all([
      SystemLog.aggregate([
        { $match: query },
        { $sort: { timestamp: -1 } },
        { $skip: skip },
        { $limit: limit },
        {
          $lookup: {
            from: 'users',
            localField: 'performedBy',
            foreignField: '_id',
            as: 'user'
          }
        },
        {
          $addFields: {
            performedByUser: {
              $cond: {
                if: { $eq: ['$performedBy', 'system'] },
                then: 'System',
                else: { $arrayElemAt: ['$user.username', 0] }
              }
            }
          }
        }
      ]),
      SystemLog.countDocuments(query)
    ]);

    return {
      activities: activities.map(activity => ({
        ...activity,
        performedBy: activity.performedByUser || activity.performedBy
      })),
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit)
      }
    };
  }

  static async cleanup(retentionDays: number): Promise<void> {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - retentionDays);

    try {
      await SystemLog.deleteMany({
        timestamp: { $lt: cutoffDate },
        severity: { $ne: 'error' }  // Keep error logs longer
      });
    } catch (error) {
      console.error('Failed to cleanup system logs:', error);
    }
  }

  private static writeToBackupLog(entry: ILogEntry): void {
    const backupPath = process.env.LOG_BACKUP_PATH || './logs/backup';
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const filename = `${backupPath}/backup-${timestamp}.json`;

    try {
      const fs = require('fs');
      const path = require('path');

      // Ensure backup directory exists
      fs.mkdirSync(path.dirname(filename), { recursive: true });

      // Write log entry to file
      fs.writeFileSync(
        filename,
        JSON.stringify(entry, null, 2),
        'utf8'
      );
    } catch (error) {
      console.error('Failed to write to backup log:', error);
    }
  }
}