import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { format } from "date-fns";
import { Class } from "@/types/class";
import { hasRole } from "@/lib/auth";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Skeleton } from "@/components/ui/skeleton";
import { ArrowLeft, Edit, PlusCircle, Users, Calendar, Book, List, CalendarClock, Merge, Split } from "lucide-react";
import ClassScheduleSection from "./ClassScheduleSection";
import ClassTeachersSection from "./ClassTeachersSection";
import ClassStudentsTab from "./ClassStudentsTab";
import ClassMakeupTab from "./ClassMakeupTab";
import ClassMergeDialog from "./ClassMergeDialog";
import ClassSplitDialog from "./ClassSplitDialog";
import MakeupClassFormDialog from "./MakeupClassFormDialog";
import { useToast } from "@/hooks/use-toast";

interface ClassDetailsViewProps {
  classData?: Class;
  isLoading: boolean;
}

const ClassDetailsView = ({ classData, isLoading }: ClassDetailsViewProps) => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const canManageClasses = hasRole(["SuperAdmin", "Manager"]);
  const canScheduleMakeup = hasRole(["SuperAdmin", "Manager", "Teacher"]);
  const [activeTab, setActiveTab] = useState("details");
  const [isMergeDialogOpen, setIsMergeDialogOpen] = useState(false);
  const [isSplitDialogOpen, setSplitDialogOpen] = useState(false);
  const [isMakeupDialogOpen, setMakeupDialogOpen] = useState(false);
  
  // Get status badge color
  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'active':
        return "bg-green-100 text-green-800 hover:bg-green-100";
      case 'inactive':
        return "bg-gray-100 text-gray-800 hover:bg-gray-100";
      case 'merged':
        return "bg-blue-100 text-blue-800 hover:bg-blue-100";
      default:
        return "";
    }
  };
  
  // Calculate capacity percentage
  const capacityPercentage = classData 
    ? (classData.capacity.current / classData.capacity.total) * 100 
    : 0;
  
  // Handle successful operation
  const handleOperationSuccess = () => {
    // Refresh the class data
    window.location.reload();
  };
  
  if (isLoading) {
    return <ClassDetailsLoadingSkeleton />;
  }
  
  if (!classData) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold mb-2">Class not found</h2>
        <p className="text-muted-foreground mb-4">
          The class you're looking for doesn't exist or has been removed.
        </p>
        <Button onClick={() => navigate("/classes")}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Return to Classes
        </Button>
      </div>
    );
  }
  
  // Check if class is eligible for operations
  const canMergeOrSplit = classData.status === 'active' && classData.capacity.current > 0;
  
  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <div className="flex items-center">
          <Button
            variant="outline"
            size="icon"
            onClick={() => navigate("/classes")}
            className="mr-4"
            title="Back to classes"
          >
            <ArrowLeft className="h-4 w-4" />
          </Button>
          <div>
            <div className="flex items-center gap-3">
              <h1 className="text-3xl font-bold tracking-tight">{classData.name}</h1>
              <Badge
                variant="outline"
                className={getStatusColor(classData.status)}
              >
                {classData.status.charAt(0).toUpperCase() + classData.status.slice(1)}
              </Badge>
            </div>
            <p className="text-muted-foreground mt-1">
              Level: {classData.level}
            </p>
          </div>
        </div>
        
        {canManageClasses && (
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => navigate(`/classes/${classData.id}/scheduling`)}>
              <CalendarClock className="mr-2 h-4 w-4" />
              Teacher Scheduling
            </Button>
            <Button onClick={() => navigate(`/classes/${classData.id}/edit`)}>
              <Edit className="mr-2 h-4 w-4" />
              Edit Class
            </Button>
          </div>
        )}
      </div>
      
      {/* Tabs Interface */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="details">
            <Book className="mr-2 h-4 w-4" />
            Details
          </TabsTrigger>
          <TabsTrigger value="students">
            <Users className="mr-2 h-4 w-4" />
            Students
          </TabsTrigger>
          <TabsTrigger value="attendance">
            <Calendar className="mr-2 h-4 w-4" />
            Attendance
          </TabsTrigger>
          <TabsTrigger value="makeup">
            <Calendar className="mr-2 h-4 w-4" />
            Makeup Classes
          </TabsTrigger>
          <TabsTrigger value="notes">
            <List className="mr-2 h-4 w-4" />
            Notes
          </TabsTrigger>
        </TabsList>
        
        {/* Details Tab */}
        <TabsContent value="details" className="space-y-6">
          {/* Overview Card */}
          <Card>
            <CardHeader>
              <CardTitle>Class Overview</CardTitle>
              <CardDescription>
                Basic information about this class
              </CardDescription>
            </CardHeader>
            <CardContent className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              <div>
                <h3 className="font-medium text-sm text-muted-foreground mb-2">Room</h3>
                <p className="text-lg font-medium">{classData.room}</p>
              </div>
              
              <div>
                <h3 className="font-medium text-sm text-muted-foreground mb-2">Dates</h3>
                <p className="text-lg font-medium">
                  {format(new Date(classData.schedule.startDate), "MMM d, yyyy")} - {format(new Date(classData.schedule.endDate), "MMM d, yyyy")}
                </p>
              </div>
              
              <div>
                <h3 className="font-medium text-sm text-muted-foreground mb-2">Capacity</h3>
                <div className="space-y-2">
                  <p className="text-lg font-medium">
                    {classData.capacity.current} / {classData.capacity.total} Students
                  </p>
                  <Progress value={capacityPercentage} className="h-2" />
                  <p className="text-sm text-muted-foreground">
                    {classData.capacity.available} spots available
                  </p>
                </div>
              </div>
              
              <div>
                <h3 className="font-medium text-sm text-muted-foreground mb-2">Created On</h3>
                <p className="text-lg font-medium">
                  {classData.createdAt ? format(new Date(classData.createdAt), "MMM d, yyyy") : "N/A"}
                </p>
                <p className="text-sm text-muted-foreground">
                  Last updated: {classData.updatedAt ? format(new Date(classData.updatedAt), "MMM d, yyyy") : "N/A"}
                </p>
              </div>
            </CardContent>
            {canManageClasses && (
              <CardFooter className="flex flex-wrap gap-2 border-t pt-6">
                <Button variant="outline" size="sm" onClick={() => navigate(`/classes/${classData.id}/scheduling`)}>
                  <CalendarClock className="mr-2 h-4 w-4" />
                  Teacher Scheduling
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => setMakeupDialogOpen(true)}
                  disabled={classData.status !== 'active'}
                >
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Schedule Makeup
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => setIsMergeDialogOpen(true)}
                  disabled={!canMergeOrSplit}
                >
                  <Merge className="mr-2 h-4 w-4" />
                  Merge Class
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={() => setSplitDialogOpen(true)}
                  disabled={!canMergeOrSplit}
                >
                  <Split className="mr-2 h-4 w-4" />
                  Split Class
                </Button>
                <Button variant="outline" size="sm" onClick={() => navigate(`/classes/${classData.id}/edit`)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Class
                </Button>
              </CardFooter>
            )}
          </Card>
          
          {/* Class Schedule Section */}
          <ClassScheduleSection schedule={classData.schedule} />
          
          {/* Class Teachers Section */}
          <ClassTeachersSection teachers={classData.teachers} />
        </TabsContent>
        
        {/* Students Tab */}
        <TabsContent value="students">
          <ClassStudentsTab 
            capacity={classData.capacity} 
            studentHistory={classData.studentHistory || []} 
          />
        </TabsContent>
        
        {/* Attendance Tab (Placeholder) */}
        <TabsContent value="attendance">
          <Card>
            <CardHeader>
              <CardTitle>Attendance</CardTitle>
              <CardDescription>
                Track student attendance for this class
              </CardDescription>
            </CardHeader>
            <CardContent className="py-10 text-center text-muted-foreground">
              <p>Attendance tracking functionality will be added in a future update.</p>
            </CardContent>
          </Card>
        </TabsContent>
        
        {/* Makeup Classes Tab */}
        <TabsContent value="makeup">
          <ClassMakeupTab 
            classId={classData.id} 
            makeupClasses={classData.makeupClasses || []}
          />
        </TabsContent>
        
        {/* Notes Tab (Placeholder) */}
        <TabsContent value="notes">
          <Card>
            <CardHeader>
              <CardTitle>Class Notes</CardTitle>
              <CardDescription>
                Notes and important information about this class
              </CardDescription>
            </CardHeader>
            <CardContent className="py-10 text-center text-muted-foreground">
              <p>Class notes functionality will be added in a future update.</p>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      {/* Class Merge Dialog */}
      <ClassMergeDialog 
        isOpen={isMergeDialogOpen} 
        onClose={() => setIsMergeDialogOpen(false)} 
        sourceClass={classData}
        onSuccess={handleOperationSuccess}
      />
      
      {/* Class Split Dialog */}
      <ClassSplitDialog 
        open={isSplitDialogOpen} 
        onOpenChange={setSplitDialogOpen} 
        sourceClass={classData}
        onSuccess={handleOperationSuccess}
      />
      
      {/* Makeup Class Form Dialog */}
      {isMakeupDialogOpen && (
        <MakeupClassFormDialog
          isOpen={isMakeupDialogOpen}
          onClose={() => setMakeupDialogOpen(false)}
          classData={classData}
          onSuccess={handleOperationSuccess}
        />
      )}
    </div>
  );
};

// Loading skeleton component
const ClassDetailsLoadingSkeleton = () => {
  return (
    <div className="space-y-6">
      {/* Header Skeleton */}
      <div className="flex justify-between items-center">
        <div className="flex items-center">
          <Skeleton className="h-10 w-10 rounded-md mr-4" />
          <div>
            <Skeleton className="h-10 w-64 mb-2" />
            <Skeleton className="h-5 w-32" />
          </div>
        </div>
        <Skeleton className="h-10 w-32" />
      </div>
      
      {/* Tabs Skeleton */}
      <Skeleton className="h-10 w-full" />
      
      {/* Content Skeleton */}
      <Card>
        <CardHeader>
          <Skeleton className="h-7 w-40 mb-2" />
          <Skeleton className="h-5 w-60" />
        </CardHeader>
        <CardContent className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <div key={i}>
              <Skeleton className="h-5 w-20 mb-2" />
              <Skeleton className="h-8 w-32" />
            </div>
          ))}
        </CardContent>
      </Card>
      
      {/* Additional Card Skeletons */}
      <Card>
        <CardHeader>
          <Skeleton className="h-7 w-40 mb-2" />
          <Skeleton className="h-5 w-60" />
        </CardHeader>
        <CardContent>
          <Skeleton className="h-40 w-full" />
        </CardContent>
      </Card>
    </div>
  );
};

export default ClassDetailsView;
