// server/src/models/attendance.model.ts
import mongoose, { Schema, Document, Types, Model } from 'mongoose';
import { IAttendance, AttendanceStatus, ExcuseDocument } from '../types/attendance.types';
import { AppError, InvalidAttendanceDateError } from '../types/error.types';

// Interface for individual student attendance record
interface IStudentAttendance {
  studentId: Types.ObjectId;
  status: AttendanceStatus;
  arrivalTime?: Date;
  excuse?: ExcuseDocument;
  notes?: string;
}

// Interface for the document (instance methods)
export interface IAttendanceDocument extends Document {
  _id: Types.ObjectId;
  classId: Types.ObjectId;
  date: Date;
  teacherId: Types.ObjectId;
  students: IStudentAttendance[];
  isMakeupClass: boolean;
  makeupClassId?: Types.ObjectId;
  createdAt: Date;
  modifiedAt: Date;
  lastModifiedBy: Types.ObjectId;
  modificationHistory: Array<{
    modifiedBy: Types.ObjectId;
    timestamp: Date;
    changes: Record<string, any>;
  }>;

  // Instance methods
  markAttendance(
    studentId: Types.ObjectId,
    status: AttendanceStatus,
    modifiedBy: Types.ObjectId,
    options?: {
      arrivalTime?: Date;
      excuse?: ExcuseDocument;
      notes?: string;
    }
  ): Promise<void>;

  bulkMarkAttendance(
    records: Array<{
      studentId: Types.ObjectId;
      status: AttendanceStatus;
      arrivalTime?: Date;
      excuse?: ExcuseDocument;
      notes?: string;
    }>,
    modifiedBy: Types.ObjectId
  ): Promise<void>;

  addExcuse(
    studentId: Types.ObjectId,
    excuse: ExcuseDocument,
    modifiedBy: Types.ObjectId
  ): Promise<void>;

  validateAttendanceTime(date: Date): boolean;
}

// Interface for the model (static methods)
interface IAttendanceModel extends Model<IAttendanceDocument> {
  isValidTimeFormat(timeStr: string): boolean;
  
  getStudentAttendanceRecord(
    studentId: Types.ObjectId,
    startDate: Date,
    endDate: Date
  ): Promise<IAttendanceDocument[]>;

  getClassAttendanceStats(
    classId: Types.ObjectId,
    startDate: Date,
    endDate: Date
  ): Promise<{
    totalDays: number;
    attendanceByStudent: Record<string, {
      present: number;
      absent: number;
      late: number;
      excused: number;
      attendanceRate: number;
    }>;
  }>;
}

const attendanceSchema = new Schema<IAttendanceDocument>(
  {
    classId: {
      type: Schema.Types.ObjectId,
      ref: 'Class',
      required: true,
      index: true
    },
    date: {
      type: Date,
      required: true,
      index: true
    },
    teacherId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true
    },
    students: [{
      studentId: {
        type: Schema.Types.ObjectId,
        ref: 'Student',
        required: true
      },
      status: {
        type: String,
        enum: ['present', 'absent', 'late', 'excused'],
        required: true
      },
      arrivalTime: Date,
      excuse: {
        reason: {
          type: String,
          required: function (this: { excuse?: ExcuseDocument }) {
            return !!this.excuse;
          }
        },
        documentUrl: String,
        verifiedBy: {
          type: Schema.Types.ObjectId,
          ref: 'User'
        },
        verifiedAt: Date,
        status: {
          type: String,
          enum: ['pending', 'approved', 'rejected'],
          default: 'pending'
        }
      },
      notes: String
    }],
    isMakeupClass: {
      type: Boolean,
      default: false
    },
    makeupClassId: {
      type: Schema.Types.ObjectId,
      ref: 'Class'
    },
    createdAt: {
      type: Date,
      default: Date.now
    },
    modifiedAt: {
      type: Date,
      default: Date.now
    },
    lastModifiedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    modificationHistory: [{
      modifiedBy: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true
      },
      timestamp: {
        type: Date,
        default: Date.now
      },
      changes: Schema.Types.Mixed
    }]
  },
  {
    timestamps: true
  }
);

// Main compound indexes for common queries
// Replace existing indexes with these optimized ones:

// Main compound indexes for common queries
attendanceSchema.index({ classId: 1, date: 1 }, { unique: true });
attendanceSchema.index({ 'students.studentId': 1, date: 1 });
attendanceSchema.index({ teacherId: 1, date: 1 });

// Support for date range queries
attendanceSchema.index({ date: 1 });

// Support for status-based queries
attendanceSchema.index({ 'students.status': 1, date: 1 });

// Support for excuse-based queries
attendanceSchema.index({ 'students.excuse.status': 1, date: 1 });

// Support for makeup class queries
attendanceSchema.index({ isMakeupClass: 1, date: 1 });

// Support for modification history queries
attendanceSchema.index({ modifiedAt: -1 });

// Support for class-based student attendance queries
attendanceSchema.index({ classId: 1, 'students.studentId': 1, date: 1 });

// Partial index for active excuses
attendanceSchema.index(
  { 'students.excuse.status': 1 },
  {
    partialFilterExpression: {
      'students.excuse.status': { $exists: true }
    }
  }
);

// Text index for searching excuse reasons
attendanceSchema.index(
  { 'students.excuse.reason': 'text' },
  {
    weights: {
      'students.excuse.reason': 2
    },
    default_language: 'english'
  }
);

// Instance methods
attendanceSchema.methods.markAttendance = async function (
  studentId: Types.ObjectId,
  status: AttendanceStatus,
  modifiedBy: Types.ObjectId,
  options: {
    arrivalTime?: Date;
    excuse?: ExcuseDocument;
    notes?: string;
  } = {}
): Promise<void> {
  const studentRecord = this.students.find((s: IStudentAttendance) =>
    s.studentId.toString() === studentId.toString()
  );

  const oldStatus = studentRecord?.status;
  const changes: Record<string, any> = {};

  if (studentRecord) {
    // Update existing record
    if (studentRecord.status !== status) {
      changes[`students.${studentId}.status`] = {
        from: studentRecord.status,
        to: status
      };
      studentRecord.status = status;
    }

    if (options.arrivalTime) {
      changes[`students.${studentId}.arrivalTime`] = {
        from: studentRecord.arrivalTime,
        to: options.arrivalTime
      };
      studentRecord.arrivalTime = options.arrivalTime;
    }

    if (options.excuse) {
      changes[`students.${studentId}.excuse`] = {
        from: studentRecord.excuse,
        to: options.excuse
      };
      studentRecord.excuse = options.excuse;
    }

    if (options.notes) {
      changes[`students.${studentId}.notes`] = {
        from: studentRecord.notes,
        to: options.notes
      };
      studentRecord.notes = options.notes;
    }
  } else {
    // Add new record
    this.students.push({
      studentId,
      status,
      ...options
    });
    changes[`students.${studentId}`] = {
      from: null,
      to: { status, ...options }
    };
  }

  if (Object.keys(changes).length > 0) {
    this.modificationHistory.push({
      modifiedBy,
      timestamp: new Date(),
      changes
    });
    this.lastModifiedBy = modifiedBy;
    this.modifiedAt = new Date();
  }

  await this.save();
};

attendanceSchema.methods.bulkMarkAttendance = async function (
  records: Array<{
    studentId: Types.ObjectId;
    status: AttendanceStatus;
    arrivalTime?: Date;
    excuse?: ExcuseDocument;
    notes?: string;
  }>,
  modifiedBy: Types.ObjectId
): Promise<void> {
  const changes: Record<string, any> = {};

  records.forEach(record => {
    const studentRecord = this.students.find((s: IStudentAttendance) =>
      s.studentId.toString() === record.studentId.toString()
    );

    if (studentRecord) {
      // Update existing record
      if (studentRecord.status !== record.status) {
        changes[`students.${record.studentId}.status`] = {
          from: studentRecord.status,
          to: record.status
        };
        studentRecord.status = record.status;
      }

      if (record.arrivalTime) {
        changes[`students.${record.studentId}.arrivalTime`] = {
          from: studentRecord.arrivalTime,
          to: record.arrivalTime
        };
        studentRecord.arrivalTime = record.arrivalTime;
      }

      if (record.excuse) {
        changes[`students.${record.studentId}.excuse`] = {
          from: studentRecord.excuse,
          to: record.excuse
        };
        studentRecord.excuse = record.excuse;
      }

      if (record.notes) {
        changes[`students.${record.studentId}.notes`] = {
          from: studentRecord.notes,
          to: record.notes
        };
        studentRecord.notes = record.notes;
      }
    } else {
      // Add new record
      this.students.push(record);
      changes[`students.${record.studentId}`] = {
        from: null,
        to: record
      };
    }
  });

  if (Object.keys(changes).length > 0) {
    this.modificationHistory.push({
      modifiedBy,
      timestamp: new Date(),
      changes
    });
    this.lastModifiedBy = modifiedBy;
    this.modifiedAt = new Date();
  }

  await this.save();
};

attendanceSchema.methods.addExcuse = async function (
  studentId: Types.ObjectId,
  excuse: ExcuseDocument,
  modifiedBy: Types.ObjectId
): Promise<void> {
  const studentRecord = this.students.find((s: IStudentAttendance) =>
    s.studentId.toString() === studentId.toString()
  );

  if (!studentRecord) {
    throw new AppError(404, 'Student attendance record not found');
  }

  const oldExcuse = studentRecord.excuse;
  studentRecord.excuse = excuse;

  // If excuse is approved, update status to excused
  if (excuse.status === 'approved') {
    studentRecord.status = 'excused';
  }

  this.modificationHistory.push({
    modifiedBy,
    timestamp: new Date(),
    changes: {
      [`students.${studentId}.excuse`]: {
        from: oldExcuse,
        to: excuse
      }
    }
  });

  this.lastModifiedBy = modifiedBy;
  this.modifiedAt = new Date();

  await this.save();
};

attendanceSchema.statics.isValidTimeFormat = function(timeStr: string): boolean {
  return /^([01]\d|2[0-3]):([0-5]\d)$/.test(timeStr);
};

attendanceSchema.methods.validateAttendanceTime = async function(date: Date): Promise<boolean> {
  try {
    // Get the class document
    const classDoc = await mongoose.model('Class').findById(this.classId);
    if (!classDoc) {
      throw new AppError(404, 'Class not found');
    }

    // Get day of week in lowercase
    const dayOfWeek = date.toLocaleDateString('en-US', { weekday: 'long' })
      .toLowerCase() as 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday';  

    const hasScheduledClass = classDoc.teachers.some((teacher: { 
      teacherId: Types.ObjectId;
      schedule: Array<{
        day: string;
        timeStart: string;
        timeEnd: string;
      }>;
    }) =>
      teacher.schedule.some((schedule: {
        day: string;
        timeStart: string;
        timeEnd: string;
      }) => {
        if (schedule.day !== dayOfWeek) return false;

        // Validate time format first
        const model = this.constructor as IAttendanceModel;
        if (!model.isValidTimeFormat(schedule.timeStart) || 
            !model.isValidTimeFormat(schedule.timeEnd)) {
          console.warn(`Invalid time format detected: ${schedule.timeStart} or ${schedule.timeEnd}`);
          return false;
        }

        // Convert schedule times to comparable values
        const [startHour, startMinute] = schedule.timeStart.split(':').map(Number);
        const [endHour, endMinute] = schedule.timeEnd.split(':').map(Number);
        const scheduleStart = new Date(date);
        const scheduleEnd = new Date(date);
        
        scheduleStart.setHours(startHour, startMinute, 0, 0);
        scheduleEnd.setHours(endHour, endMinute, 0, 0);

        // Check if the attendance date falls within the scheduled time
        const attendanceTime = new Date(date);
        return attendanceTime >= scheduleStart && attendanceTime <= scheduleEnd;
      })
    );
  
    if (!hasScheduledClass) {
      return false;
    }

    // Check if class is active on this date
    const classStartDate = new Date(classDoc.startDate);
    const classEndDate = new Date(classDoc.endDate);
    classStartDate.setHours(0, 0, 0, 0);
    classEndDate.setHours(23, 59, 59, 999);

    return date >= classStartDate && date <= classEndDate;
  } catch (error) {
    if (error instanceof AppError) throw error;
    throw new AppError(500, 'Error validating attendance time');
  }
};

// Static methods
attendanceSchema.static('getStudentAttendanceRecord', async function (
  studentId: Types.ObjectId,
  startDate: Date,
  endDate: Date
): Promise<IAttendanceDocument[]> {
  return this.find({
    'students.studentId': studentId,
    date: {
      $gte: startDate,
      $lte: endDate
    }
  }).sort({ date: 1 });
});

attendanceSchema.static('getClassAttendanceStats', async function (
  classId: Types.ObjectId,
  startDate: Date,
  endDate: Date
): Promise<{
  totalDays: number;
  attendanceByStudent: Record<string, {
    present: number;
    absent: number;
    late: number;
    excused: number;
    attendanceRate: number;
  }>;
}> {
  const attendanceRecords = await this.find({
    classId,
    date: {
      $gte: startDate,
      $lte: endDate
    }
  });

  const totalDays = attendanceRecords.length;
  const attendanceByStudent: Record<string, {
    present: number;
    absent: number;
    late: number;
    excused: number;
    attendanceRate: number;
  }> = {};

  attendanceRecords.forEach((record: IAttendanceDocument) => {
    record.students.forEach((student: IStudentAttendance) => {
      const studentId = student.studentId.toString();
      if (!attendanceByStudent[studentId]) {
        attendanceByStudent[studentId] = {
          present: 0,
          absent: 0,
          late: 0,
          excused: 0,
          attendanceRate: 0
        };
      }

      // Type assertion to ensure status is used as key
      const status = student.status as keyof typeof attendanceByStudent[typeof studentId];
      if (status !== 'attendanceRate') {
        attendanceByStudent[studentId][status]++;
      }
    });
  });

  // Calculate attendance rates
  Object.values(attendanceByStudent).forEach(stats => {
    const totalAttendance = stats.present + stats.late;
    stats.attendanceRate = totalDays > 0 ? (totalAttendance / totalDays) * 100 : 0;
  });

  return {
    totalDays,
    attendanceByStudent
  };
});

// Middleware
attendanceSchema.pre('save', async function (next) {
  try {
    // Ensure the date is set to midnight for consistent querying
    if (this.isModified('date')) {
      this.date.setHours(0, 0, 0, 0);
    }

    // Validate against class schedule if it's not a makeup class
    if (!this.isMakeupClass && this.isModified('date')) {
      const isValidTime = await this.validateAttendanceTime(this.date);
      if (!isValidTime) {
        throw new InvalidAttendanceDateError('Invalid attendance date for class schedule');
      }
    }

    next();
  } catch (error: any) {
    next(error);
  }
});

export const Attendance = mongoose.model<IAttendanceDocument, IAttendanceModel>(
  'Attendance',
  attendanceSchema
);