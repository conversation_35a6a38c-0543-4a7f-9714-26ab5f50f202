
import { useState } from "react";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  Dialog, 
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import { 
  MessageSquare, 
  Plus, 
  Calendar, 
  User, 
  Lock, 
  Unlock, 
  Trash2, 
  Edit 
} from "lucide-react";
import { StudentNote } from "@/types/student";

interface StudentNotesTabProps {
  studentId: string;
  notes: StudentNote[];
  onNotesUpdated: () => void;
}

const StudentNotesTab = ({ studentId, notes, onNotesUpdated }: StudentNotesTabProps) => {
  const [isAddingNote, setIsAddingNote] = useState(false);
  const [newNoteContent, setNewNoteContent] = useState("");
  const [isPrivate, setIsPrivate] = useState(false);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleAddNote = async () => {
    if (!newNoteContent.trim()) {
      toast.error("Note content cannot be empty");
      return;
    }

    // In a real app, this would call the API
    // Simulating API call with timeout
    const newNote: StudentNote = {
      id: `note${Date.now()}`,
      content: newNoteContent.trim(),
      createdAt: new Date().toISOString(),
      createdBy: "Current User",
      isPrivate
    };

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    setIsAddingNote(false);
    setNewNoteContent("");
    setIsPrivate(false);
    
    toast.success("Note added successfully");
    onNotesUpdated();
  };

  const handleDeleteNote = async (noteId: string) => {
    setIsDeleting(noteId);
    
    // In a real app, this would call the API
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    setIsDeleting(null);
    toast.success("Note deleted successfully");
    onNotesUpdated();
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Student Notes</CardTitle>
            <CardDescription>
              Notes and comments about this student
            </CardDescription>
          </div>
          <Dialog open={isAddingNote} onOpenChange={setIsAddingNote}>
            <DialogTrigger asChild>
              <Button>
                <Plus size={16} className="mr-2" />
                Add Note
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add Student Note</DialogTitle>
                <DialogDescription>
                  Add a new note about this student. Private notes are only visible to administrators.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <Textarea
                  placeholder="Enter note content..."
                  value={newNoteContent}
                  onChange={(e) => setNewNoteContent(e.target.value)}
                  className="min-h-[150px]"
                />
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="private-note"
                    checked={isPrivate}
                    onCheckedChange={(checked) => setIsPrivate(checked === true)}
                  />
                  <label
                    htmlFor="private-note"
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 flex items-center gap-2"
                  >
                    {isPrivate ? (
                      <>
                        <Lock size={14} />
                        <span>Private note (only visible to administrators)</span>
                      </>
                    ) : (
                      <>
                        <Unlock size={14} />
                        <span>Public note (visible to all staff)</span>
                      </>
                    )}
                  </label>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddingNote(false)}>
                  Cancel
                </Button>
                <Button onClick={handleAddNote}>
                  Save Note
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </CardHeader>
        <CardContent>
          {notes.length > 0 ? (
            <div className="space-y-4">
              {notes.map((note) => (
                <div 
                  key={note.id} 
                  className={cn(
                    "border rounded-lg p-4 space-y-3",
                    note.isPrivate && "border-amber-200 bg-amber-50 dark:bg-amber-900/10 dark:border-amber-800/50"
                  )}
                >
                  <div className="flex items-start justify-between">
                    <div className="space-x-2">
                      {note.isPrivate && (
                        <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                          <Lock size={10} className="mr-1" />
                          Private
                        </Badge>
                      )}
                    </div>
                    <div className="flex gap-2">
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="h-8 w-8" 
                        onClick={() => {
                          // In a real app, this would open edit dialog
                          toast.info("Editing notes is not implemented in this demo");
                        }}
                      >
                        <Edit size={14} />
                      </Button>
                      <Dialog>
                        <DialogTrigger asChild>
                          <Button 
                            variant="ghost" 
                            size="icon" 
                            className="h-8 w-8 text-red-500"
                          >
                            <Trash2 size={14} />
                          </Button>
                        </DialogTrigger>
                        <DialogContent>
                          <DialogHeader>
                            <DialogTitle>Delete Note</DialogTitle>
                            <DialogDescription>
                              Are you sure you want to delete this note? This action cannot be undone.
                            </DialogDescription>
                          </DialogHeader>
                          <DialogFooter>
                            <Button variant="outline">
                              Cancel
                            </Button>
                            <Button 
                              variant="destructive" 
                              onClick={() => handleDeleteNote(note.id)}
                              disabled={isDeleting === note.id}
                            >
                              {isDeleting === note.id ? "Deleting..." : "Delete Note"}
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    </div>
                  </div>
                  
                  <p className="text-sm whitespace-pre-wrap">{note.content}</p>
                  
                  <div className="flex items-center justify-between text-xs text-muted-foreground pt-2">
                    <div className="flex items-center gap-2">
                      <User size={14} />
                      <span>{note.createdBy}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar size={14} />
                      <span>{formatDate(note.createdAt)}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center p-6 text-center border rounded-lg border-dashed bg-muted/30">
              <MessageSquare size={48} className="text-muted-foreground mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-1">No notes yet</h3>
              <p className="text-muted-foreground mb-4">
                Add a note to keep track of important information about this student
              </p>
              <Button onClick={() => setIsAddingNote(true)}>
                <Plus size={16} className="mr-2" />
                Add First Note
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default StudentNotesTab;
