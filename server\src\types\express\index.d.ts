// server/src/types/express/index.d.ts
import { IUser } from '../auth.types';
import { IPaymentDocument } from '../../models/payment.model';
import { IStudentDocument } from '../../models/student.model';
import { INoteDocument } from '../../models/note.model';

declare global {
    namespace Express {
        interface Request {
            user?: IUser;
            payment?: IPaymentDocument;
            student?: IStudentDocument;
            note?: INoteDocument;
            versionControl?: {
                enabled: boolean;
                document?: any;
                currentVersion?: number;
            };
        }
    }
}

export {};