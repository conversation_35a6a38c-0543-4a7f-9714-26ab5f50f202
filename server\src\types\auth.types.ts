// server/src/types/auth.types.ts
import { Types } from 'mongoose';
import { UserRole } from './user.types';  // Import UserRole type

export interface IUser {
    _id?: Types.ObjectId;  // The _id is optional in the interface
    username: string;
    password: string;
    role: UserRole;
    status: 'active' | 'inactive';
    lastLogin?: Date;
    createdAt: Date;
    modifiedAt: Date;
    createdBy: Types.ObjectId;
    roleHistory: Array<{
        role: string;
        changedAt: Date;
        changedBy: Types.ObjectId;
        reason: string;
    }>;
    loginAttempts: Array<{
        timestamp: Date;
        ipAddress: string;
        success: boolean;
    }>;
}