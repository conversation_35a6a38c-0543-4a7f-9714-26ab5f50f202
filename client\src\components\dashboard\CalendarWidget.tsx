
import { useMemo } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Calendar, Clock } from "lucide-react";

interface CalendarEvent {
  id: string;
  title: string;
  time: string;
  type: string;
  location?: string;
}

interface CalendarWidgetProps {
  events: CalendarEvent[];
  title: string;
  description?: string;
  isLoading?: boolean;
  maxItems?: number;
  date?: Date;
}

const CalendarWidget = ({
  events,
  title,
  description,
  isLoading = false,
  maxItems = 5,
  date = new Date(),
}: CalendarWidgetProps) => {
  // Get appropriate color class based on event type
  const getEventColorClass = (type: string): string => {
    switch (type.toLowerCase()) {
      case 'class':
        return 'border-blue-200 bg-blue-50';
      case 'meeting':
        return 'border-purple-200 bg-purple-50';
      case 'deadline':
        return 'border-red-200 bg-red-50';
      case 'maintenance':
        return 'border-yellow-200 bg-yellow-50';
      default:
        return 'border-gray-200 bg-gray-50';
    }
  };

  // Format date for display
  const formattedDate = useMemo(() => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  }, [date]);

  // Limit the number of events to display
  const displayedEvents = useMemo(() => {
    return events.slice(0, maxItems);
  }, [events, maxItems]);

  return (
    <Card className="h-full">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Calendar className="w-5 h-5 mr-2" />
          {title}
        </CardTitle>
        <CardDescription>
          {description || formattedDate}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {isLoading ? (
            // Skeleton loader for loading state
            Array(maxItems).fill(0).map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <Skeleton className="h-4 w-16" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-5 w-full" />
                  <Skeleton className="h-4 w-[70%]" />
                </div>
              </div>
            ))
          ) : displayedEvents.length > 0 ? (
            // Actual event items
            displayedEvents.map((event) => (
              <div 
                key={event.id} 
                className={`p-3 rounded-lg border-l-4 ${getEventColorClass(event.type)}`}
              >
                <div className="flex items-center justify-between mb-1">
                  <h4 className="font-medium text-sm">{event.title}</h4>
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Clock size={14} className="mr-1" />
                    {event.time}
                  </div>
                </div>
                {event.location && (
                  <p className="text-xs text-muted-foreground">
                    Location: {event.location}
                  </p>
                )}
              </div>
            ))
          ) : (
            // No events case
            <div className="text-center py-6 text-muted-foreground">
              <Calendar className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p>No events scheduled</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default CalendarWidget;
