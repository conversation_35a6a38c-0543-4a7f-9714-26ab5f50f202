// server/src/types/error.types.ts

export class AppError extends Error {
  constructor(
    public statusCode: number,
    public message: string,
    public isOperational = true,
    public stack = ''
  ) {
    super(message);
    this.name = this.constructor.name;
    if (stack) {
      this.stack = stack;
    } else {
      Error.captureStackTrace(this, this.constructor);
    }
  }
}

export class UserManagementError extends AppError {
  constructor(
    statusCode: number,
    message: string,
    public errorCode: string,
    public context?: any
  ) {
    super(statusCode, message, true);
    this.name = 'UserManagementError';
  }
}

export class SuperAdminConstraintError extends UserManagementError {
  constructor(message: string, context?: any) {
    super(400, message, 'SUPERADMIN_CONSTRAINT', context);
    this.name = 'SuperAdminConstraintError';
  }
}

export class RoleHierarchyError extends UserManagementError {
  constructor(message: string, context?: any) {
    super(403, message, 'ROLE_HIERARCHY', context);
    this.name = 'RoleHierarchyError';
  }
}

export class UserArchiveError extends UserManagementError {
  constructor(message: string, context?: any) {
    super(500, message, 'ARCHIVE_ERROR', context);
    this.name = 'UserArchiveError';
  }
}

export class UserReactivationError extends UserManagementError {
  constructor(message: string, context?: any) {
    super(400, message, 'REACTIVATION_ERROR', context);
    this.name = 'UserReactivationError';
  }
}

export class ConcurrentOperationError extends UserManagementError {
  constructor(message: string, context?: any) {
    super(409, message, 'CONCURRENT_OPERATION', context);
    this.name = 'ConcurrentOperationError';
  }
}

export class BulkOperationError extends UserManagementError {
  constructor(message: string, context?: any) {
    super(400, message, 'BULK_OPERATION', context);
    this.name = 'BulkOperationError';
  }
}

export class AttendanceError extends AppError {
  constructor(
    statusCode: number,
    message: string,
    public errorCode: string,
    public context?: any
  ) {
    super(statusCode, message, true);
    this.name = 'AttendanceError';
  }
}

export class DuplicateAttendanceError extends AttendanceError {
  constructor(message: string, context?: any) {
    super(400, message, 'DUPLICATE_ATTENDANCE', context);
    this.name = 'DuplicateAttendanceError';
  }
}

export class InvalidAttendanceDateError extends AttendanceError {
  constructor(message: string, context?: any) {
    super(400, message, 'INVALID_ATTENDANCE_DATE', context);
    this.name = 'InvalidAttendanceDateError';
  }
}

export class AttendanceScheduleError extends AttendanceError {
  constructor(message: string, context?: any) {
    super(400, message, 'ATTENDANCE_SCHEDULE', context);
    this.name = 'AttendanceScheduleError';
  }
}

export class ExcuseValidationError extends AttendanceError {
  constructor(message: string, context?: any) {
    super(400, message, 'EXCUSE_VALIDATION', context);
    this.name = 'ExcuseValidationError';
  }
}

export class AttendanceModificationError extends AttendanceError {
  constructor(message: string, context?: any) {
    super(403, message, 'ATTENDANCE_MODIFICATION', context);
    this.name = 'AttendanceModificationError';
  }
}