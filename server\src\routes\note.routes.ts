// server/src/routes/note.routes.ts
import express from 'express';
import { Note<PERSON><PERSON>roller } from '../controllers/note.controller';
import { authenticateToken, authorizeRoles } from '../middleware/auth.middleware';
import { validate } from '../middleware/validate.middleware';
import { noteValidation } from '../validations/note.validation';
import { catchAsync } from '../middleware/error.middleware';
import { apiLimiter } from '../middleware/rateLimiter.middleware';
import { NoteMiddleware } from '../middleware/note.middleware';

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Get notes with role-based access
router.get(
    '/',
    authorizeRoles('superAdmin', 'manager', 'secretary', 'teacher'),
    validate(noteValidation.getNotesQuery),
    catchAsync(NoteController.getNotes)
);

// Create new note
router.post(
    '/',
    authorizeRoles('superAdmin', 'manager', 'teacher'),
    validate(noteValidation.createNote),
    catchAsync(NoteController.createNote)
);
// Search notes
router.get(
    '/search',
    authorizeRoles('superAdmin', 'manager', 'secretary', 'teacher'),
    validate(noteValidation.getNotesQuery),
    catchAsync(NoteController.searchNotes)
);

// Export notes
router.get(
    '/export',
    authorizeRoles('superAdmin', 'manager'),
    validate(noteValidation.exportNotes),
    catchAsync(NoteController.exportNotes)
);

// Get student-specific notes
router.get(
    '/students/:studentId',
    authorizeRoles('superAdmin', 'manager', 'secretary', 'teacher'),
    catchAsync(NoteController.getStudentNotes)
);

// Get class-specific notes
router.get(
    '/classes/:classId',
    authorizeRoles('superAdmin', 'manager', 'secretary', 'teacher'),
    catchAsync(NoteController.getClassNotes)
);

// Bulk operations
router.post(
    '/bulk',
    authorizeRoles('superAdmin', 'manager'),
    validate(noteValidation.bulkOperation),
    catchAsync(NoteController.bulkOperation)
);

// Get specific note
router.get(
    '/:id',
    authorizeRoles('superAdmin', 'manager', 'secretary', 'teacher'),
    NoteMiddleware.validateNoteExists,
    NoteMiddleware.validateNoteAccess,
    catchAsync(NoteController.getNoteById)
);

// Update note
router.patch(
    '/:id',
    authorizeRoles('superAdmin', 'manager', 'teacher'),
    NoteMiddleware.validateNoteExists,
    NoteMiddleware.validateNoteAccess,
    NoteMiddleware.validateModificationPermissions,
    validate(noteValidation.updateNote),
    catchAsync(NoteController.updateNote)
);

// Delete note
router.delete(
    '/:id',
    authorizeRoles('superAdmin', 'manager'),
    NoteMiddleware.validateNoteExists,
    NoteMiddleware.validateNoteAccess,
    catchAsync(NoteController.deleteNote)
);

// Apply rate limiting to all routes
router.use(apiLimiter);

export default router;