import React, { useState, useEffect } from "react";
import { format, isAfter, addDays, isBefore, parseISO } from "date-fns";
import { useQuery } from "@tanstack/react-query";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { cn } from "@/lib/utils";
import { Class, MakeupClassFormData, Teacher } from "@/types/class";
import { scheduleMakeupClass, getAvailableRooms } from "@/services/makeupClassService";
import { useToast } from "@/hooks/use-toast";
import {
  Calendar as CalendarIcon,
  Clock,
  AlertCircle,
  CheckCircle,
  Loader2,
} from "lucide-react";

interface MakeupClassFormDialogProps {
  isOpen: boolean;
  onClose: () => void;
  classData: Class;
  onSuccess: () => void;
}

const timeOptions = [
  "08:00", "08:30", "09:00", "09:30", "10:00", "10:30", "11:00", "11:30", 
  "12:00", "12:30", "13:00", "13:30", "14:00", "14:30", "15:00", "15:30", 
  "16:00", "16:30", "17:00", "17:30", "18:00", "18:30", "19:00", "19:30"
];

// Form schema for validation
const makeupClassSchema = z.object({
  originalDate: z.date({
    required_error: "Original class date is required",
  }),
  makeupDate: z.date({
    required_error: "Makeup date is required",
  }).refine(date => isAfter(date, addDays(new Date(), -1)), {
    message: "Makeup date must be in the future",
  }),
  teacherId: z.string({
    required_error: "Teacher is required",
  }),
  roomId: z.string({
    required_error: "Room is required",
  }),
  timeStart: z.string({
    required_error: "Start time is required",
  }),
  timeEnd: z.string({
    required_error: "End time is required",
  }).refine((endTime) => {
    // This validation will be handled in the superRefine below
    return true;
  }, {
    message: "End time must be after start time",
  }),
  reasonType: z.enum(["teacher-absence", "holiday", "weather", "other"], {
    required_error: "Reason type is required",
  }),
  reason: z.string({
    required_error: "Reason is required",
  }).min(5, "Reason must be at least 5 characters"),
  notes: z.string().optional(),
  notifyStudents: z.boolean().default(true),
}).superRefine((data, ctx) => {
  // Validate that end time is after start time
  if (data.timeStart && data.timeEnd && data.timeStart >= data.timeEnd) {
    ctx.addIssue({
      code: z.ZodIssueCode.custom,
      message: "End time must be after start time",
      path: ["timeEnd"],
    });
  }
});

const MakeupClassFormDialog = ({ isOpen, onClose, classData, onSuccess }: MakeupClassFormDialogProps) => {
  const { toast } = useToast();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedTimeStart, setSelectedTimeStart] = useState<string>("");
  const [selectedTimeEnd, setSelectedTimeEnd] = useState<string>("");

  // Form setup
  const form = useForm<MakeupClassFormData>({
    resolver: zodResolver(makeupClassSchema),
    defaultValues: {
      originalDate: null,
      makeupDate: null,
      teacherId: classData.teachers[0]?.id || "",
      roomId: "",
      timeStart: "",
      timeEnd: "",
      reasonType: "teacher-absence",
      reason: "",
      notes: "",
      notifyStudents: true,
    },
  });

  // Fetch available rooms when date and time are selected
  const { data: availableRooms, isLoading: isLoadingRooms } = useQuery({
    queryKey: ["available-rooms", selectedDate, selectedTimeStart, selectedTimeEnd],
    queryFn: () => {
      if (!selectedDate || !selectedTimeStart || !selectedTimeEnd) {
        return Promise.resolve([]);
      }
      return getAvailableRooms(
        format(selectedDate, "yyyy-MM-dd"),
        selectedTimeStart,
        selectedTimeEnd
      );
    },
    enabled: !!(selectedDate && selectedTimeStart && selectedTimeEnd),
  });

  // Watch for reasonType changes to update reason field
  const reasonType = form.watch("reasonType");
  useEffect(() => {
    // Set default reason based on reason type
    switch (reasonType) {
      case "teacher-absence":
        form.setValue("reason", "Teacher was absent");
        break;
      case "holiday":
        form.setValue("reason", "Makeup for public holiday");
        break;
      case "weather":
        form.setValue("reason", "Canceled due to weather conditions");
        break;
      case "other":
        form.setValue("reason", "");
        break;
    }
  }, [reasonType, form]);

  // Watch for date and time changes
  const makeupDate = form.watch("makeupDate");
  const timeStart = form.watch("timeStart");
  const timeEnd = form.watch("timeEnd");

  useEffect(() => {
    if (makeupDate) {
      setSelectedDate(makeupDate);
    }
  }, [makeupDate]);

  useEffect(() => {
    if (timeStart) {
      setSelectedTimeStart(timeStart);
    }
  }, [timeStart]);

  useEffect(() => {
    if (timeEnd) {
      setSelectedTimeEnd(timeEnd);
    }
  }, [timeEnd]);

  // Handle form submission
  const onSubmit = async (data: MakeupClassFormData) => {
    try {
      setIsSubmitting(true);
      
      const formattedData = {
        originalDate: format(data.originalDate!, "yyyy-MM-dd"),
        makeupDate: format(data.makeupDate!, "yyyy-MM-dd"),
        teacherId: data.teacherId,
        roomId: data.roomId,
        timeStart: data.timeStart,
        timeEnd: data.timeEnd,
        reason: data.reason,
        notes: data.notes,
        notifyStudents: data.notifyStudents,
      };
      
      const response = await scheduleMakeupClass(classData.id, formattedData);
      
      toast({
        title: "Success",
        description: "Makeup class scheduled successfully",
      });
      
      onSuccess();
      onClose();
    } catch (error) {
      console.error("Error scheduling makeup class:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to schedule makeup class. Please try again.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Reset form when dialog closes
  useEffect(() => {
    if (!isOpen) {
      form.reset();
      setSelectedDate(null);
      setSelectedTimeStart("");
      setSelectedTimeEnd("");
    }
  }, [isOpen, form]);

  // Render room availability status
  const renderRoomAvailability = () => {
    if (!selectedDate || !selectedTimeStart || !selectedTimeEnd) {
      return (
        <FormDescription>
          Select date and time to see available rooms
        </FormDescription>
      );
    }

    if (isLoadingRooms) {
      return (
        <FormDescription className="flex items-center text-muted-foreground">
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Checking room availability...
        </FormDescription>
      );
    }

    if (availableRooms && availableRooms.length > 0) {
      const availableCount = availableRooms.filter(room => room.available).length;
      return (
        <FormDescription className="flex items-center">
          {availableCount > 0 ? (
            <>
              <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
              {availableCount} rooms available
            </>
          ) : (
            <>
              <AlertCircle className="mr-2 h-4 w-4 text-amber-500" />
              No rooms available for this time slot
            </>
          )}
        </FormDescription>
      );
    }

    return null;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>Schedule Makeup Class</DialogTitle>
          <DialogDescription>
            Schedule a makeup session for {classData.name}
          </DialogDescription>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Original Date Picker */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="originalDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Original Class Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Select date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value ?? undefined}
                          onSelect={field.onChange}
                          disabled={(date) => {
                            // Disable dates outside class schedule
                            const classStart = parseISO(classData.schedule.startDate);
                            const classEnd = parseISO(classData.schedule.endDate);
                            return isBefore(date, classStart) || isAfter(date, classEnd);
                          }}
                          initialFocus
                          className="p-3 pointer-events-auto"
                        />
                      </PopoverContent>
                    </Popover>
                    <FormDescription>
                      The date of the missed class
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              {/* Makeup Date Picker */}
              <FormField
                control={form.control}
                name="makeupDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>Makeup Class Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Select date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value ?? undefined}
                          onSelect={field.onChange}
                          disabled={(date) => {
                            // Disable dates in the past
                            return isBefore(date, new Date());
                          }}
                          initialFocus
                          className="p-3 pointer-events-auto"
                        />
                      </PopoverContent>
                    </Popover>
                    <FormDescription>
                      When the makeup class will be held
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            
            {/* Time Selection */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="timeStart"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Start Time</FormLabel>
                    <Select 
                      onValueChange={field.onChange} 
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select time" />
                          <Clock className="ml-2 h-4 w-4" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {timeOptions.map((time) => (
                          <SelectItem key={time} value={time}>
                            {time}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="timeEnd"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>End Time</FormLabel>
                    <Select 
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select time" />
                          <Clock className="ml-2 h-4 w-4" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {timeOptions.map((time) => (
                          <SelectItem 
                            key={time} 
                            value={time}
                            disabled={time <= form.getValues("timeStart")}
                          >
                            {time}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            
            {/* Teacher Selection */}
            <FormField
              control={form.control}
              name="teacherId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Teacher</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select teacher" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {classData.teachers.map((teacher) => (
                        <SelectItem key={teacher.id} value={teacher.id}>
                          {teacher.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Teacher who will conduct the makeup class
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            {/* Room Selection */}
            <FormField
              control={form.control}
              name="roomId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Room</FormLabel>
                  <Select 
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                    disabled={isLoadingRooms || !availableRooms || availableRooms.length === 0}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={isLoadingRooms ? "Loading rooms..." : "Select room"} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {availableRooms && availableRooms.map((room) => (
                        <SelectItem 
                          key={room.id} 
                          value={room.id}
                          disabled={!room.available}
                          className={!room.available ? "opacity-50" : ""}
                        >
                          {room.name} {!room.available && " (Not Available)"}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {renderRoomAvailability()}
                  <FormMessage />
                </FormItem>
              )}
            />
            
            {/* Reason Selection */}
            <FormField
              control={form.control}
              name="reasonType"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>Reason for Makeup</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="flex flex-col space-y-1"
                    >
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="teacher-absence" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          Teacher absence
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="holiday" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          Holiday reschedule
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="weather" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          Weather cancellation
                        </FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-3 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="other" />
                        </FormControl>
                        <FormLabel className="font-normal">
                          Other reason
                        </FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            {/* Reason Description */}
            <FormField
              control={form.control}
              name="reason"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Reason Details</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Provide details about why this makeup class is needed"
                      className="resize-none"
                      {...field}
                      disabled={reasonType !== "other"}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            {/* Additional Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Additional Notes (Optional)</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Any additional information about this makeup class"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    These notes will be visible to teachers and administrators
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            {/* Notification Option */}
            <FormField
              control={form.control}
              name="notifyStudents"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>
                      Notify students
                    </FormLabel>
                    <FormDescription>
                      Send notification to all students in this class
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />
            
            <DialogFooter>
              <Button 
                type="button" 
                variant="outline" 
                onClick={onClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button 
                type="submit"
                disabled={isSubmitting || isLoadingRooms}
              >
                {isSubmitting && (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                Schedule Makeup Class
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default MakeupClassFormDialog;
