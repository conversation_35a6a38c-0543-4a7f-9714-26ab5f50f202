
import { Skeleton } from "@/components/ui/skeleton";
import { cn } from "@/lib/utils";
import { 
  Calendar, 
  User, 
  DollarSign, 
  UserPlus, 
  Book, 
  PenSquare, 
  AlertCircle,
  Activity 
} from "lucide-react";

interface ActivityFeedProps {
  activities: Array<{
    id: string;
    type: string;
    description: string;
    timestamp: string;
    user: string;
  }>;
  isLoading?: boolean;
  className?: string;
  title?: string;
}

const ActivityFeed = ({ 
  activities, 
  isLoading = false, 
  className,
  title = "Activity Feed"
}: ActivityFeedProps) => {
  
  // Get icon based on activity type
  const getActivityIcon = (type: string) => {
    switch (type.toLowerCase()) {
      case 'payment':
        return <DollarSign className="h-5 w-5 text-emerald-500" />;
      case 'registration':
        return <UserPlus className="h-5 w-5 text-blue-500" />;
      case 'class':
        return <Book className="h-5 w-5 text-indigo-500" />;
      case 'note':
        return <PenSquare className="h-5 w-5 text-amber-500" />;
      case 'attendance':
        return <Calendar className="h-5 w-5 text-violet-500" />;
      case 'user':
        return <User className="h-5 w-5 text-slate-500" />;
      case 'warning':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        return <Activity className="h-5 w-5 text-gray-500" />;
    }
  };
  
  // Format timestamp
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return new Intl.DateTimeFormat('en-US', {
      month: 'short',
      day: 'numeric',
      hour: 'numeric',
      minute: 'numeric'
    }).format(date);
  };
  
  if (isLoading) {
    return (
      <div className={cn("space-y-4", className)}>
        <h3 className="text-lg font-medium">{title}</h3>
        {[1, 2, 3].map((i) => (
          <div key={i} className="flex items-start space-x-3">
            <Skeleton className="h-9 w-9 rounded-full" />
            <div className="space-y-1 flex-1">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-3 w-1/2" />
            </div>
          </div>
        ))}
      </div>
    );
  }
  
  if (!activities || activities.length === 0) {
    return (
      <div className={cn("flex flex-col items-center justify-center py-6 text-center", className)}>
        <h3 className="text-lg font-medium mb-2">{title}</h3>
        <Activity className="h-10 w-10 text-muted-foreground mb-2 opacity-50" />
        <p className="text-muted-foreground">No recent activity to display</p>
      </div>
    );
  }
  
  return (
    <div className={cn("space-y-4", className)}>
      <h3 className="text-lg font-medium">{title}</h3>
      <div className="space-y-4">
        {activities.map((activity) => (
          <div key={activity.id} className="flex items-start space-x-3">
            <div className="shrink-0 mt-0.5">
              {getActivityIcon(activity.type)}
            </div>
            <div className="space-y-1 flex-1">
              <p className="text-sm">{activity.description}</p>
              <div className="flex items-center text-xs text-muted-foreground">
                <span>{activity.user}</span>
                <span className="mx-1">•</span>
                <span>{formatTime(activity.timestamp)}</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default ActivityFeed;
