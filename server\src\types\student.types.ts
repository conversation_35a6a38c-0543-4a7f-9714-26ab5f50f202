// server/src/types/student.types.ts
import { Types } from 'mongoose';

export interface ClassHistoryItem {
    classId: Types.ObjectId;
    startDate: Date;
    endDate?: Date;
    reason?: string;
}

export interface IStudent {
    _id?: Types.ObjectId;
    name: string;
    contactInfo: {
        phone: string;
        email: string;
        address: string;
    };
    status: 'active' | 'inactive';
    currentLevel: string;
    currentClass?: Types.ObjectId;
    registeredAt: Date;
    registeredBy: Types.ObjectId;
    payments: Array<{
        amount: number;
        date: Date;
        recordedBy: Types.ObjectId;
        remainingBalance: number;
        nextDueDate: Date;
        notes?: string;
    }>;
    levelHistory: Array<{
        fromLevel: string;
        toLevel: string;
        date: Date;
        reason: string;
        approvedBy: Types.ObjectId;
    }>;
    classHistory: ClassHistoryItem[];
}

export interface StudentQueryOptions {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    status?: 'active' | 'inactive';
    level?: string;
    search?: string;
    classId?: string;
    fromDate?: Date;
    toDate?: Date;
    paymentStatus?: 'paid' | 'pending' | 'overdue';
}

export interface StudentUpdateDTO {
    name?: string;
    contactInfo?: {
        phone?: string;
        email?: string;
        address?: string;
    };
    currentLevel?: string;
    status?: 'active' | 'inactive';
}

export interface PaymentRecordDTO {
    amount: number;
    date: Date;
    notes?: string;
    nextDueDate: Date;
}

export interface StudentResponseDTO {
    id: string;
    name: string;
    contactInfo: {
        phone: string;
        email: string;
        address: string;
    };
    status: string;
    currentLevel: string;
    currentClass?: string;
    registeredAt: Date;
    registeredBy: string;
    latestPayment?: {
        amount: number;
        date: Date;
        remainingBalance: number;
        nextDueDate: Date;
    };
}

export interface StudentBulkOperationDTO {
    studentIds: string[];
    operation: 'activate' | 'deactivate' | 'changeLevel' | 'assignClass';
    reason: string;
    newValue?: string; // For level or class changes
}

export interface StudentStatusChangeDTO {
    status: 'active' | 'inactive';
    reason: string;
    changedBy: Types.ObjectId;
    timestamp: Date;
}

export interface StudentArchiveData {
    _id: Types.ObjectId;
    studentId: Types.ObjectId;
    name: string;
    status: string;
    archivedAt: Date;
    archivedBy: Types.ObjectId;
    lastLevel: string;
    lastClass?: Types.ObjectId;
    paymentHistory: Array<{
        amount: number;
        date: Date;
        recordedBy: Types.ObjectId;
    }>;
    classHistory: Array<{
        classId: Types.ObjectId;
        startDate: Date;
        endDate?: Date;
    }>;
}

export interface StudentTransferDTO {
    fromClass: Types.ObjectId;
    toClass: Types.ObjectId;
    reason: string;
    transferDate: Date;
}

export interface StudentExportOptions {
    format: 'csv' | 'json';
    fields?: string[];
    includePaymentHistory?: boolean;
    includeClassHistory?: boolean;
    dateRange?: {
        start: Date;
        end: Date;
    };
}

export interface PaymentSummaryDTO {
    totalPaid: number;
    totalDue: number;
    lastPaymentDate?: Date;
    nextDueDate?: Date;
    paymentStatus: 'paid' | 'pending' | 'overdue';
}