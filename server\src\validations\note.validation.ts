// server/src/validations/note.validation.ts
import <PERSON><PERSON> from 'joi';
import { NoteCategory, NoteVisibility } from '../types/notes.types';

// Reusable sub-schemas
const noteTypeSchema = Joi.string()
    .valid('academic', 'behavioral', 'attendance', 'general')
    .required()
    .messages({
        'any.only': 'Invalid note type',
        'any.required': 'Note type is required'
    });

const visibilitySchema = Joi.string()
    .valid('teacher_only', 'all_staff', 'manager_only')
    .required()
    .messages({
        'any.only': 'Invalid visibility level',
        'any.required': 'Visibility level is required'
    });

const contentSchema = Joi.string()
    .min(3)
    .max(5000)
    .required()
    .messages({
        'string.min': 'Note content must be at least 3 characters long',
        'string.max': 'Note content cannot exceed 5000 characters',
        'any.required': 'Note content is required'
    });

const tagsSchema = Joi.array()
    .items(
        Joi.string()
            .min(2)
            .max(20)
            .pattern(/^[a-zA-Z0-9-_]+$/)
            .messages({
                'string.pattern.base': 'Tags can only contain letters, numbers, hyphens and underscores',
                'string.min': 'Tag must be at least 2 characters long',
                'string.max': 'Tag cannot exceed 20 characters'
            })
    )
    .max(10)
    .unique()
    .messages({
        'array.max': 'Maximum 10 tags allowed',
        'array.unique': 'Duplicate tags are not allowed'
    });

export const noteValidation = {
    // GET /api/notes query parameters
    getNotesQuery: Joi.object({
        page: Joi.number()
            .integer()
            .min(1)
            .max(1000)
            .default(1)
            .messages({
                'number.base': 'Page must be a number',
                'number.integer': 'Page number must be an integer',
                'number.min': 'Page number must be greater than 0',
                'number.max': 'Page number too large'
            }),
        limit: Joi.number()
            .integer()
            .min(1)
            .max(100)
            .default(10)
            .messages({
                'number.base': 'Limit must be a number',
                'number.integer': 'Limit must be an integer',
                'number.min': 'Limit must be greater than 0',
                'number.max': 'Limit cannot exceed 100'
            }),
        sortBy: Joi.string()
            .valid('createdAt', 'modifiedAt', 'type', 'visibility')
            .default('createdAt')
            .messages({
                'any.only': 'Invalid sort field, must be one of: createdAt, modifiedAt, type, visibility'
            }),
        sortOrder: Joi.string()
            .valid('asc', 'desc')
            .default('desc')
            .messages({
                'any.only': 'Sort order must be either asc or desc'
            }),
        type: noteTypeSchema.optional(),
        visibility: visibilitySchema.optional(),
        studentId: Joi.string()
            .pattern(/^[0-9a-fA-F]{24}$/)
            .messages({
                'string.pattern.base': 'Invalid student ID format'
            }),
        classId: Joi.string()
            .pattern(/^[0-9a-fA-F]{24}$/)
            .messages({
                'string.pattern.base': 'Invalid class ID format'
            }),
        tags: Joi.string()
            .pattern(/^[a-zA-Z0-9-_]+(,[a-zA-Z0-9-_]+)*$/)
            .messages({
                'string.pattern.base': 'Invalid tags format. Use comma-separated values'
            }),
        search: Joi.string()
            .min(2)
            .max(50)
            .trim()
            .messages({
                'string.min': 'Search term must be at least 2 characters long',
                'string.max': 'Search term cannot exceed 50 characters'
            }),
        fromDate: Joi.date()
            .iso()
            .messages({
                'date.base': 'From date must be a valid date',
                'date.format': 'From date must be in ISO format'
            }),
        toDate: Joi.date()
            .iso()
            .min(Joi.ref('fromDate'))
            .messages({
                'date.base': 'To date must be a valid date',
                'date.format': 'To date must be in ISO format',
                'date.min': 'End date must be after start date'
            }),
        datePeriod: Joi.string()
            .valid('today', 'yesterday', 'thisWeek', 'lastWeek', 'thisMonth', 'lastMonth', 'thisYear')
            .messages({
                'any.only': 'Invalid date period'
            }),
        createdBy: Joi.string()
            .pattern(/^[0-9a-fA-F]{24}$/)
            .messages({
                'string.pattern.base': 'Invalid user ID format'
            })
    }),

    // POST /api/notes
    createNote: Joi.object({
        type: noteTypeSchema,
        visibility: visibilitySchema,
        content: contentSchema,
        tags: tagsSchema,
        studentId: Joi.string()
            .pattern(/^[0-9a-fA-F]{24}$/)
            .messages({
                'string.pattern.base': 'Invalid student ID format'
            }),
        classId: Joi.string()
            .pattern(/^[0-9a-fA-F]{24}$/)
            .messages({
                'string.pattern.base': 'Invalid class ID format'
            }),
        relatedTo: Joi.object({
            type: Joi.string()
                .valid('attendance', 'assessment', 'payment', 'class', 'behavior')
                .required()
                .messages({
                    'any.only': 'Invalid related entity type',
                    'any.required': 'Related entity type is required'
                }),
            id: Joi.string()
                .pattern(/^[0-9a-fA-F]{24}$/)
                .required()
                .messages({
                    'string.pattern.base': 'Invalid related entity ID format',
                    'any.required': 'Related entity ID is required'
                })
        })
    }).custom((value, helpers) => {
        // Ensure at least one reference exists
        if (!value.studentId && !value.classId && !value.relatedTo) {
            return helpers.error('object.missing_reference');
        }
        return value;
    }).messages({
        'object.missing_reference': 'Note must be associated with at least one entity (student, class, or related entity)'
    }),

    // PATCH /api/notes/:id
    updateNote: Joi.object({
        content: Joi.string()
            .min(3)
            .max(5000)
            .messages({
                'string.min': 'Note content must be at least 3 characters long',
                'string.max': 'Note content cannot exceed 5000 characters'
            }),
        visibility: visibilitySchema.optional(),
        tags: tagsSchema
    }).min(1).messages({
        'object.min': 'At least one field must be provided for update'
    }),

    // POST /api/notes/bulk
    bulkOperation: Joi.object({
        noteIds: Joi.array()
            .items(
                Joi.string()
                    .pattern(/^[0-9a-fA-F]{24}$/)
                    .messages({
                        'string.pattern.base': 'Invalid note ID format'
                    })
            )
            .min(1)
            .max(50)
            .unique()
            .required()
            .messages({
                'array.min': 'At least one note ID must be provided',
                'array.max': 'Cannot process more than 50 notes at once',
                'array.unique': 'Duplicate note IDs are not allowed',
                'any.required': 'Note IDs are required'
            }),
        operation: Joi.string()
            .valid('delete', 'updateVisibility', 'updateTags')
            .required()
            .messages({
                'any.only': 'Invalid operation specified',
                'any.required': 'Operation type is required'
            }),
        value: Joi.when('operation', {
            is: 'updateVisibility',
            then: visibilitySchema,
            otherwise: Joi.when('operation', {
                is: 'updateTags',
                then: tagsSchema,
                otherwise: Joi.forbidden()
            })
        })
    }),

    // GET /api/notes/export
    exportNotes: Joi.object({
        format: Joi.string()
            .valid('csv', 'json')
            .default('csv')
            .messages({
                'any.only': 'Export format must be either csv or json'
            }),
        fields: Joi.string()
            .pattern(/^[a-zA-Z_]+(,[a-zA-Z_]+)*$/)
            .messages({
                'string.pattern.base': 'Invalid fields format. Use comma-separated field names'
            }),
        includeHistory: Joi.boolean(),
        fromDate: Joi.date()
            .iso()
            .messages({
                'date.base': 'From date must be a valid date',
                'date.format': 'From date must be in ISO format'
            }),
        toDate: Joi.date()
            .iso()
            .min(Joi.ref('fromDate'))
            .messages({
                'date.base': 'To date must be a valid date',
                'date.format': 'To date must be in ISO format',
                'date.min': 'End date must be after start date'
            }),
        datePeriod: Joi.string()
            .valid('today', 'yesterday', 'thisWeek', 'lastWeek', 'thisMonth', 'lastMonth', 'thisYear')
            .messages({
                'any.only': 'Invalid date period'
            }),
        groupBy: Joi.string()
            .valid('student', 'class', 'type', 'date')
            .messages({
                'any.only': 'Invalid grouping option'
            })
    })
};

export default noteValidation;