
import MainLayout from "@/components/layout/MainLayout";
import ClassCreateForm from "@/components/classes/ClassCreateForm";
import { hasRole } from "@/lib/auth";
import { Navigate } from "react-router-dom";

const ClassCreate = () => {
  // Check if user has proper permissions
  const canCreateClass = hasRole(['SuperAdmin', 'Manager']);

  if (!canCreateClass) {
    return <Navigate to="/classes" replace />;
  }

  return (
    <MainLayout>
      <ClassCreateForm />
    </MainLayout>
  );
};

export default ClassCreate;
