
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useQuery } from "@tanstack/react-query";
import { fetchStudents } from "@/services/studentService";
import { createPayment } from "@/services/paymentService";
import { toast } from "sonner";
import { format } from "date-fns";
import { Calendar as CalendarIcon, CreditCard, Check, Plus, Search } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  Popover<PERSON>ontent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";

// Define schema for payment form
const paymentSchema = z.object({
  studentId: z.string().min(1, "Student is required"),
  amount: z.coerce.number().positive("Amount must be greater than zero"),
  method: z.enum(["cash", "card", "transfer", "check", "other"], {
    required_error: "Payment method is required",
  }),
  date: z.date({
    required_error: "Payment date is required",
  }),
  nextDueDate: z.date().optional(),
  description: z.string().min(1, "Description is required"),
  period: z.enum(["monthly", "quarterly", "semi_annual", "annual", "one_time"]).optional(),
  notes: z.string().optional(),
});

type PaymentFormValues = z.infer<typeof paymentSchema>;

interface PaymentFormProps {
  studentId?: string;
  onSuccess?: (paymentId: string) => void;
}

const PaymentForm = ({ studentId, onSuccess }: PaymentFormProps) => {
  const navigate = useNavigate();
  const [selectedStudent, setSelectedStudent] = useState<{id: string, name: string} | null>(null);
  const [openStudentPicker, setOpenStudentPicker] = useState(false);

  // Fetch students for dropdown
  const { data: studentsData } = useQuery({
    queryKey: ["students"],
    queryFn: () => fetchStudents({ page: 1, limit: 100 }),
  });

  const students = studentsData?.data || [];

  // Configure form with default values
  const form = useForm<PaymentFormValues>({
    resolver: zodResolver(paymentSchema),
    defaultValues: {
      studentId: studentId || "",
      amount: undefined,
      method: "cash",
      date: new Date(),
      description: "",
      period: "monthly",
    },
  });

  // Update form when studentId prop changes
  useEffect(() => {
    if (studentId) {
      form.setValue("studentId", studentId);
      
      // Find student name for display
      const student = students.find(s => s.id === studentId);
      if (student) {
        setSelectedStudent({
          id: student.id, 
          name: `${student.firstName} ${student.lastName}`
        });
      }
    }
  }, [studentId, students, form]);

  // Handle form submission
  const onSubmit = async (data: PaymentFormValues) => {
    try {
      // Convert dates to ISO format expected by API
      const paymentData = {
        ...data,
        date: format(data.date, "yyyy-MM-dd"),
        nextDueDate: data.nextDueDate ? format(data.nextDueDate, "yyyy-MM-dd") : undefined,
      };
      
      const response = await createPayment(paymentData);
      
      if (response.success) {
        toast.success("Payment recorded successfully");
        if (onSuccess) {
          onSuccess(response.data.id);
        } else {
          navigate(`/payments/${response.data.id}`);
        }
      } else {
        toast.error("Failed to record payment");
      }
    } catch (error) {
      console.error("Error recording payment:", error);
      toast.error("An error occurred while recording payment");
    }
  };

  // Common payment descriptions for quick selection
  const commonDescriptions = [
    "Monthly Tuition",
    "Quarterly Tuition",
    "Registration Fee",
    "Material Fee",
    "Exam Fee",
    "Special Class Fee",
  ];

  // Handle selecting a common description
  const selectCommonDescription = (description: string) => {
    const month = format(new Date(), "MMMM yyyy");
    form.setValue("description", `${description} - ${month}`);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Payment Information</CardTitle>
            <CardDescription>
              Record a new payment for a student
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Student Selection */}
            <FormField
              control={form.control}
              name="studentId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Student</FormLabel>
                  <Popover open={openStudentPicker} onOpenChange={setOpenStudentPicker}>
                    <PopoverTrigger asChild>
                      <FormControl>
                        <Button
                          variant="outline"
                          role="combobox"
                          className={`w-full justify-between ${!field.value && "text-muted-foreground"}`}
                          disabled={!!studentId} // Disable if studentId is provided as prop
                        >
                          {selectedStudent?.name || "Select student"}
                          <Search className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                        </Button>
                      </FormControl>
                    </PopoverTrigger>
                    <PopoverContent className="w-[400px] p-0">
                      <Command>
                        <CommandInput placeholder="Search students..." />
                        <CommandEmpty>No student found</CommandEmpty>
                        <CommandGroup className="max-h-[300px] overflow-y-auto">
                          {students.map((student) => (
                            <CommandItem
                              key={student.id}
                              value={`${student.firstName} ${student.lastName}`}
                              onSelect={() => {
                                form.setValue("studentId", student.id);
                                setSelectedStudent({
                                  id: student.id,
                                  name: `${student.firstName} ${student.lastName}`
                                });
                                setOpenStudentPicker(false);
                              }}
                            >
                              <Check
                                className={`mr-2 h-4 w-4 ${field.value === student.id ? "opacity-100" : "opacity-0"}`}
                              />
                              {student.firstName} {student.lastName}
                            </CommandItem>
                          ))}
                        </CommandGroup>
                      </Command>
                    </PopoverContent>
                  </Popover>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            {/* Amount */}
            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Amount</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <span className="absolute left-3 top-2.5 text-muted-foreground">$</span>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        placeholder="0.00"
                        className="pl-8"
                        {...field}
                        onChange={(e) => field.onChange(e.target.valueAsNumber)}
                      />
                    </div>
                  </FormControl>
                  <FormDescription>
                    Enter the payment amount in dollars
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            {/* Payment Method */}
            <FormField
              control={form.control}
              name="method"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Payment Method</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select payment method" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="cash">Cash</SelectItem>
                      <SelectItem value="card">Card</SelectItem>
                      <SelectItem value="transfer">Bank Transfer</SelectItem>
                      <SelectItem value="check">Check</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            {/* Dates */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Payment Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className="w-full pl-3 text-left font-normal"
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span className="text-muted-foreground">Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) => date > new Date()}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="nextDueDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Next Due Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant="outline"
                            className="w-full pl-3 text-left font-normal"
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span className="text-muted-foreground">Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          disabled={(date) => date < new Date()}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormDescription>
                      When the next payment is due
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            
            {/* Description */}
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Payment description" {...field} />
                  </FormControl>
                  <div className="mt-2 flex flex-wrap gap-2">
                    {commonDescriptions.map((desc) => (
                      <Button
                        key={desc}
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => selectCommonDescription(desc)}
                      >
                        <Plus className="mr-1 h-3 w-3" />
                        {desc}
                      </Button>
                    ))}
                  </div>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            {/* Period */}
            <FormField
              control={form.control}
              name="period"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Payment Period</FormLabel>
                  <Select 
                    onValueChange={field.onChange} 
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select payment period" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="monthly">Monthly</SelectItem>
                      <SelectItem value="quarterly">Quarterly</SelectItem>
                      <SelectItem value="semi_annual">Semi-Annual</SelectItem>
                      <SelectItem value="annual">Annual</SelectItem>
                      <SelectItem value="one_time">One-time Payment</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    This helps categorize recurring payments
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            {/* Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Notes (Optional)</FormLabel>
                  <FormControl>
                    <Textarea placeholder="Any additional notes about this payment" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </CardContent>
          <CardFooter className="flex flex-col sm:flex-row gap-3 sm:justify-between">
            <Button type="button" variant="outline" onClick={() => navigate(-1)}>
              Cancel
            </Button>
            <div className="flex gap-3">
              <Button type="submit">
                <CreditCard className="mr-2 h-4 w-4" />
                Record Payment
              </Button>
            </div>
          </CardFooter>
        </Card>
      </form>
    </Form>
  );
};

export default PaymentForm;
