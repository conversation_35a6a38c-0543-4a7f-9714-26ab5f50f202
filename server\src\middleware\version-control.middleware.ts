// server/src/middleware/version-control.middleware.ts

import { Request, Response, NextFunction } from 'express';
import mongoose from 'mongoose';
import { AppError, ConcurrentOperationError } from '../types/error.types';

export const versionControl = (modelName: string) => {
    return async (req: Request, res: Response, next: NextFunction) => {
        try {
            const { id } = req.params;
            const { version } = req.body;
            
            if (!version) {
                // If no version provided, just add a flag to check in controller
                req.versionControl = { enabled: false };
                return next();
            }
            
            // Find the document by id
            const model = mongoose.model(modelName);
            const document = await model.findById(id);
            
            if (!document) {
                throw new AppError(404, `${modelName} not found`);
            }
            
            // Check document version against provided version
            if (document.__v !== version) {
                throw new ConcurrentOperationError(
                    `This ${modelName.toLowerCase()} has been modified by someone else. Please refresh and try again.`,
                    {
                        currentVersion: document.__v,
                        providedVersion: version
                    }
                );
            }
            
            // Attach version control info to request
            req.versionControl = {
                enabled: true,
                document,
                currentVersion: document.__v
            };
            
            next();
        } catch (error) {
            next(error);
        }
    };
};