
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { LucideIcon } from "lucide-react";
import { Link } from "react-router-dom";

interface QuickActionButtonProps {
  icon: LucideIcon;
  label: string;
  href?: string;
  onClick?: () => void;
  description?: string;
  variant?: "default" | "ghost" | "outline" | "secondary" | "destructive" | "link";
  className?: string;
}

const QuickActionButton = ({
  icon: Icon,
  label,
  href,
  onClick,
  description,
  variant = "outline",
  className,
}: QuickActionButtonProps) => {
  const buttonContent = (
    <>
      <Icon className="h-6 w-6" />
      <div className="text-center">
        <p className="font-medium text-sm">{label}</p>
        {description && (
          <p className="text-xs opacity-70">{description}</p>
        )}
      </div>
    </>
  );

  if (href) {
    return (
      <Button
        variant={variant}
        className={cn(
          "flex flex-col items-center justify-center h-24 w-full p-2 gap-2",
          className
        )}
        asChild
      >
        <Link to={href}>{buttonContent}</Link>
      </Button>
    );
  }

  return (
    <Button
      variant={variant}
      className={cn(
        "flex flex-col items-center justify-center h-24 w-full p-2 gap-2",
        className
      )}
      onClick={onClick}
    >
      {buttonContent}
    </Button>
  );
};

export default QuickActionButton;
