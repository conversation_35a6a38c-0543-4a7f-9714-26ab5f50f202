// server/src/controllers/user.controller.ts
import { Request, Response } from 'express';
import { UserService } from '../services/user.service';
import { AppError } from '../types/error.types';
import { SystemLogger } from '../services/logger.service';
import { UserRole } from '../types/user.types';

export class UserController {
  static async getUsers(req: Request, res: Response) {
    const currentUser = req.user;
    if (!currentUser?._id) {
      throw new AppError(401, 'User not authenticated');
    }

    const {
      page,
      limit,
      sortBy,
      sortOrder,
      status,
      role,
      search,
      fromDate,
      toDate,
      activityStatus
    } = req.query;

    const options = {
      page: page ? parseInt(page as string) : undefined,
      limit: limit ? parseInt(limit as string) : undefined,
      sortBy: sortBy as string,
      sortOrder: sortOrder as 'asc' | 'desc',
      status: status as 'active' | 'inactive',
      role: role ? (role as UserRole) : undefined,
      search: search as string,
      fromDate: fromDate ? new Date(fromDate as string) : undefined,
      toDate: toDate ? new Date(toDate as string) : undefined,
      activityStatus: activityStatus as 'online' | 'offline' | 'idle'
    };

    const result = await UserService.getUsers(options, currentUser._id.toString());

    res.json({
      success: true,
      data: result.users,
      pagination: result.pagination
    });
  }

  static async getUserById(req: Request, res: Response) {
    const currentUser = req.user;
    if (!currentUser?._id) {
      throw new AppError(401, 'User not authenticated');
    }

    const { id } = req.params;
    const user = await UserService.getUserById(id, currentUser._id.toString());

    res.json({
      success: true,
      data: user
    });
  }

  static async updateUserStatus(req: Request, res: Response) {
    const currentUser = req.user;
    if (!currentUser?._id) {
      throw new AppError(401, 'User not authenticated');
    }

    const { id } = req.params;
    const { status, reason } = req.body;

    if (!reason || reason.trim().length < 10) {
      throw new AppError(400, 'A detailed reason (minimum 10 characters) is required for status changes');
    }

    const user = await UserService.updateUserStatus(
      id,
      status,
      currentUser._id.toString(),
      reason
    );

    res.json({
      success: true,
      message: `User status updated to ${status}`,
      data: user
    });
  }

  static async updateUserRole(req: Request, res: Response) {
    const currentUser = req.user;
    if (!currentUser?._id) {
      throw new AppError(401, 'User not authenticated');
    }

    const { id } = req.params;
    const { role, reason } = req.body;

    if (!reason || reason.trim().length < 10) {
      throw new AppError(400, 'A detailed reason (minimum 10 characters) is required for role changes');
    }

    const user = await UserService.updateUserRole(
      id,
      role,
      currentUser._id.toString(),
      reason
    );

    res.json({
      success: true,
      message: `User role updated to ${role}`,
      data: user
    });
  }

  static async getUserActivities(req: Request, res: Response) {
    const currentUser = req.user;
    if (!currentUser?._id) {
      throw new AppError(401, 'User not authenticated');
    }

    const { id } = req.params;
    const {
      fromDate,
      toDate,
      activityType,
      page = '1',
      limit = '10'
    } = req.query;

    const activities = await UserService.getUserActivities(
      id,
      currentUser._id.toString()
    );

    res.json({
      success: true,
      data: activities
    });
  }

  static async bulkUpdateStatus(req: Request, res: Response) {
    const currentUser = req.user;
    if (!currentUser?._id) {
      throw new AppError(401, 'User not authenticated');
    }

    const { userIds, status, reason } = req.body;
    const performerId = currentUser._id.toString();

    if (!Array.isArray(userIds) || userIds.length === 0) {
      throw new AppError(400, 'User IDs array is required');
    }

    if (!reason || reason.trim().length < 10) {
      throw new AppError(400, 'A detailed reason (minimum 10 characters) is required for bulk status changes');
    }

    const results = await Promise.allSettled(
      userIds.map(id => 
        UserService.updateUserStatus(id, status, performerId, reason)
      )
    );

    const summary = {
      total: userIds.length,
      successful: results.filter(r => r.status === 'fulfilled').length,
      failed: results.filter(r => r.status === 'rejected').length,
      errors: results
        .map((r, i) => r.status === 'rejected' ? 
          { userId: userIds[i], error: (r as PromiseRejectedResult).reason.message } : 
          null)
        .filter(Boolean)
    };

    await SystemLogger.log({
      severity: 'info',
      category: 'user_management',
      action: 'bulk_status_update',
      performedBy: performerId,
      details: {
        targetUsers: userIds,
        newStatus: status,
        reason,
        summary
      },
      status: 'success',
      timestamp: new Date()
    });

    res.json({
      success: true,
      message: 'Bulk status update completed',
      data: summary
    });
  }

  static async bulkUpdateRole(req: Request, res: Response) {
    const currentUser = req.user;
    if (!currentUser?._id) {
      throw new AppError(401, 'User not authenticated');
    }

    const { userIds, role, reason } = req.body;
    const performerId = currentUser._id.toString();

    if (!Array.isArray(userIds) || userIds.length === 0) {
      throw new AppError(400, 'User IDs array is required');
    }

    if (!reason || reason.trim().length < 10) {
      throw new AppError(400, 'A detailed reason (minimum 10 characters) is required for bulk role changes');
    }

    const results = await Promise.allSettled(
      userIds.map(id => 
        UserService.updateUserRole(id, role, performerId, reason)
      )
    );

    const summary = {
      total: userIds.length,
      successful: results.filter(r => r.status === 'fulfilled').length,
      failed: results.filter(r => r.status === 'rejected').length,
      errors: results
        .map((r, i) => r.status === 'rejected' ? 
          { userId: userIds[i], error: (r as PromiseRejectedResult).reason.message } : 
          null)
        .filter(Boolean)
    };

    await SystemLogger.log({
      severity: 'info',
      category: 'user_management',
      action: 'bulk_role_update',
      performedBy: performerId,
      details: {
        targetUsers: userIds,
        newRole: role,
        reason,
        summary
      },
      status: 'success',
      timestamp: new Date()
    });

    res.json({
      success: true,
      message: 'Bulk role update completed',
      data: summary
    });
  }

  static async validateUsername(req: Request, res: Response) {
    const { username } = req.query;

    if (!username) {
      throw new AppError(400, 'Username is required');
    }

    const exists = await UserService.checkUsernameExists(username as string);

    res.json({
      success: true,
      data: { exists }
    });
  }

  static async exportUserData(req: Request, res: Response) {
    const currentUser = req.user;
    if (!currentUser?._id) {
      throw new AppError(401, 'User not authenticated');
    }

    const { format = 'csv', fields } = req.query;

    const exportData = await UserService.exportUsers(
      currentUser._id.toString(),
      format as string,
      fields ? (fields as string).split(',') : undefined
    );

    const filename = `users_export_${new Date().toISOString()}.${format}`;

    res.setHeader('Content-Disposition', `attachment; filename=${filename}`);
    res.setHeader('Content-Type', format === 'csv' ? 'text/csv' : 'application/json');
    res.send(exportData);
  }
}