// server/src/validations/schedule.validation.ts
import <PERSON><PERSON> from 'joi';

export const scheduleValidation = {
    getDailySchedule: Joi.object({
        query: Joi.object({
            date: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).required(),
            teacherId: Joi.string().optional(),
            roomId: Joi.string().optional(),
            classLevel: Joi.string().optional()
        }).unknown(true),
        body: Joi.object().unknown(true),
        params: Joi.object().unknown(true)
    }).unknown(true),

    getWeeklySchedule: Joi.object({
        query: Joi.object({
            startDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).required(),
            teacherId: Joi.string().optional(),
            roomId: Joi.string().optional(),
            classLevel: Joi.string().optional()
        }).unknown(true),
        body: Joi.object().unknown(true),
        params: Joi.object().unknown(true)
    }).unknown(true),

    getScheduleByRange: Joi.object({
        query: Joi.object({
            startDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).required(),
            endDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).required(),
            teacherId: Joi.string().optional(),
            roomId: Joi.string().optional(),
            classLevel: Joi.string().optional()
        }).unknown(true),
        body: Joi.object().unknown(true),
        params: Joi.object().unknown(true)
    }).unknown(true),

    getTeacherSchedule: Joi.object({
        params: Joi.object({
            teacherId: Joi.string().required()
        }),
        query: Joi.object({
            startDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).optional(),
            endDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).optional()
        }).unknown(true),
        body: Joi.object().unknown(true)
    }).unknown(true),

    getRoomSchedule: Joi.object({
        params: Joi.object({
            roomId: Joi.string().required()
        }),
        query: Joi.object({
            startDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).optional(),
            endDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).optional()
        }).unknown(true),
        body: Joi.object().unknown(true)
    }).unknown(true),

    getClassSchedule: Joi.object({
        params: Joi.object({
            classId: Joi.string().required()
        }),
        query: Joi.object({
            startDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).optional(),
            endDate: Joi.string().pattern(/^\d{4}-\d{2}-\d{2}$/).optional(),
            includeMakeup: Joi.boolean().optional()
        }).unknown(true),
        body: Joi.object().unknown(true)
    }).unknown(true)
};
