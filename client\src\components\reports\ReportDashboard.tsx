
import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { ReportOption, SavedReport } from "@/types/reports";
import { 
  FileText, Users, GraduationCap, ClipboardList, CalendarDays, 
  UserPlus, CreditCard, AlertCircle, BarChart2, Activity,
  Download, Eye, FileSpreadsheet
} from "lucide-react";

interface ReportDashboardProps {
  reportOptions: ReportOption[];
  savedReports: SavedReport[];
  isLoading: boolean;
  onCreateReport: (id: string) => void;
  onViewSavedReport: (id: string) => void;
  userRole: string;
}

export default function ReportDashboard({
  reportOptions,
  savedReports,
  isLoading,
  onCreateReport,
  onViewSavedReport,
  userRole
}: ReportDashboardProps) {
  const [activeTab, setActiveTab] = useState("quick-access");
  
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  // Group report options by role
  const teacherReports = reportOptions.filter(report => 
    report.role && report.role.includes('teacher'));
  const secretaryReports = reportOptions.filter(report => 
    report.role && report.role.includes('secretary'));
  const managerReports = reportOptions.filter(report => 
    report.role && report.role.includes('manager'));

  // Get recent reports (last 5)
  const recentReports = [...savedReports].sort(
    (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  ).slice(0, 5);

  return (
    <div className="space-y-6">
      {/* Report Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatCard 
          title="Total Reports"
          value={savedReports.length.toString()}
          icon={<FileText className="h-4 w-4 text-blue-600" />}
          description="All generated reports"
        />
        
        <StatCard 
          title="Available Templates"
          value="5"
          icon={<ClipboardList className="h-4 w-4 text-purple-600" />}
          description="Reusable report templates"
        />
        
        <StatCard 
          title="Downloads"
          value={(savedReports.reduce((total, report) => total + (report.downloadCount || 0), 0)).toString()}
          icon={<Download className="h-4 w-4 text-green-600" />}
          description="Total report downloads"
        />
        
        <StatCard 
          title="Report Types"
          value={reportOptions.length.toString()}
          icon={<FileSpreadsheet className="h-4 w-4 text-amber-600" />}
          description="Available report formats"
        />
      </div>

      {/* Quick Access Section */}
      <Tabs defaultValue="quick-access" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="quick-access">Quick Access</TabsTrigger>
          <TabsTrigger value="recent">Recent Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="quick-access">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {/* Quick access cards for common report types */}
            {reportOptions.slice(0, 6).map(report => (
              <QuickAccessCard 
                key={report.id}
                title={report.name}
                description={report.description}
                icon={getReportIcon(report.icon || "")}
                onClick={() => onCreateReport(report.id)}
              />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="recent">
          <div className="bg-card rounded-md border">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b bg-muted/50">
                    <th className="py-3 px-4 text-left text-sm font-medium">Report Name</th>
                    <th className="py-3 px-4 text-left text-sm font-medium">Type</th>
                    <th className="py-3 px-4 text-left text-sm font-medium">Format</th>
                    <th className="py-3 px-4 text-left text-sm font-medium">Generated</th>
                    <th className="py-3 px-4 text-left text-sm font-medium">Size</th>
                    <th className="py-3 px-4 text-right text-sm font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {recentReports.map(report => (
                    <tr key={report.id} className="border-b last:border-0 hover:bg-muted/30 group">
                      <td className="py-3 px-4 text-sm">{report.name}</td>
                      <td className="py-3 px-4 text-sm">{formatReportType(report.type)}</td>
                      <td className="py-3 px-4 text-sm uppercase">{report.format}</td>
                      <td className="py-3 px-4 text-sm">{formatDate(report.createdAt)}</td>
                      <td className="py-3 px-4 text-sm">{report.size || "N/A"}</td>
                      <td className="py-3 px-4 text-right">
                        <div className="flex justify-end gap-2">
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => onViewSavedReport(report.id)}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            View
                          </Button>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => window.open(report.url || "#", '_blank')}
                          >
                            <Download className="h-4 w-4 mr-1" />
                            Download
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </TabsContent>
      </Tabs>

      {/* Report Categories Section */}
      <div className="space-y-6">
        <h2 className="text-xl font-semibold">Report Categories</h2>
        
        {/* Teacher Reports Section */}
        {(userRole === 'teacher' || userRole === 'superadmin') && teacherReports.length > 0 && (
          <ReportCategorySection 
            title="Teacher Reports"
            description="Reports for tracking student progress and class attendance"
            reports={teacherReports}
            onCreateReport={onCreateReport}
          />
        )}
        
        {/* Secretary Reports Section */}
        {(userRole === 'secretary' || userRole === 'manager' || userRole === 'superadmin') && secretaryReports.length > 0 && (
          <ReportCategorySection 
            title="Secretary Reports"
            description="Reports for administrative and operational tasks"
            reports={secretaryReports}
            onCreateReport={onCreateReport}
          />
        )}
        
        {/* Manager Reports Section */}
        {(userRole === 'manager' || userRole === 'superadmin') && managerReports.length > 0 && (
          <ReportCategorySection 
            title="Manager Reports"
            description="Financial and performance analysis reports"
            reports={managerReports}
            onCreateReport={onCreateReport}
          />
        )}
      </div>
    </div>
  );
}

interface StatCardProps {
  title: string;
  value: string;
  icon: React.ReactNode;
  description: string;
}

function StatCard({ title, value, icon, description }: StatCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        {icon}
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        <p className="text-xs text-muted-foreground">{description}</p>
      </CardContent>
    </Card>
  );
}

interface QuickAccessCardProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  onClick: () => void;
}

function QuickAccessCard({ title, description, icon, onClick }: QuickAccessCardProps) {
  return (
    <div 
      className="bg-card rounded-lg border p-6 hover:shadow-md transition-all cursor-pointer"
      onClick={onClick}
    >
      <div className="flex items-center gap-3 mb-3">
        <div className="text-primary">{icon}</div>
        <h3 className="font-semibold">{title}</h3>
      </div>
      <p className="text-sm text-muted-foreground">{description}</p>
    </div>
  );
}

interface ReportCategorySectionProps {
  title: string;
  description: string;
  reports: ReportOption[];
  onCreateReport: (id: string) => void;
}

function ReportCategorySection({ title, description, reports, onCreateReport }: ReportCategorySectionProps) {
  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-semibold">{title}</h3>
        <p className="text-sm text-muted-foreground">{description}</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {reports.map(report => (
          <Card key={report.id} className="overflow-hidden">
            <CardHeader className="pb-2">
              <div className="flex items-center gap-2">
                {getReportIcon(report.icon || "")}
                <CardTitle className="text-base">{report.name}</CardTitle>
              </div>
              <CardDescription className="text-xs">
                {report.description}
              </CardDescription>
            </CardHeader>
            <CardFooter className="pt-2">
              <Button 
                variant="outline" 
                size="sm" 
                className="w-full"
                onClick={() => onCreateReport(report.id)}
              >
                Generate
              </Button>
            </CardFooter>
          </Card>
        ))}
      </div>
    </div>
  );
}

// Helper function to format report type
function formatReportType(type: string): string {
  return type
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

// Helper function to format date
function formatDate(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
}

// Helper function to get icon based on report icon name
function getReportIcon(iconName: string): React.ReactNode {
  switch (iconName) {
    case 'users':
      return <Users className="h-5 w-5" />;
    case 'graduation-cap':
      return <GraduationCap className="h-5 w-5" />;
    case 'clipboard-list':
      return <ClipboardList className="h-5 w-5" />;
    case 'calendar':
      return <CalendarDays className="h-5 w-5" />;
    case 'user-plus':
      return <UserPlus className="h-5 w-5" />;
    case 'credit-card':
      return <CreditCard className="h-5 w-5" />;
    case 'alert-circle':
      return <AlertCircle className="h-5 w-5" />;
    case 'bar-chart-2':
      return <BarChart2 className="h-5 w-5" />;
    case 'activity':
      return <Activity className="h-5 w-5" />;
    default:
      return <FileText className="h-5 w-5" />;
  }
}
