
import { useState, useEffect } from "react";
import { getCurrentUser } from "@/lib/auth";
import { UserRole } from "@/types";
import { Calendar } from "lucide-react";
import { getClassStatistics, getStudentStatistics } from "@/services/dashboardService";

interface WelcomeBarProps {}

const WelcomeBar = ({}: WelcomeBarProps) => {
  const [userData, setUserData] = useState<{name: string, role: UserRole} | null>(null);
  const [stats, setStats] = useState<{totalStudents: number, activeClasses: number}>({
    totalStudents: 0,
    activeClasses: 0
  });
  const [isLoading, setIsLoading] = useState(true);

  const currentDate = new Date();
  const formattedDate = new Intl.DateTimeFormat('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(currentDate);

  // Get user data and stats on component mount
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);

        // Fetch user data
        const user = await getCurrentUser();
        if (user) {
          setUserData({
            name: user.name || 'User',
            role: user.role || 'Guest'
          });
        }

        // Fetch dashboard stats
        const [classStats, studentStats] = await Promise.all([
          getClassStatistics(),
          getStudentStatistics()
        ]);

        setStats({
          totalStudents: studentStats.totalStudents,
          activeClasses: classStats.activeClasses
        });
      } catch (error) {
        console.error('Error fetching dashboard data:', error);
        // Keep default values (0, 0) on error
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  // Get greeting based on time of day
  const getGreeting = () => {
    const hour = currentDate.getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  };

  // Get role-specific welcome message
  const getWelcomeMessage = (role: UserRole) => {
    if (isLoading) {
      return "Loading dashboard data...";
    }

    switch (role) {
      case 'superAdmin':
        return `You have ${stats.totalStudents} students and ${stats.activeClasses} active classes`;
      case 'manager':
        return `Manage ${stats.activeClasses} active classes with ${stats.totalStudents} students`;
      case 'secretary':
        return `Assist with ${stats.totalStudents} students across ${stats.activeClasses} classes`;
      case 'teacher':
        return `You're teaching classes with a total of ${stats.totalStudents} students`;
      case 'student':
        return `You're enrolled in classes`;
      default:
        return `Welcome to Vertex Education`;
    }
  };

  if (!userData) return null;

  return (
    <div className="bg-blue-50 border border-blue-100 rounded-xl p-6 mb-6 animate-slide-down">
      <div className="flex flex-col md:flex-row md:items-center justify-between">
        <div className="space-y-2">
          <h1 className="text-2xl font-semibold text-blue-900">
            {getGreeting()}, {userData.name}
          </h1>
          <p className="text-blue-700 text-sm">
            {getWelcomeMessage(userData.role)}
          </p>
        </div>
        <div className="flex items-center mt-3 md:mt-0 space-x-1.5 text-blue-700 text-sm">
          <Calendar size={16} />
          <span>{formattedDate}</span>
        </div>
      </div>
    </div>
  );
};

export default WelcomeBar;
