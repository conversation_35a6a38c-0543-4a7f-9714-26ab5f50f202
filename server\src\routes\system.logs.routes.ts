import express, { Request, Response } from 'express';
import { authenticateToken, authorizeRoles } from '../middleware/auth.middleware';
import { catchAsync } from '../middleware/error.middleware';
import { SystemLogger } from '../services/logger.service';

type LogSeverity = 'info' | 'warning' | 'error';

interface SystemLogsQuery {
  page?: string;
  limit?: string;
  severity?: string | string[];
  categories?: string | string[];
  fromDate?: string;
  toDate?: string;
  search?: string;
}

const isValidSeverity = (severity: string): severity is LogSeverity => {
  return ['info', 'warning', 'error'].includes(severity);
};

const validateAndParseSeverity = (severity: string | string[] | undefined): LogSeverity[] | undefined => {
  if (!severity) return undefined;
  
  const severities = Array.isArray(severity) ? severity : [severity];
  const validSeverities = severities.filter(isValidSeverity);
  
  return validSeverities.length > 0 ? validSeverities : undefined;
};

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);
router.use(authorizeRoles('superAdmin'));

router.get('/', catchAsync(async (req: Request<{}, {}, {}, SystemLogsQuery>, res: Response) => {
  const {
    page = '1',
    limit = '25',
    severity,
    categories,
    fromDate,
    toDate,
    search
  } = req.query;

  const options = {
    page: parseInt(page),
    limit: parseInt(limit),
    severity: validateAndParseSeverity(severity),
    categories: categories ? (Array.isArray(categories) ? categories : [categories]) : undefined,
    fromDate: fromDate ? new Date(fromDate) : undefined,
    toDate: toDate ? new Date(toDate) : undefined,
    search
  };

  const result = await SystemLogger.getSystemActivities(options);
  res.json(result);
}));

export default router;