
import { useState, useEffect } from 'react';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { AlertCircle, CheckCircle2, Loader2 } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/components/ui/use-toast';

export default function NetworkConfig() {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [isTesting, setIsTesting] = useState(false);
  const [isChanged, setIsChanged] = useState(false);
  const [activeTab, setActiveTab] = useState('general');

  // Mock settings
  const [settings, setSettings] = useState({
    apiRateLimiting: {
      enabled: true,
      requestsPerMinute: '60',
      burstLimit: '100',
    },
    timeouts: {
      connectionTimeout: '30',
      readTimeout: '60',
      writeTimeout: '60',
    },
    cors: {
      enabled: true,
      allowCredentials: true,
      maxAge: '86400',  // Use string for consistency with form data
      allowedMethods: ['GET', 'POST', 'PUT', 'DELETE'],
      allowedOrigins: ['https://example.com', 'https://app.example.com'],
    },
    ipRestriction: {
      enabled: false,
      whitelistedIPs: ['***********', '********'],
      blacklistedIPs: [],
    },
  });

  // Handle form submission
  const handleSubmit = async () => {
    try {
      setIsLoading(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      toast({
        title: 'Settings Updated',
        description: 'Network configuration has been successfully updated.',
      });
      setIsChanged(false);
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to update network settings.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle test connection
  const handleTestConnection = async () => {
    try {
      setIsTesting(true);
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      toast({
        title: 'Connection Test Successful',
        description: 'All endpoints are reachable and responding correctly.',
      });
    } catch (error) {
      toast({
        title: 'Connection Test Failed',
        description: 'One or more endpoints are not responding correctly.',
        variant: 'destructive',
      });
    } finally {
      setIsTesting(false);
    }
  };

  // Handle setting changes
  const handleSettingChange = (section: string, key: string, value: any) => {
    setSettings(prevSettings => {
      // Create a deep copy to avoid mutation
      const newSettings = { ...prevSettings };
      
      // Update the specific section's key
      if (section === 'cors' && key === 'maxAge') {
        // Convert maxAge to string to match the expected type
        newSettings.cors.maxAge = String(value);
      } else if (section in newSettings) {
        (newSettings as any)[section][key] = value;
      }
      
      return newSettings;
    });
    setIsChanged(true);
  };

  return (
    <Tabs value={activeTab} onValueChange={setActiveTab}>
      <TabsList className="mb-6">
        <TabsTrigger value="general">General</TabsTrigger>
        <TabsTrigger value="cors">CORS Settings</TabsTrigger>
        <TabsTrigger value="security">Security</TabsTrigger>
        <TabsTrigger value="advanced">Advanced</TabsTrigger>
      </TabsList>

      <TabsContent value="general" className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>API Rate Limiting</CardTitle>
            <CardDescription>
              Configure request limits to prevent API abuse
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Enable Rate Limiting</h4>
                <p className="text-sm text-muted-foreground">
                  Limit the number of requests per client
                </p>
              </div>
              <Switch
                checked={settings.apiRateLimiting.enabled}
                onCheckedChange={(checked) => handleSettingChange('apiRateLimiting', 'enabled', checked)}
              />
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div>
                <FormLabel>Requests Per Minute</FormLabel>
                <Input
                  type="number"
                  value={settings.apiRateLimiting.requestsPerMinute}
                  onChange={(e) => handleSettingChange('apiRateLimiting', 'requestsPerMinute', e.target.value)}
                  disabled={!settings.apiRateLimiting.enabled}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Maximum requests allowed per minute per IP
                </p>
              </div>

              <div>
                <FormLabel>Burst Limit</FormLabel>
                <Input
                  type="number"
                  value={settings.apiRateLimiting.burstLimit}
                  onChange={(e) => handleSettingChange('apiRateLimiting', 'burstLimit', e.target.value)}
                  disabled={!settings.apiRateLimiting.enabled}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Maximum concurrent requests allowed
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Timeout Settings</CardTitle>
            <CardDescription>
              Configure network timeouts for API operations
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-3">
              <div>
                <FormLabel>Connection Timeout (s)</FormLabel>
                <Input
                  type="number"
                  value={settings.timeouts.connectionTimeout}
                  onChange={(e) => handleSettingChange('timeouts', 'connectionTimeout', e.target.value)}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Time to establish connection
                </p>
              </div>

              <div>
                <FormLabel>Read Timeout (s)</FormLabel>
                <Input
                  type="number"
                  value={settings.timeouts.readTimeout}
                  onChange={(e) => handleSettingChange('timeouts', 'readTimeout', e.target.value)}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Time to wait for server response
                </p>
              </div>

              <div>
                <FormLabel>Write Timeout (s)</FormLabel>
                <Input
                  type="number"
                  value={settings.timeouts.writeTimeout}
                  onChange={(e) => handleSettingChange('timeouts', 'writeTimeout', e.target.value)}
                />
                <p className="text-xs text-muted-foreground mt-1">
                  Time to complete sending request
                </p>
              </div>
            </div>
          </CardContent>
          <CardFooter>
            <Button
              variant="secondary"
              onClick={handleTestConnection}
              disabled={isTesting}
              className="mr-2"
            >
              {isTesting ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Testing...
                </>
              ) : (
                'Test Connection'
              )}
            </Button>
          </CardFooter>
        </Card>
      </TabsContent>

      <TabsContent value="cors" className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Cross-Origin Resource Sharing (CORS)</CardTitle>
            <CardDescription>
              Configure which domains can access your API
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Enable CORS</h4>
                <p className="text-sm text-muted-foreground">
                  Allow cross-origin requests to your API
                </p>
              </div>
              <Switch
                checked={settings.cors.enabled}
                onCheckedChange={(checked) => handleSettingChange('cors', 'enabled', checked)}
              />
            </div>

            <div>
              <FormLabel>Allowed Origins</FormLabel>
              <div className="space-y-2 mt-2">
                {settings.cors.allowedOrigins.map((origin, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <Input
                      value={origin}
                      onChange={(e) => {
                        const newOrigins = [...settings.cors.allowedOrigins];
                        newOrigins[index] = e.target.value;
                        handleSettingChange('cors', 'allowedOrigins', newOrigins);
                      }}
                      disabled={!settings.cors.enabled}
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        const newOrigins = settings.cors.allowedOrigins.filter((_, i) => i !== index);
                        handleSettingChange('cors', 'allowedOrigins', newOrigins);
                      }}
                      disabled={!settings.cors.enabled}
                    >
                      Remove
                    </Button>
                  </div>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newOrigins = [...settings.cors.allowedOrigins, ''];
                    handleSettingChange('cors', 'allowedOrigins', newOrigins);
                  }}
                  disabled={!settings.cors.enabled}
                >
                  Add Origin
                </Button>
              </div>
              <p className="text-xs text-muted-foreground mt-2">
                Specify which domains can access your API. Use * for all origins (not recommended for production).
              </p>
            </div>

            <div className="flex items-center space-x-2">
              <Checkbox 
                id="allowCredentials"
                checked={settings.cors.allowCredentials}
                onCheckedChange={(checked) => handleSettingChange('cors', 'allowCredentials', !!checked)}
                disabled={!settings.cors.enabled}
              />
              <label
                htmlFor="allowCredentials"
                className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                Allow Credentials
              </label>
            </div>

            <div>
              <FormLabel>Max Age (seconds)</FormLabel>
              <Input
                type="number"
                value={settings.cors.maxAge}
                onChange={(e) => handleSettingChange('cors', 'maxAge', e.target.value)}
                disabled={!settings.cors.enabled}
              />
              <p className="text-xs text-muted-foreground mt-1">
                How long the results of a preflight request can be cached
              </p>
            </div>

            <div>
              <FormLabel>Allowed Methods</FormLabel>
              <div className="flex flex-wrap gap-2 mt-2">
                {['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS', 'HEAD'].map((method) => (
                  <div key={method} className="flex items-center space-x-2">
                    <Checkbox
                      id={`method-${method}`}
                      checked={settings.cors.allowedMethods.includes(method)}
                      onCheckedChange={(checked) => {
                        const newMethods = checked
                          ? [...settings.cors.allowedMethods, method]
                          : settings.cors.allowedMethods.filter((m) => m !== method);
                        handleSettingChange('cors', 'allowedMethods', newMethods);
                      }}
                      disabled={!settings.cors.enabled}
                    />
                    <label
                      htmlFor={`method-${method}`}
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {method}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="security" className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>IP Restrictions</CardTitle>
            <CardDescription>
              Control access to your API based on IP addresses
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium">Enable IP Restrictions</h4>
                <p className="text-sm text-muted-foreground">
                  Restrict API access based on IP address
                </p>
              </div>
              <Switch
                checked={settings.ipRestriction.enabled}
                onCheckedChange={(checked) => handleSettingChange('ipRestriction', 'enabled', checked)}
              />
            </div>

            <div>
              <FormLabel>Whitelisted IPs</FormLabel>
              <div className="space-y-2 mt-2">
                {settings.ipRestriction.whitelistedIPs.map((ip, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <Input
                      value={ip}
                      onChange={(e) => {
                        const newIPs = [...settings.ipRestriction.whitelistedIPs];
                        newIPs[index] = e.target.value;
                        handleSettingChange('ipRestriction', 'whitelistedIPs', newIPs);
                      }}
                      disabled={!settings.ipRestriction.enabled}
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        const newIPs = settings.ipRestriction.whitelistedIPs.filter((_, i) => i !== index);
                        handleSettingChange('ipRestriction', 'whitelistedIPs', newIPs);
                      }}
                      disabled={!settings.ipRestriction.enabled}
                    >
                      Remove
                    </Button>
                  </div>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newIPs = [...settings.ipRestriction.whitelistedIPs, ''];
                    handleSettingChange('ipRestriction', 'whitelistedIPs', newIPs);
                  }}
                  disabled={!settings.ipRestriction.enabled}
                >
                  Add IP
                </Button>
              </div>
              <p className="text-xs text-muted-foreground mt-2">
                IPs that are allowed to access your API. Empty list allows all IPs.
              </p>
            </div>

            <div>
              <FormLabel>Blacklisted IPs</FormLabel>
              <div className="space-y-2 mt-2">
                {settings.ipRestriction.blacklistedIPs.map((ip, index) => (
                  <div key={index} className="flex items-center gap-2">
                    <Input
                      value={ip}
                      onChange={(e) => {
                        const newIPs = [...settings.ipRestriction.blacklistedIPs];
                        newIPs[index] = e.target.value;
                        handleSettingChange('ipRestriction', 'blacklistedIPs', newIPs);
                      }}
                      disabled={!settings.ipRestriction.enabled}
                    />
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        const newIPs = settings.ipRestriction.blacklistedIPs.filter((_, i) => i !== index);
                        handleSettingChange('ipRestriction', 'blacklistedIPs', newIPs);
                      }}
                      disabled={!settings.ipRestriction.enabled}
                    >
                      Remove
                    </Button>
                  </div>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    const newIPs = [...settings.ipRestriction.blacklistedIPs, ''];
                    handleSettingChange('ipRestriction', 'blacklistedIPs', newIPs);
                  }}
                  disabled={!settings.ipRestriction.enabled}
                >
                  Add IP
                </Button>
              </div>
              <p className="text-xs text-muted-foreground mt-2">
                IPs that are blocked from accessing your API.
              </p>
            </div>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="advanced" className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Advanced Settings</CardTitle>
            <CardDescription>
              Additional network configuration options
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground text-sm">
              Advanced settings are currently unavailable.
            </p>
          </CardContent>
        </Card>
      </TabsContent>

      <div className="mt-6 flex items-center justify-between">
        {isChanged && (
          <div className="flex items-center text-amber-500 text-sm">
            <AlertCircle className="h-4 w-4 mr-2" />
            You have unsaved changes
          </div>
        )}
        <div className="ml-auto">
          <Button
            onClick={handleSubmit}
            disabled={isLoading || !isChanged}
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Saving...
              </>
            ) : (
              'Save Changes'
            )}
          </Button>
        </div>
      </div>
    </Tabs>
  );
}
