
import { Student } from "./student";

export type PaymentMethod = 'cash' | 'bank_transfer' | 'other';
export type PaymentStatus = 'completed' | 'pending' | 'voided';
export type PaymentPeriod = 'monthly' | 'quarterly' | 'semi_annual' | 'annual';

export interface PaymentAttachment {
  id: string;
  fileName: string;
  fileUrl: string;
  uploadedAt: string;
  uploadedBy: string;
}

export interface Payment {
  id: string;
  studentId: string;
  student?: {
    id: string;
    firstName: string;
    lastName: string;
  };
  amount: number;
  remainingBalance?: number;
  status: PaymentStatus;
  method: PaymentMethod;
  date: string;
  nextDueDate?: string;
  description: string;
  period?: PaymentPeriod;
  recordedBy: string;
  recordedAt: string;
  modifiedBy?: string;
  modifiedAt?: string;
  voidedBy?: string;
  voidedAt?: string;
  voidReason?: string;
  receiptNumber?: string;
  notes?: string;
  attachments?: PaymentAttachment[];
}

export interface PaymentFilter {
  page: number;
  limit: number;
  sortBy?: 'date' | 'amount' | 'studentName' | 'status' | 'method' | 'recordedAt';
  sortOrder?: 'asc' | 'desc';
  status?: PaymentStatus;
  method?: PaymentMethod;
  studentId?: string;
  startDate?: string;
  endDate?: string;
  minAmount?: number;
  maxAmount?: number;
  dueDateStart?: string;
  dueDateEnd?: string;
  period?: PaymentPeriod;
  search?: string;
}

export interface PaymentStatisticsPeriod {
  label: string;
  value: number;
  count: number;
}

export interface PaymentMethodStatistic {
  method: PaymentMethod;
  value: number;
  count: number;
  percentage: number;
}

export interface PaymentStatusStatistic {
  status: PaymentStatus;
  value: number;
  count: number;
  percentage: number;
}

export interface PaymentStatistics {
  totalAmount: number;
  totalCount: number;
  periodBreakdown: PaymentStatisticsPeriod[];
  methodBreakdown: PaymentMethodStatistic[];
  statusBreakdown: PaymentStatusStatistic[];
  overdueCount: number;
  overdueAmount: number;
  upcomingCount: number;
}

export interface PaymentApiResponse {
  success: boolean;
  data: Payment[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}

export interface PaymentDetailsApiResponse {
  success: boolean;
  data: Payment;
}

export interface PaymentStatisticsApiResponse {
  success: boolean;
  data: PaymentStatistics;
}
