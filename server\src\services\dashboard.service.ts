// server/src/services/dashboard.service.ts
import { Student } from '../models/student.model';
import { Class } from '../models/class.model';
import { Attendance } from '../models/attendance.model';
import { Payment } from '../models/payment.model';
import { Room } from '../models/room.model';
import { AppError } from '../types/error.types';

interface DateRange {
    fromDate?: Date;
    toDate?: Date;
    level?: string;
}

interface PaymentDateRange {
    startDate?: Date;
    endDate?: Date;
}

export class DashboardService {
    static async getDashboardData(
        userRole: string,
        dateRange: DateRange,
        userId: string
    ) {
        try {
            const data: any = {};

            // Get basic statistics for all roles
            data.classStats = await this.getClassStatistics(dateRange, userId);
            data.studentStats = await this.getStudentStatistics(dateRange, userId);

            // Role-specific data
            switch (userRole) {
                case 'superAdmin':
                    data.paymentStats = await this.getPaymentStatistics({
                        startDate: dateRange.fromDate,
                        endDate: dateRange.toDate
                    }, userId);
                    data.roomUtilization = await this.getRoomUtilization({
                        startDate: dateRange.fromDate,
                        endDate: dateRange.toDate
                    }, userId);
                    data.systemActivity = await this.getSystemActivity({
                        fromDate: dateRange.fromDate,
                        toDate: dateRange.toDate,
                        limit: 10
                    }, userId);
                    break;

                case 'manager':
                    data.attendanceStats = await this.getAttendanceStatistics(dateRange, userId);
                    data.paymentStats = await this.getPaymentStatistics({
                        startDate: dateRange.fromDate,
                        endDate: dateRange.toDate
                    }, userId);
                    data.roomUtilization = await this.getRoomUtilization({
                        startDate: dateRange.fromDate,
                        endDate: dateRange.toDate
                    }, userId);
                    break;

                case 'secretary':
                    data.paymentStats = await this.getPaymentStatistics({
                        startDate: dateRange.fromDate,
                        endDate: dateRange.toDate
                    }, userId);
                    data.systemActivity = await this.getSystemActivity({
                        fromDate: dateRange.fromDate,
                        toDate: dateRange.toDate,
                        limit: 5
                    }, userId);
                    break;

                case 'teacher':
                    data.attendanceStats = await this.getAttendanceStatistics(dateRange, userId);
                    break;
            }

            return data;
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error fetching dashboard data');
        }
    }

    static async getClassStatistics(dateRange: DateRange, userId: string) {
        try {
            const query: any = {};
            
            if (dateRange.fromDate || dateRange.toDate) {
                query.createdAt = {};
                if (dateRange.fromDate) query.createdAt.$gte = dateRange.fromDate;
                if (dateRange.toDate) query.createdAt.$lte = dateRange.toDate;
            }

            const classes = await Class.find(query);
            
            const totalClasses = classes.length;
            const activeClasses = classes.filter(cls => cls.status === 'active').length;
            const inactiveClasses = totalClasses - activeClasses;

            // Group by level
            const levelCounts = classes.reduce((acc: any, cls) => {
                acc[cls.level] = (acc[cls.level] || 0) + 1;
                return acc;
            }, {});

            const classesByLevel = Object.entries(levelCounts).map(([level, count]) => ({
                level,
                count: count as number,
                percentage: totalClasses > 0 ? ((count as number) / totalClasses) * 100 : 0
            }));

            // Group by capacity ranges
            const capacityRanges = {
                '1-10 students': classes.filter(cls => cls.capacity <= 10).length,
                '11-20 students': classes.filter(cls => cls.capacity > 10 && cls.capacity <= 20).length,
                '21-30 students': classes.filter(cls => cls.capacity > 20 && cls.capacity <= 30).length,
                '30+ students': classes.filter(cls => cls.capacity > 30).length
            };

            const classesByCapacity = Object.entries(capacityRanges).map(([range, count]) => ({
                range,
                count,
                percentage: totalClasses > 0 ? (count / totalClasses) * 100 : 0
            }));

            const totalStudents = classes.reduce((sum, cls) => sum + cls.currentStudentCount, 0);
            const averageClassSize = totalClasses > 0 ? totalStudents / totalClasses : 0;

            // Mock enrollment trend for now (can be enhanced later)
            const enrollmentTrend = [
                { date: "2023-01", count: 0 },
                { date: "2023-02", count: 0 },
                { date: "2023-03", count: 0 },
                { date: "2023-04", count: 0 },
                { date: "2023-05", count: 0 },
                { date: "2023-06", count: totalStudents }
            ];

            return {
                totalClasses,
                activeClasses,
                inactiveClasses,
                classesByLevel,
                classesByCapacity,
                averageClassSize,
                enrollmentTrend
            };
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error fetching class statistics');
        }
    }

    static async getStudentStatistics(dateRange: DateRange, userId: string) {
        try {
            const query: any = {};
            
            if (dateRange.fromDate || dateRange.toDate) {
                query.createdAt = {};
                if (dateRange.fromDate) query.createdAt.$gte = dateRange.fromDate;
                if (dateRange.toDate) query.createdAt.$lte = dateRange.toDate;
            }

            const students = await Student.find(query);
            
            const totalStudents = students.length;
            const activeStudents = students.filter(student => student.status === 'active').length;
            const inactiveStudents = students.filter(student => student.status === 'inactive').length;
            const pendingStudents = 0; // No pending status in student model

            // Group by level (using currentLevel from student model)
            const levelCounts = students.reduce((acc: any, student) => {
                acc[student.currentLevel] = (acc[student.currentLevel] || 0) + 1;
                return acc;
            }, {});

            const studentsByLevel = Object.entries(levelCounts).map(([level, count]) => ({
                level,
                count: count as number,
                percentage: totalStudents > 0 ? ((count as number) / totalStudents) * 100 : 0
            }));

            // Mock registration trend for now (can be enhanced later)
            const registrationTrend = [
                { date: "2023-01", count: 0 },
                { date: "2023-02", count: 0 },
                { date: "2023-03", count: 0 },
                { date: "2023-04", count: 0 },
                { date: "2023-05", count: 0 },
                { date: "2023-06", count: totalStudents }
            ];

            const retentionRate = totalStudents > 0 ? (activeStudents / totalStudents) * 100 : 0;

            return {
                totalStudents,
                activeStudents,
                inactiveStudents,
                pendingStudents,
                studentsByLevel,
                registrationTrend,
                retentionRate
            };
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error fetching student statistics');
        }
    }

    static async getAttendanceStatistics(dateRange: DateRange, userId: string) {
        try {
            // Basic attendance statistics - can be enhanced
            return {
                overallAttendanceRate: 0,
                attendanceByClass: [],
                attendanceTrend: [],
                lateArrivals: 0,
                excusedAbsences: 0
            };
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error fetching attendance statistics');
        }
    }

    static async getPaymentStatistics(dateRange: PaymentDateRange, userId: string) {
        try {
            // Basic payment statistics - can be enhanced
            return {
                totalRevenue: 0,
                pendingPayments: 0,
                completedPayments: 0,
                overduePayments: 0,
                revenueByMonth: [],
                paymentsByMethod: []
            };
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error fetching payment statistics');
        }
    }

    static async getRoomUtilization(dateRange: PaymentDateRange, userId: string) {
        try {
            // Basic room utilization - can be enhanced
            return {
                overallUtilization: 0,
                roomUtilizationByDay: [],
                peakHours: [],
                underutilizedRooms: []
            };
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error fetching room utilization');
        }
    }

    static async getSystemActivity(params: { fromDate?: Date; toDate?: Date; limit?: number }, userId: string) {
        try {
            // Basic system activity - can be enhanced
            return {
                recentActivities: [],
                userActivity: [],
                activityByType: []
            };
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error fetching system activity');
        }
    }
}
