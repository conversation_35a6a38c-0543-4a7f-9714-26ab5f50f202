
import { Student } from "@/types/student";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { PhoneCall, Mail, MapPin, Calendar, Book, Users, CreditCard } from "lucide-react";

interface StudentOverviewTabProps {
  student: Student;
}

const StudentOverviewTab = ({ student }: StudentOverviewTabProps) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {/* Basic Information */}
      <Card className="md:col-span-2">
        <CardHeader>
          <CardTitle>Basic Information</CardTitle>
          <CardDescription>Personal details and contact information</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Full Name</h3>
              <p className="text-base">{student.firstName} {student.lastName}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Status</h3>
              <Badge
                variant="outline"
                className={cn(
                  "mt-1 capitalize",
                  student.status === "active" && "border-green-200 bg-green-50 text-green-700 dark:bg-green-900/30 dark:text-green-400 dark:border-green-900",
                  student.status === "inactive" && "border-red-200 bg-red-50 text-red-700 dark:bg-red-900/30 dark:text-red-400 dark:border-red-900",
                  student.status === "pending" && "border-yellow-200 bg-yellow-50 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400 dark:border-yellow-900",
                  student.status === "archived" && "border-gray-200 bg-gray-50 text-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-700"
                )}
              >
                {student.status}
              </Badge>
            </div>
          </div>

          <div className="space-y-3">
            <h3 className="text-sm font-medium text-muted-foreground">Contact Information</h3>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <PhoneCall size={16} className="text-muted-foreground" />
                <span>{student.contactInfo.phone || "No phone number"}</span>
              </div>
              <div className="flex items-center gap-2">
                <Mail size={16} className="text-muted-foreground" />
                <span>{student.contactInfo.email || "No email address"}</span>
              </div>
              <div className="flex items-start gap-2">
                <MapPin size={16} className="text-muted-foreground mt-1" />
                <span>{student.contactInfo.address || "No address"}</span>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Registration Date</h3>
              <div className="flex items-center gap-2 mt-1">
                <Calendar size={16} className="text-muted-foreground" />
                <span>{formatDate(student.registeredAt)}</span>
              </div>
            </div>
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Registered By</h3>
              <p className="text-base mt-1">{student.registeredBy}</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Current Class & Level */}
      <Card>
        <CardHeader>
          <CardTitle>Current Status</CardTitle>
          <CardDescription>Level and class information</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Current Level</h3>
            <div className="flex items-center gap-2 mt-1">
              <Book size={16} className="text-muted-foreground" />
              <span>{student.currentLevel}</span>
            </div>
          </div>

          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Current Class</h3>
            <div className="flex items-center gap-2 mt-1">
              <Users size={16} className="text-muted-foreground" />
              <span>{student.currentClass?.name || "Not assigned"}</span>
            </div>
          </div>

          <div>
            <h3 className="text-sm font-medium text-muted-foreground">Payment Status</h3>
            <div className="flex items-center gap-2 mt-1">
              <CreditCard size={16} className="text-muted-foreground" />
              <Badge
                variant="outline"
                className={cn(
                  "capitalize",
                  student.paymentStatus === "paid" && "border-green-200 bg-green-50 text-green-700 dark:bg-green-900/30 dark:text-green-400 dark:border-green-900",
                  student.paymentStatus === "pending" && "border-yellow-200 bg-yellow-50 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400 dark:border-yellow-900",
                  student.paymentStatus === "overdue" && "border-red-200 bg-red-50 text-red-700 dark:bg-red-900/30 dark:text-red-400 dark:border-red-900"
                )}
              >
                {student.paymentStatus || "Unknown"}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default StudentOverviewTab;
