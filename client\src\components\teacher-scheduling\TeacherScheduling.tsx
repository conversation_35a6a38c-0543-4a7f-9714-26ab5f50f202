import React, { useState, useEffect, useCallback } from 'react';
import { Calendar } from "@/components/ui/calendar"
import { CalendarIcon, Check, Plus, Trash2 } from "lucide-react";
import { format } from "date-fns";

import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Class, Teacher, TeacherScheduleItem } from "@/types/class";
import { fetchClassById } from "@/services/classService";
import { useQuery } from "@tanstack/react-query";
import { useParams } from "react-router-dom";
import { toast } from 'sonner';

interface TeacherSchedulingProps {
  classId?: string;
}

const TeacherScheduling: React.FC<TeacherSchedulingProps> = ({ classId }) => {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
  const [teacherSchedules, setTeacherSchedules] = useState<Map<string, TeacherScheduleItem[]>>(new Map());
  const [availableTeachers, setAvailableTeachers] = useState<Teacher[]>([]);
  const [selectedTeacher, setSelectedTeacher] = useState<string | null>(null);
  const [startTime, setStartTime] = useState<string>('09:00');
  const [endTime, setEndTime] = useState<string>('10:00');
  const [isAdding, setIsAdding] = useState<boolean>(false);

  const { data: classData, isLoading, error } = useQuery({
    queryKey: ['class', classId],
    queryFn: () => fetchClassById(classId || ''),
    enabled: !!classId,
  });

  useEffect(() => {
    if (classData?.data) {
      setAvailableTeachers(classData.data.teachers);
    }
  }, [classData]);

  const handleDateSelect = (date: Date | undefined) => {
    setSelectedDate(date);
  };

  const handleTeacherSelect = (teacherId: string) => {
    setSelectedTeacher(teacherId);
  };

  const handleStartTimeChange = (time: string) => {
    setStartTime(time);
  };

  const handleEndTimeChange = (time: string) => {
    setEndTime(time);
  };

  const addScheduleItem = useCallback(() => {
    if (!selectedDate) {
      toast.error('Please select a date.');
      return;
    }

    if (!selectedTeacher) {
      toast.error('Please select a teacher.');
      return;
    }

    const day = format(selectedDate, 'EEEE').toLowerCase();

    const newScheduleItem: TeacherScheduleItem = {
      day: day,
      timeStart: startTime,
      timeEnd: endTime,
    };

    setTeacherSchedules((prevSchedules) => {
      const existingSchedule = prevSchedules.get(selectedTeacher) || [];
      const updatedSchedule = [...existingSchedule, newScheduleItem];
      const newMap = new Map(prevSchedules);
      newMap.set(selectedTeacher, updatedSchedule);
      return new Map(newMap);
    });

    setIsAdding(false);
    toast.success('Schedule item added successfully.');
  }, [selectedDate, selectedTeacher, startTime, endTime]);

  const removeScheduleItem = (teacherId: string, index: number) => {
    setTeacherSchedules((prevSchedules) => {
      const existingSchedule = prevSchedules.get(teacherId) || [];
      const updatedSchedule = existingSchedule.filter((_, i) => i !== index);
      const newMap = new Map(prevSchedules);
      newMap.set(teacherId, updatedSchedule);
      return new Map(newMap);
    });
    toast.success('Schedule item removed successfully.');
  };

  const getTeacherSchedulesForDay = (teacherId: string, day: string): TeacherScheduleItem[] => {
    const schedules = teacherSchedules.get(teacherId) || [];
    return schedules.filter(item => item.day === day);
  };

  const daysOfWeek = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"];

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error.message}</div>;
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="text-2xl font-bold">Teacher Scheduling</div>
      <Card>
        <CardHeader>
          <CardTitle>Select Date</CardTitle>
          <CardDescription>Choose a date to view and manage teacher schedules.</CardDescription>
        </CardHeader>
        <CardContent className="grid gap-4">
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant={"outline"}
                className={cn(
                  "w-[240px] justify-start text-left font-normal",
                  !selectedDate && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {selectedDate ? format(selectedDate, "PPP") : <span>Pick a date</span>}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={handleDateSelect}
                disabled={(date) =>
                  date < new Date(new Date().setDate(new Date().getDate() - 7))
                }
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </CardContent>
      </Card>

      {selectedDate && (
        <Card>
          <CardHeader>
            <CardTitle>Manage Schedules for {format(selectedDate, "PPP")}</CardTitle>
            <CardDescription>Add and remove teacher schedules for the selected date.</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4">
              <Select onValueChange={handleTeacherSelect}>
                <SelectTrigger className="w-[240px]">
                  <SelectValue placeholder="Select a teacher" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    {availableTeachers.map((teacher) => (
                      <SelectItem key={teacher.id} value={teacher.id}>
                        {teacher.name}
                      </SelectItem>
                    ))}
                  </SelectGroup>
                </SelectContent>
              </Select>

              {selectedTeacher && (
                <>
                  <div className="flex items-center space-x-2">
                    <Label htmlFor="start-time">Start Time</Label>
                    <Input
                      type="time"
                      id="start-time"
                      value={startTime}
                      onChange={(e) => handleStartTimeChange(e.target.value)}
                    />
                    <Label htmlFor="end-time">End Time</Label>
                    <Input
                      type="time"
                      id="end-time"
                      value={endTime}
                      onChange={(e) => handleEndTimeChange(e.target.value)}
                    />
                    <Button size="sm" onClick={() => setIsAdding(true)}>
                      <Plus className="h-4 w-4 mr-2" />
                      Add Schedule
                    </Button>
                  </div>

                  {isAdding && (
                    <div className="flex justify-center">
                      <Button size="sm" onClick={addScheduleItem}>
                        Confirm Add
                      </Button>
                    </div>
                  )}

                  <Card className="mt-4">
                    <CardHeader>
                      <CardTitle>
                        {availableTeachers.find((t) => t.id === selectedTeacher)?.name}'s Schedule
                      </CardTitle>
                      <CardDescription>
                        Here are the schedules for the selected teacher on {format(selectedDate, "PPP")}.
                      </CardDescription>
                    </CardHeader>
                    <CardContent>
                      {daysOfWeek.map((day) => {
                        const schedulesForDay = getTeacherSchedulesForDay(selectedTeacher, day);
                        if (schedulesForDay.length === 0) {
                          return null;
                        }
                        return (
                          <div key={day} className="mb-4">
                            <h3 className="text-lg font-semibold capitalize">{day}</h3>
                            <ul>
                              {schedulesForDay.map((schedule, index) => (
                                <li key={index} className="flex items-center justify-between py-2 border-b">
                                  <span>{schedule.timeStart} - {schedule.timeEnd}</span>
                                  <Button
                                    variant="outline"
                                    size="icon"
                                    onClick={() => removeScheduleItem(selectedTeacher, index)}
                                  >
                                    <Trash2 className="h-4 w-4" />
                                  </Button>
                                </li>
                              ))}
                            </ul>
                          </div>
                        );
                      })}
                    </CardContent>
                  </Card>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default TeacherScheduling;
