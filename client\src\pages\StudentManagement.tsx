
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { fetchStudents } from "@/services/studentService";
import { StudentFilter } from "@/types/student";
import MainLayout from "@/components/layout/MainLayout";
import StudentTable from "@/components/students/StudentTable";
import StudentFilters from "@/components/students/StudentFilters";
import StudentBulkActions from "@/components/students/StudentBulkActions";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";

export default function StudentManagement() {
  const navigate = useNavigate();
  const [filters, setFilters] = useState<Partial<StudentFilter>>({
    page: 1,
    limit: 10,
    sortBy: "name", // Changed from "lastName" to match the type
    sortOrder: "asc",
  });
  const [selectedStudentIds, setSelectedStudentIds] = useState<string[]>([]);

  const { data, isLoading, isError, refetch } = useQuery({
    queryKey: ["students", filters],
    queryFn: () => fetchStudents(filters),
  });

  const students = data?.data || [];
  const pagination = data?.pagination || {
    total: 0,
    page: 1,
    limit: 10,
    pages: 1,
  };

  const handlePageChange = (page: number) => {
    setFilters({ ...filters, page });
  };

  const handleFilterChange = (newFilters: Partial<StudentFilter>) => {
    setFilters({ ...filters, ...newFilters, page: 1 });
  };

  const handleResetFilters = () => {
    setFilters({
      page: 1,
      limit: 10,
      sortBy: "name", // Changed from "lastName" to match the type
      sortOrder: "asc",
    });
  };

  const handleCreateStudent = () => {
    navigate("/students/create");
  };

  const handleSelectedIdsChange = (ids: string[]) => {
    setSelectedStudentIds(ids);
  };

  const handleBulkActionComplete = () => {
    setSelectedStudentIds([]);
    refetch();
  };

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex flex-col md:flex-row justify-between md:items-center space-y-2 md:space-y-0">
          <div>
            <h1 className="text-2xl font-bold tracking-tight">Student Management</h1>
            <p className="text-muted-foreground">
              Manage and view all student records
            </p>
          </div>
          <div className="flex flex-wrap gap-2">
            <Button onClick={handleCreateStudent}>
              <Plus className="mr-2 h-4 w-4" />
              Add Student
            </Button>
          </div>
        </div>

        <StudentFilters
          filters={filters}
          onFilterChange={handleFilterChange}
        />

        <StudentBulkActions 
          selectedCount={selectedStudentIds.length}
          selectedIds={selectedStudentIds}
          onCancel={() => setSelectedStudentIds([])}
          onComplete={handleBulkActionComplete}
        />

        <StudentTable
          students={students}
          isLoading={isLoading}
          isError={isError}
          pagination={pagination}
          onPageChange={handlePageChange}
          selectedIds={selectedStudentIds}
          onSelectedIdsChange={handleSelectedIdsChange}
          onRefresh={refetch}
        />
      </div>
    </MainLayout>
  );
}
