
import { toast } from "sonner";

// Define types for system operations
export interface SystemStats {
  systemHealth: "healthy" | "warning" | "critical";
  uptime: string;
  lastBackup: string;
  errorRate: number;
  averageResponseTime: number;
  systemLoad: {
    cpu: number;
    memory: number;
    disk: number;
    network: number;
  };
  activeUsers: number;
  recentErrors: number;
  pendingUpdates: number;
}

export interface SystemLog {
  id: string;
  timestamp: string;
  severity: "info" | "warning" | "error";
  category: string;
  action: string;
  performedBy: string;
  details: string;
  status: "resolved" | "unresolved";
}

export interface LogFilterParams {
  severity?: string;
  category?: string;
  startDate?: string;
  endDate?: string;
  search?: string;
  page?: number;
  pageSize?: number;
}

export interface LogsResponse {
  logs: SystemLog[];
  pagination: {
    page: number;
    pageSize: number;
    totalLogs: number;
    totalPages: number;
  };
}

export interface BackupInfo {
  id: string;
  timestamp: string;
  size: string;
  type: "automatic" | "manual";
  status: "completed" | "failed" | "in_progress";
  notes?: string;
}

// Mock system settings data
const mockSystemSettings = {
  smtp: {
    server: "smtp.vertex.edu",
    port: 587,
    username: "<EMAIL>",
    password: "**********",
    sslEnabled: true,
    fromName: "Vertex Academy",
    fromEmail: "<EMAIL>"
  },
  backup: {
    enabled: true,
    schedule: "0 3 * * *", // cron syntax: 3 AM daily
    retention: 30, // days
    storageLocation: "/var/backups/vertex",
    includeMedia: true,
    compression: "gzip",
    encrypted: true
  },
  security: {
    passwordPolicy: {
      minLength: 8,
      requireUppercase: true,
      requireLowercase: true,
      requireNumbers: true,
      requireSpecial: true,
      expiryDays: 90,
      preventReuse: true,
      previousPasswordsCount: 5
    },
    session: {
      jwtExpiryHours: 24,
      refreshTokenDays: 7,
      inactivityTimeoutMinutes: 30,
      singleSessionPerUser: false,
      forceLogoutOnPasswordChange: true
    },
    loginSecurity: {
      maxAttempts: 5,
      lockoutMinutes: 15,
      captchaThreshold: 3,
      twoFactorAuth: {
        enabled: false,
        required: false,
        requiredForAdmins: true,
        methods: ["app", "email"]
      }
    }
  },
  cors: {
    enabled: true,
    allowedOrigins: ["https://vertex.edu", "https://admin.vertex.edu"],
    allowCredentials: true,
    maxAge: 86400,
    allowedMethods: ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"]
  },
  notifications: {
    emailEnabled: true,
    smsEnabled: false,
    adminAlerts: true,
    userNotifications: true,
    errorNotifications: true,
    loginNotifications: false
  },
  network: {
    apiRateLimiting: {
      enabled: true,
      requestsPerMinute: "60",
      burstLimit: "100"
    },
    timeouts: {
      connectionTimeout: "30",
      readTimeout: "60",
      writeTimeout: "60"
    },
    ipRestriction: {
      enabled: false,
      whitelistOnly: true,
      failedLoginLockout: true,
      lockoutDuration: "15",
      allowedIps: ["***********/24", "10.0.0.0/16"]
    }
  },
  maintenance: {
    maintenanceMode: false,
    scheduledMaintenance: false,
    maintenanceMessage: "The system is currently undergoing scheduled maintenance and will be back shortly.",
    allowedIpsDuringMaintenance: ["127.0.0.1"]
  }
};

// Mock backup history
const mockBackupHistory: BackupInfo[] = [
  {
    id: "backup-20230815-030000",
    timestamp: "2023-08-15T03:00:00Z",
    size: "285.4 MB",
    type: "automatic",
    status: "completed"
  },
  {
    id: "backup-20230814-030000",
    timestamp: "2023-08-14T03:00:00Z",
    size: "283.7 MB",
    type: "automatic",
    status: "completed"
  },
  {
    id: "backup-20230813-151230",
    timestamp: "2023-08-13T15:12:30Z",
    size: "286.2 MB",
    type: "manual",
    status: "completed",
    notes: "Pre-update backup"
  },
  {
    id: "backup-20230813-030000",
    timestamp: "2023-08-13T03:00:00Z",
    size: "282.9 MB",
    type: "automatic",
    status: "completed"
  },
  {
    id: "backup-20230812-030000",
    timestamp: "2023-08-12T03:00:00Z",
    size: "280.1 MB",
    type: "automatic",
    status: "failed",
    notes: "Disk space issue"
  }
];

// Add audit logging function
export const logConfigChange = (component: string, changes: any, user?: string) => {
  console.log(`CONFIG CHANGE [${component}] by ${user || 'unknown user'}:`, changes);
  // In a real implementation, this would send to the server
};

// Mock implementation of system API functions
export async function getSystemStats(): Promise<SystemStats> {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 800));
  
  // In a real app, this would be an API call
  return {
    systemHealth: "healthy",
    uptime: "15 days, 7 hours, 23 minutes",
    lastBackup: "2023-08-15T03:00:00Z",
    errorRate: 0.05, // 5%
    averageResponseTime: 240, // ms
    systemLoad: {
      cpu: 32, // percent
      memory: 48, // percent
      disk: 63, // percent
      network: 27 // percent
    },
    activeUsers: 18,
    recentErrors: 3,
    pendingUpdates: 2
  };
}

export async function getSystemLogs(params: LogFilterParams): Promise<LogsResponse> {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 600));
  
  // Generate mock logs
  const mockLogs: SystemLog[] = [];
  const categories = ["auth", "student", "class", "payment", "system", "user", "api", "database"];
  const severities: ("info" | "warning" | "error")[] = ["info", "warning", "error"];
  const actions = ["create", "update", "delete", "view", "login", "error", "export", "import", "configure"];
  const users = ["system", "admin", "manager1", "teacher3", "secretary2"];
  
  for (let i = 0; i < 50; i++) {
    const severity = severities[Math.floor(Math.random() * severities.length)];
    const category = categories[Math.floor(Math.random() * categories.length)];
    const action = actions[Math.floor(Math.random() * actions.length)];
    const user = users[Math.floor(Math.random() * users.length)];
    
    // Generate a random date in the last 3 days
    const date = new Date();
    date.setDate(date.getDate() - Math.floor(Math.random() * 3));
    date.setHours(Math.floor(Math.random() * 24), Math.floor(Math.random() * 60), Math.floor(Math.random() * 60));
    
    mockLogs.push({
      id: `log-${i + 1}`,
      timestamp: date.toISOString(),
      severity,
      category,
      action,
      performedBy: user,
      details: `${severity === "error" ? "Failed" : "Successful"} ${action} operation in ${category} module`,
      status: severity === "error" ? (Math.random() > 0.3 ? "unresolved" : "resolved") : "resolved"
    });
  }
  
  // Sort logs by timestamp (newest first)
  mockLogs.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  
  // Apply filters
  let filteredLogs = [...mockLogs];
  
  if (params.severity) {
    filteredLogs = filteredLogs.filter(log => log.severity === params.severity);
  }
  
  if (params.category) {
    filteredLogs = filteredLogs.filter(log => log.category === params.category);
  }
  
  if (params.startDate) {
    const startDate = new Date(params.startDate);
    filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) >= startDate);
  }
  
  if (params.endDate) {
    const endDate = new Date(params.endDate);
    endDate.setHours(23, 59, 59, 999);
    filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) <= endDate);
  }
  
  if (params.search) {
    const searchLower = params.search.toLowerCase();
    filteredLogs = filteredLogs.filter(log => 
      log.details.toLowerCase().includes(searchLower) ||
      log.action.toLowerCase().includes(searchLower) ||
      log.performedBy.toLowerCase().includes(searchLower) ||
      log.category.toLowerCase().includes(searchLower)
    );
  }
  
  // Pagination
  const page = params.page || 1;
  const pageSize = params.pageSize || 10;
  const totalLogs = filteredLogs.length;
  const totalPages = Math.ceil(totalLogs / pageSize);
  const paginatedLogs = filteredLogs.slice((page - 1) * pageSize, page * pageSize);
  
  return {
    logs: paginatedLogs,
    pagination: {
      page,
      pageSize,
      totalLogs,
      totalPages
    }
  };
}

export async function getSystemSettings() {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // In a real app, this would be an API call
  return mockSystemSettings;
}

export async function updateSystemSettings(settings: any) {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Log the update for audit purposes
  logConfigChange("systemSettings", settings);
  
  // In a real app, this would send the settings to the backend
  console.log("Updating system settings:", settings);
  
  // Simulate success/failure
  if (Math.random() > 0.1) {
    return { success: true, message: "Settings updated successfully" };
  } else {
    throw new Error("Failed to update settings");
  }
}

export async function getBackupHistory(): Promise<BackupInfo[]> {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 700));
  
  // In a real app, this would be an API call
  return mockBackupHistory;
}

export async function triggerBackup(): Promise<BackupInfo> {
  // Simulate network delay (longer for backup operation)
  await new Promise(resolve => setTimeout(resolve, 3000));
  
  // Generate a new backup entry
  const newBackup: BackupInfo = {
    id: `backup-${new Date().toISOString().replace(/[:.]/g, "")}`,
    timestamp: new Date().toISOString(),
    size: `${280 + Math.floor(Math.random() * 10)}.${Math.floor(Math.random() * 10)} MB`,
    type: "manual",
    status: "completed"
  };
  
  // Log the operation for audit
  logConfigChange("backup", { action: "manual_backup", backupId: newBackup.id });
  
  // In a real app, this would be an API call to trigger a backup
  // and the response would contain the actual backup info
  
  return newBackup;
}

export async function restoreBackup(backupId: string): Promise<{ success: boolean; message: string }> {
  // Simulate network delay (longer for restore operation)
  await new Promise(resolve => setTimeout(resolve, 5000));
  
  // Log the operation for audit
  logConfigChange("backup", { action: "restore_backup", backupId });
  
  // In a real app, this would be an API call to restore from a backup
  console.log(`Restoring from backup: ${backupId}`);
  
  // Simulate success
  return {
    success: true,
    message: "System successfully restored from backup"
  };
}

export async function optimizeDatabase(): Promise<{ success: boolean; message: string }> {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 4000));
  
  // Log the operation for audit
  logConfigChange("maintenance", { action: "optimize_database" });
  
  // In a real app, this would be an API call to optimize the database
  
  // Simulate success
  return {
    success: true,
    message: "Database optimization completed successfully"
  };
}

export async function clearSystemCache(): Promise<{ success: boolean; message: string }> {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 1500));
  
  // Log the operation for audit
  logConfigChange("maintenance", { action: "clear_cache" });
  
  // In a real app, this would be an API call to clear the system cache
  
  // Simulate success
  return {
    success: true,
    message: "System cache cleared successfully"
  };
}

export async function toggleMaintenanceMode(enabled: boolean): Promise<{ success: boolean; message: string }> {
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // Log the operation for audit
  logConfigChange("maintenance", { action: "toggle_maintenance_mode", enabled });
  
  // In a real app, this would be an API call to toggle maintenance mode
  console.log(`Setting maintenance mode to: ${enabled}`);
  
  // Simulate success
  return {
    success: true,
    message: enabled 
      ? "Maintenance mode activated successfully" 
      : "Maintenance mode deactivated successfully"
  };
}
