
import { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { fetchStudent, getSuggestedClasses, transferStudent } from "@/services/studentService";
import MainLayout from "@/components/layout/MainLayout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Form, 
  FormControl, 
  FormDescription, 
  FormField, 
  FormItem, 
  FormLabel, 
  FormMessage 
} from "@/components/ui/form";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";
import { toast } from "sonner";
import { Calendar as CalendarIcon, ChevronLeft, Users } from "lucide-react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { cn } from "@/lib/utils";

// Define form schema
const formSchema = z.object({
  toClassId: z.string({
    required_error: "Please select a class",
  }),
  reason: z.string({
    required_error: "Please provide a reason for transfer",
  }).min(5, {
    message: "Reason must be at least 5 characters",
  }),
  transferDate: z.date({
    required_error: "Please select a transfer date",
  }),
});

type FormValues = z.infer<typeof formSchema>;

export default function StudentTransfer() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  // Fetch student details
  const { data: studentData, isLoading: isLoadingStudent } = useQuery({
    queryKey: ['student', id],
    queryFn: () => fetchStudent(id!),
    enabled: !!id
  });
  
  // Fetch suggested classes
  const { data: suggestedClasses, isLoading: isLoadingClasses } = useQuery({
    queryKey: ['suggestedClasses', id],
    queryFn: () => getSuggestedClasses(id!),
    enabled: !!id
  });
  
  const student = studentData?.data;
  
  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      reason: "",
      transferDate: new Date(),
    },
  });
  
  const onSubmit = async (data: FormValues) => {
    if (!id) return;
    
    setIsSubmitting(true);
    try {
      const result = await transferStudent(
        id,
        data.toClassId,
        data.reason,
        data.transferDate.toISOString()
      );
      
      if (result.success) {
        toast.success("Student transferred successfully");
        navigate(`/students/${id}`);
      } else {
        toast.error("Failed to transfer student");
      }
    } catch (error) {
      toast.error("An error occurred while transferring student");
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  if (isLoadingStudent) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
        </div>
      </MainLayout>
    );
  }
  
  if (!student) {
    return (
      <MainLayout>
        <div className="flex flex-col items-center justify-center min-h-[60vh]">
          <p className="text-xl text-muted-foreground mb-4">
            Could not load student details
          </p>
          <Button onClick={() => navigate('/students')}>Back to Students</Button>
        </div>
      </MainLayout>
    );
  }
  
  return (
    <MainLayout>
      <div className="space-y-6 max-w-3xl mx-auto">
        <div className="flex items-center gap-2">
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={() => navigate(`/students/${id}`)}
          >
            <ChevronLeft size={18} />
          </Button>
          <h1 className="text-2xl font-bold">Transfer Student</h1>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>Transfer {student.firstName} {student.lastName}</CardTitle>
            <CardDescription>
              Transfer this student to a different class
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <div className="flex items-center p-4 bg-muted/30 rounded-lg gap-3 text-sm">
                  <Users size={18} className="text-muted-foreground" />
                  <span>
                    <strong>Current Class:</strong>{" "}
                    {student.currentClass 
                      ? student.currentClass.name
                      : "Not currently assigned to any class"}
                  </span>
                </div>
                
                <FormField
                  control={form.control}
                  name="toClassId"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>New Class</FormLabel>
                      <Select 
                        onValueChange={field.onChange} 
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger>
                            <SelectValue placeholder="Select a class" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {isLoadingClasses ? (
                            <div className="p-2 text-center">Loading classes...</div>
                          ) : (
                            suggestedClasses?.map(c => (
                              <SelectItem 
                                key={c.id} 
                                value={c.id}
                                disabled={c.id === student.currentClass?.id}
                              >
                                {c.name}
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                      <FormDescription>
                        Select the class you want to transfer the student to
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="transferDate"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Transfer Date</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) =>
                              date < new Date("1900-01-01") || date > new Date()
                            }
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormDescription>
                        The date when the transfer becomes effective
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="reason"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Reason for Transfer</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Enter reason for transfer..."
                          className="min-h-[100px]"
                          {...field}
                        />
                      </FormControl>
                      <FormDescription>
                        Provide a clear reason for this class transfer
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <div className="flex justify-end gap-3 pt-4">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => navigate(`/students/${id}`)}
                  >
                    Cancel
                  </Button>
                  <Button 
                    type="submit"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? "Processing..." : "Transfer Student"}
                  </Button>
                </div>
              </form>
            </Form>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}
