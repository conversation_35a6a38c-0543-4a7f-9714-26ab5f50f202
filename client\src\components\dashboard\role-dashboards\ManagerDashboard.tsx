
import InteractiveChart from "../InteractiveChart";
import DashboardSection from "../DashboardSection";
import MetricCard from "../MetricCard";
import QuickActionButton from "../QuickActionButton";
import { 
  BookOpen, 
  Users, 
  CheckCircle, 
  Home, 
  PlusCircle, 
  UserPlus, 
  CheckSquare, 
  DollarSign,
} from "lucide-react";
import { DashboardData } from "@/services/dashboardService";

interface ManagerDashboardProps {
  dashboardData: Partial<DashboardData>;
  isLoadingData: boolean;
}

const ManagerDashboard = ({ dashboardData, isLoadingData }: ManagerDashboardProps) => {
  return (
    <div className="space-y-6">
      {/* Metric Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard 
          title="Classes" 
          value={dashboardData?.classStats?.totalClasses || 0}
          icon={BookOpen}
          trend={{ value: 3.2, isPositive: true }}
          isLoading={isLoadingData}
        />
        <MetricCard 
          title="Students" 
          value={dashboardData?.studentStats?.totalStudents || 0}
          icon={Users}
          trend={{ value: 4.7, isPositive: true }}
          isLoading={isLoadingData}
        />
        <MetricCard 
          title="Attendance Rate" 
          value={dashboardData?.attendanceStats?.overallAttendanceRate || 0}
          suffix="%"
          icon={CheckCircle}
          trend={{ value: 1.2, isPositive: true }}
          isLoading={isLoadingData}
        />
        <MetricCard 
          title="Room Usage" 
          value={dashboardData?.roomUtilization?.roomUtilizationRate || 0}
          suffix="%"
          icon={Home}
          trend={{ value: 2.5, isPositive: true }}
          isLoading={isLoadingData}
        />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <InteractiveChart
          title="Class Distribution"
          description="Classes by level"
          data={dashboardData?.classStats?.classesByLevel || []}
          xKey="level"
          yKeys={[{ key: "count", name: "Classes", color: "#4f46e5" }]}
          type="bar"
          isLoading={isLoadingData}
          actions={true}
        />
        <InteractiveChart
          title="Student Enrollment"
          description="Enrollment over time"
          data={dashboardData?.classStats?.enrollmentTrend || []}
          xKey="date"
          yKeys={[{ key: "count", name: "Students", color: "#10b981" }]}
          type="area"
          isLoading={isLoadingData}
          actions={true}
        />
      </div>

      {/* Additional Insights */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
        <InteractiveChart
          title="Attendance by Class"
          description="Class attendance rates"
          data={dashboardData?.attendanceStats?.attendanceByClass?.map(item => ({
            className: item.className,
            attendanceRate: item.attendanceRate
          })) || []}
          xKey="className"
          yKeys={[{ key: "attendanceRate", name: "Attendance Rate", color: "#f59e0b" }]}
          type="bar"
          isLoading={isLoadingData}
          actions={true}
        />
        <DashboardSection
          title="Quick Actions"
          description="Common tasks"
          isLoading={isLoadingData}
          className="lg:col-span-2"
        >
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <QuickActionButton
              label="Create Class"
              icon={PlusCircle}
              href="/classes/create"
            />
            <QuickActionButton
              label="Add Student"
              icon={UserPlus}
              href="/students/create"
            />
            <QuickActionButton
              label="Mark Attendance"
              icon={CheckSquare}
              href="/attendance"
            />
            <QuickActionButton
              label="Payment Records"
              icon={DollarSign}
              href="/payments"
            />
          </div>
        </DashboardSection>
      </div>
    </div>
  );
};

export default ManagerDashboard;
