
// We only need to modify the rooms transformation part of the file
import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { School, Users, CheckSquare, ArrowLeft } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import ClassFormBasicInfo, { BasicInfoFormValues } from "./ClassFormBasicInfo";
import ClassFormTeachers, { TeachersFormValues } from "./ClassFormTeachers";
import ClassFormReview from "./ClassFormReview";
import { createClass, updateClass, fetchClassById } from "@/services/classService";
import { fetchTeachers } from "@/services/teacherService";
import { findAvailableRooms } from "@/services/roomService";
import { ClassFormData, TeacherForm<PERSON><PERSON>, Class, Teacher } from "@/types/class";
import { Room } from "@/types/room";

type FormStep = "basicInfo" | "teachers" | "review";

type ClassCreateFormProps = {
  isEditMode?: boolean;
};

const ClassCreateForm = ({ isEditMode = false }: ClassCreateFormProps) => {
  const navigate = useNavigate();
  const { id: classId } = useParams<{ id: string }>();
  const [currentStep, setCurrentStep] = useState<FormStep>("basicInfo");
  const [formData, setFormData] = useState<ClassFormData>({
    name: "",
    level: "",
    room: "",
    capacity: 20,
    startDate: undefined,
    endDate: undefined,
    teachers: [],
    status: "active",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [originalData, setOriginalData] = useState<Class | null>(null);

  // Fetch class data if in edit mode
  const {
    data: classData,
    isLoading: isLoadingClass,
    error: classError,
  } = useQuery({
    queryKey: ["class", classId],
    queryFn: () => fetchClassById(classId || ""),
    enabled: isEditMode && !!classId,
  });

  // Fetch teachers for selection
  const {
    data: teachers,
    isLoading: isLoadingTeachers,
    error: teachersError,
  } = useQuery({
    queryKey: ["teachers"],
    queryFn: fetchTeachers,
  });

  // Fetch available rooms
  const {
    data: roomsData,
    isLoading: isLoadingRooms,
    error: roomsError,
  } = useQuery({
    queryKey: ["rooms"],
    queryFn: () => findAvailableRooms("", "", ""),
  });

  // Process room data to ensure it's an array of Room objects
  const rooms: Room[] = (roomsData && Array.isArray(roomsData)) 
    ? roomsData.map(room => ({
        id: room.id || '',
        name: room.name || '',
        capacity: room.capacity || 0,
        building: room.building || '',
        floor: room.floor || '',
        type: room.type || 'classroom',
        amenities: room.amenities || [],
        status: room.status || 'active',
        features: room.features || []
      }))
    : [];

  useEffect(() => {
    if (isEditMode && classData?.data && !originalData) {
      const classInfo = classData.data;
      setOriginalData(classInfo);
      
      // Transform class data to form data structure
      const formattedData: ClassFormData = {
        name: classInfo.name || "",
        level: classInfo.level || "",
        room: classInfo.room || "",
        capacity: classInfo.capacity?.total || 20,
        startDate: classInfo.schedule?.startDate ? new Date(classInfo.schedule.startDate) : undefined,
        endDate: classInfo.schedule?.endDate ? new Date(classInfo.schedule.endDate) : undefined,
        teachers: classInfo.teachers?.map(teacher => ({
          teacherId: teacher.id,
          schedule: teacher.schedule || []
        })) || [],
        status: classInfo.status || "active"
      };
      
      setFormData(formattedData);
    }
  }, [classData, isEditMode, originalData]);

  const handleBasicInfoSubmit = (data: BasicInfoFormValues) => {
    setFormData(prev => ({
      ...prev,
      name: data.name,
      level: data.level,
      room: data.room,
      capacity: data.capacity,
      startDate: data.startDate,
      endDate: data.endDate,
      status: data.status
    }));
    setCurrentStep("teachers");
  };

  const handleTeachersSubmit = (data: TeachersFormValues) => {
    // Fix the type mismatch issue by ensuring each item in the schedule array has required fields
    const updatedTeachers: TeacherFormData[] = data.teachers.map(teacher => ({
      teacherId: teacher.teacherId,
      // Ensure each schedule item has the required fields
      schedule: teacher.schedule?.map(item => ({
        day: item.day || "", // Ensure day is not optional
        timeStart: item.timeStart || "",
        timeEnd: item.timeEnd || ""
      })) || []
    }));
    
    setFormData(prev => ({
      ...prev,
      teachers: updatedTeachers
    }));
    setCurrentStep("review");
  };

  const handleGoBack = () => {
    if (currentStep === "teachers") {
      setCurrentStep("basicInfo");
    } else if (currentStep === "review") {
      setCurrentStep("teachers");
    }
  };

  const handleSubmit = async () => {
    try {
      setIsSubmitting(true);
      
      // Format data for API
      const apiData = {
        name: formData.name,
        level: formData.level,
        room: formData.room,
        capacity: {
          total: formData.capacity,
          current: originalData?.capacity?.current || 0,
          available: formData.capacity - (originalData?.capacity?.current || 0)
        },
        teachers: formData.teachers.map(teacher => ({
          id: teacher.teacherId,
          name: teachers?.find(t => t.id === teacher.teacherId)?.name || "Unknown Teacher",
          schedule: teacher.schedule
        })),
        schedule: {
          startDate: formData.startDate?.toISOString().split('T')[0],
          endDate: formData.endDate?.toISOString().split('T')[0]
        },
        status: formData.status
      };
      
      let result;
      if (isEditMode && classId) {
        result = await updateClass(classId, apiData);
        toast.success("Class updated successfully");
      } else {
        result = await createClass(apiData);
        toast.success("Class created successfully");
      }
      
      // Navigate to class details page
      navigate(`/classes/${result.data.id}`);
    } catch (error) {
      console.error("Error submitting class:", error);
      toast.error(error instanceof Error ? error.message : "Failed to save class");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Show loading state while fetching data in edit mode
  if (isEditMode && isLoadingClass) {
    return <div className="flex justify-center p-8">Loading class data...</div>;
  }

  // Show error state if data fetch failed
  if (isEditMode && classError) {
    return (
      <div className="flex flex-col items-center p-8">
        <p className="text-red-500 mb-4">Error loading class data</p>
        <Button onClick={() => navigate('/classes')}>Back to Classes</Button>
      </div>
    );
  }

  // The components need to be updated to accept the props correctly
  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center mb-6">
        <Button
          variant="ghost"
          size="sm"
          className="mr-2"
          onClick={() => navigate('/classes')}
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back
        </Button>
        <h1 className="text-2xl font-bold">
          {isEditMode ? "Edit Class" : "Create New Class"}
        </h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>{isEditMode ? "Edit Class" : "Create New Class"}</CardTitle>
          <CardDescription>
            {isEditMode 
              ? "Update the class information below" 
              : "Fill in the details to create a new class"}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex mb-8">
            <div className="flex items-center">
              <div className={`rounded-full p-2 ${currentStep === "basicInfo" ? "bg-primary text-primary-foreground" : "bg-muted"}`}>
                <School className="h-5 w-5" />
              </div>
              <span className={`ml-2 ${currentStep === "basicInfo" ? "font-medium" : ""}`}>Basic Info</span>
            </div>
            <div className="w-12 h-px bg-border mx-2 self-center" />
            <div className="flex items-center">
              <div className={`rounded-full p-2 ${currentStep === "teachers" ? "bg-primary text-primary-foreground" : "bg-muted"}`}>
                <Users className="h-5 w-5" />
              </div>
              <span className={`ml-2 ${currentStep === "teachers" ? "font-medium" : ""}`}>Teachers</span>
            </div>
            <div className="w-12 h-px bg-border mx-2 self-center" />
            <div className="flex items-center">
              <div className={`rounded-full p-2 ${currentStep === "review" ? "bg-primary text-primary-foreground" : "bg-muted"}`}>
                <CheckSquare className="h-5 w-5" />
              </div>
              <span className={`ml-2 ${currentStep === "review" ? "font-medium" : ""}`}>Review</span>
            </div>
          </div>

          {currentStep === "basicInfo" && (
            <ClassFormBasicInfo 
              onSubmit={handleBasicInfoSubmit} 
              defaultValues={formData}
              isLoading={isLoadingRooms}
              rooms={rooms}
            />
          )}

          {currentStep === "teachers" && (
            <ClassFormTeachers 
              onSubmit={handleTeachersSubmit}
              onBack={handleGoBack}
              defaultValues={{ teachers: formData.teachers }}
              availableTeachers={teachers || []}
              isLoading={isLoadingTeachers}
            />
          )}

          {currentStep === "review" && (
            <ClassFormReview 
              formData={formData}
              onSubmit={handleSubmit}
              onBack={handleGoBack}
              isLoading={isSubmitting}
              availableTeachers={teachers || []}
            />
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ClassCreateForm;
