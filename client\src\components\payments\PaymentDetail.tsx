import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { fetchPayment, voidPayment, generatePaymentReceipt } from "@/services/paymentService";
import { toast } from "sonner";
import { format } from "date-fns";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";

import {
  FileText,
  Banknote,
  Calendar,
  User,
  Clock,
  AlertTriangle,
  Download,
  Printer,
  Mail,
  Ban,
  Receipt,
  CreditCard,
  ExternalLink,
  ClipboardCopy,
} from "lucide-react";
import { cn } from "@/lib/utils";

interface PaymentDetailProps {
  paymentId: string;
  onPaymentUpdated?: () => void;
}

const PaymentDetail = ({ paymentId, onPaymentUpdated }: PaymentDetailProps) => {
  const navigate = useNavigate();
  const [isGeneratingReceipt, setIsGeneratingReceipt] = useState(false);
  const [isVoiding, setIsVoiding] = useState(false);
  const [voidReason, setVoidReason] = useState("");
  const [showVoidDialog, setShowVoidDialog] = useState(false);

  const { data, isLoading, isError, refetch } = useQuery({
    queryKey: ["payment", paymentId],
    queryFn: () => fetchPayment(paymentId),
  });

  const payment = data?.data;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "PPP");
  };

  const formatTime = (dateString: string) => {
    return format(new Date(dateString), "p");
  };

  const copyReceiptNumber = () => {
    if (payment?.receiptNumber) {
      navigator.clipboard.writeText(payment.receiptNumber);
      toast.success("Receipt number copied to clipboard");
    }
  };

  const handleGenerateReceipt = async () => {
    if (!payment) return;

    setIsGeneratingReceipt(true);
    try {
      const response = await generatePaymentReceipt(payment.id);
      if (response.success) {
        toast.success("Receipt generated successfully");
        if (onPaymentUpdated) {
          onPaymentUpdated();
        }
      } else {
        toast.error("Failed to generate receipt");
      }
    } catch (error) {
      console.error("Error generating receipt:", error);
      toast.error("An error occurred while generating receipt");
    } finally {
      setIsGeneratingReceipt(false);
    }
  };

  const handleVoidPayment = async () => {
    if (!payment || !voidReason.trim()) return;

    setIsVoiding(true);
    try {
      const response = await voidPayment(payment.id, voidReason);
      if (response.success) {
        toast.success("Payment has been voided");
        setShowVoidDialog(false);
        refetch();
        if (onPaymentUpdated) {
          onPaymentUpdated();
        }
      } else {
        toast.error("Failed to void payment");
      }
    } catch (error) {
      console.error("Error voiding payment:", error);
      toast.error("An error occurred while voiding payment");
    } finally {
      setIsVoiding(false);
    }
  };

  if (isLoading) {
    return (
      <div className="h-96 flex items-center justify-center">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  if (isError || !payment) {
    return (
      <div className="flex flex-col items-center justify-center h-96">
        <AlertTriangle className="h-12 w-12 text-destructive mb-4" />
        <h2 className="text-xl font-bold mb-2">Payment Not Found</h2>
        <p className="text-muted-foreground mb-4">
          The payment record you're looking for could not be found
        </p>
        <Button onClick={() => navigate("/payments")}>
          Back to Payments
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Payment header with status */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div className="space-y-1">
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Receipt className="h-6 w-6" />
            Payment Receipt
            <Badge
              variant="outline"
              className={cn(
                "ml-2 capitalize",
                payment.status === "completed" &&
                  "border-green-200 bg-green-50 text-green-700 dark:bg-green-900/30 dark:text-green-400",
                payment.status === "pending" &&
                  "border-yellow-200 bg-yellow-50 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400",
                payment.status === "voided" &&
                  "border-red-200 bg-red-50 text-red-700 dark:bg-red-900/30 dark:text-red-400"
              )}
            >
              {payment.status}
            </Badge>
          </h2>
          <div className="flex items-center text-muted-foreground">
            <Clock className="h-4 w-4 mr-1" />
            {formatDate(payment.date)} at {formatTime(payment.recordedAt)}
          </div>
        </div>
        <div className="flex flex-wrap gap-2">
          {payment.status !== "voided" && (
            <>
              <Button 
                variant="outline" 
                onClick={handleGenerateReceipt}
                disabled={isGeneratingReceipt}
              >
                <FileText className="mr-2 h-4 w-4" />
                {isGeneratingReceipt ? "Generating..." : "Receipt"}
              </Button>
              <Dialog open={showVoidDialog} onOpenChange={setShowVoidDialog}>
                <DialogTrigger asChild>
                  <Button variant="outline">
                    <Ban className="mr-2 h-4 w-4" />
                    Void Payment
                  </Button>
                </DialogTrigger>
                <DialogContent>
                  <DialogHeader>
                    <DialogTitle>Void Payment</DialogTitle>
                    <DialogDescription>
                      This action will void the payment. Voided payments cannot be restored.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="space-y-4 py-4">
                    <div className="space-y-2">
                      <Label htmlFor="void-reason">Reason for voiding</Label>
                      <Textarea
                        id="void-reason"
                        placeholder="Please specify why this payment is being voided"
                        value={voidReason}
                        onChange={(e) => setVoidReason(e.target.value)}
                      />
                    </div>
                  </div>
                  <DialogFooter>
                    <Button 
                      variant="outline" 
                      onClick={() => setShowVoidDialog(false)}
                    >
                      Cancel
                    </Button>
                    <Button 
                      variant="destructive" 
                      onClick={handleVoidPayment}
                      disabled={isVoiding || !voidReason.trim()}
                    >
                      {isVoiding ? "Voiding..." : "Void Payment"}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </>
          )}
          <Button 
            variant="outline" 
            onClick={() => navigate(`/students/${payment.studentId}`)}
          >
            <User className="mr-2 h-4 w-4" />
            View Student
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Payment details card */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Payment Details</CardTitle>
            <CardDescription>
              Complete information about this payment
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
              {/* Receipt Number */}
              <div className="space-y-1">
                <label className="text-sm text-muted-foreground">Receipt Number</label>
                <div className="flex items-center gap-2">
                  <span className="font-medium">{payment.receiptNumber || "-"}</span>
                  {payment.receiptNumber && (
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={copyReceiptNumber}
                      className="h-6 w-6 p-0"
                    >
                      <ClipboardCopy className="h-3.5 w-3.5" />
                    </Button>
                  )}
                </div>
              </div>

              {/* Student Information */}
              <div className="space-y-1">
                <label className="text-sm text-muted-foreground">Student</label>
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">
                    {payment.student ? (
                      <Button
                        variant="link"
                        className="p-0 h-auto"
                        onClick={() => navigate(`/students/${payment.studentId}`)}
                      >
                        {payment.student.firstName} {payment.student.lastName}
                        <ExternalLink className="ml-1 h-3 w-3" />
                      </Button>
                    ) : (
                      `Student ID: ${payment.studentId}`
                    )}
                  </span>
                </div>
              </div>

              {/* Amount */}
              <div className="space-y-1">
                <label className="text-sm text-muted-foreground">Amount</label>
                <div className="flex items-center gap-2">
                  <Banknote className="h-4 w-4 text-muted-foreground" />
                  <span className="font-bold text-xl text-green-600">
                    {formatCurrency(payment.amount)}
                  </span>
                </div>
              </div>

              {/* Payment Method */}
              <div className="space-y-1">
                <label className="text-sm text-muted-foreground">Payment Method</label>
                <div className="flex items-center gap-2">
                  <CreditCard className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium capitalize">{payment.method.replace("_", " ")}</span>
                </div>
              </div>

              {/* Payment Date */}
              <div className="space-y-1">
                <label className="text-sm text-muted-foreground">Payment Date</label>
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{formatDate(payment.date)}</span>
                </div>
              </div>

              {/* Next Due Date */}
              {payment.nextDueDate && (
                <div className="space-y-1">
                  <label className="text-sm text-muted-foreground">Next Due Date</label>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">{formatDate(payment.nextDueDate)}</span>
                  </div>
                </div>
              )}
            </div>

            <Separator />

            {/* Description & Notes */}
            <div className="space-y-4">
              <div className="space-y-1">
                <label className="text-sm text-muted-foreground">Description</label>
                <p>{payment.description}</p>
              </div>

              {payment.notes && (
                <div className="space-y-1">
                  <label className="text-sm text-muted-foreground">Notes</label>
                  <p className="text-muted-foreground">{payment.notes}</p>
                </div>
              )}
            </div>

            {payment.status === "voided" && (
              <>
                <Separator />
                <div className="p-4 border border-red-200 bg-red-50 dark:bg-red-900/20 dark:border-red-800 rounded-md">
                  <div className="flex items-start gap-3">
                    <AlertTriangle className="h-5 w-5 text-red-500 shrink-0 mt-0.5" />
                    <div>
                      <h4 className="font-medium text-red-700 dark:text-red-400">This payment has been voided</h4>
                      <p className="text-sm text-red-600 dark:text-red-300 mt-1">
                        Reason: {payment.voidReason || "No reason provided"}
                      </p>
                      <p className="text-xs text-red-500 dark:text-red-300 mt-1">
                        Voided by {payment.voidedBy} on {formatDate(payment.voidedAt || "")} at {formatTime(payment.voidedAt || "")}
                      </p>
                    </div>
                  </div>
                </div>
              </>
            )}
          </CardContent>
          <CardFooter>
            <div className="text-sm text-muted-foreground">
              Payment recorded by {payment.recordedBy} on {formatDate(payment.recordedAt)} at {formatTime(payment.recordedAt)}
            </div>
          </CardFooter>
        </Card>

        {/* Actions panel */}
        <Card>
          <CardHeader>
            <CardTitle>Actions</CardTitle>
            <CardDescription>
              Available operations for this payment
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              className="w-full" 
              onClick={handleGenerateReceipt}
              disabled={isGeneratingReceipt || payment.status === "voided"}
            >
              <FileText className="mr-2 h-4 w-4" />
              {isGeneratingReceipt ? "Generating..." : "Generate Receipt"}
            </Button>
            
            <Button 
              variant="outline" 
              className="w-full"
              onClick={() => window.print()}
            >
              <Printer className="mr-2 h-4 w-4" />
              Print View
            </Button>
            
            <Button 
              variant="outline" 
              className="w-full"
              onClick={() => toast.success("Email receipt functionality would be implemented here")}
            >
              <Mail className="mr-2 h-4 w-4" />
              Email Receipt
            </Button>
            
            <Button 
              variant="outline" 
              className="w-full"
              onClick={() => navigate(`/students/${payment.studentId}/payment`)}
            >
              <CreditCard className="mr-2 h-4 w-4" />
              Record New Payment
            </Button>
            
            {payment.status !== "voided" && (
              <Button 
                variant="destructive" 
                className="w-full"
                onClick={() => setShowVoidDialog(true)}
              >
                <Ban className="mr-2 h-4 w-4" />
                Void This Payment
              </Button>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default PaymentDetail;
