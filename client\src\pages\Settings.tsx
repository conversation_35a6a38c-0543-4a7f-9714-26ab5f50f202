
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import MainLayout from "@/components/layout/MainLayout";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import CustomCard from "@/components/ui/CustomCard";
import UserManagement from "@/components/settings/UserManagement";
import SystemLogs from "@/components/settings/SystemLogs";
import SystemConfig from "@/components/settings/SystemConfig";
import SystemDashboard from "@/components/settings/SystemDashboard";
import NetworkConfig from "@/components/settings/NetworkConfig";
import AuthConfig from "@/components/settings/AuthConfig";
import MaintenanceTools from "@/components/settings/MaintenanceTools";
import { getCurrentUser, hasPermission } from "@/lib/auth";
import { useNavigate } from "react-router-dom";
import { Shield } from "lucide-react";

const Settings = () => {
  const [activeTab, setActiveTab] = useState("dashboard");
  const navigate = useNavigate();
  const user = getCurrentUser();

  useEffect(() => {
    // Check if user has permission to access this page
    if (!hasPermission(["SuperAdmin", "Manager"])) {
      toast.error("You don't have permission to access this page");
      navigate("/dashboard");
    }
  }, [navigate]);

  // Additional check for SuperAdmin-only tabs
  const isSuperAdmin = user?.role === "SuperAdmin";

  return (
    <MainLayout>
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2">
          <div>
            <h1 className="text-2xl font-bold">System Settings</h1>
            <p className="text-muted-foreground">
              Manage system settings, users, and view system logs
            </p>
          </div>
          
          {!isSuperAdmin && (
            <div className="flex items-center gap-2 bg-amber-100 text-amber-800 p-2 rounded-md">
              <Shield className="h-5 w-5" />
              <span className="text-sm">Some features require SuperAdmin privileges</span>
            </div>
          )}
        </div>

        <Tabs defaultValue="dashboard" value={activeTab} onValueChange={setActiveTab}>
          <div className="overflow-x-auto">
            <TabsList className="w-full mb-2 grid grid-cols-7 gap-1">
              <TabsTrigger className="px-3 py-2" value="dashboard">Dashboard</TabsTrigger>
              <TabsTrigger className="px-3 py-2" value="users">Users</TabsTrigger>
              <TabsTrigger className="px-3 py-2" value="logs">System Logs</TabsTrigger>
              <TabsTrigger className="px-3 py-2" value="config">Configuration</TabsTrigger>
              <TabsTrigger className="px-3 py-2" value="auth" disabled={!isSuperAdmin}>Authentication</TabsTrigger>
              <TabsTrigger className="px-3 py-2" value="network" disabled={!isSuperAdmin}>Network</TabsTrigger>
              <TabsTrigger className="px-3 py-2" value="maintenance" disabled={!isSuperAdmin}>Maintenance</TabsTrigger>
            </TabsList>
          </div>
          
          <TabsContent value="dashboard" className="space-y-4 mt-6">
            <SystemDashboard />
          </TabsContent>
          
          <TabsContent value="users" className="space-y-4 mt-6">
            <UserManagement />
          </TabsContent>
          
          <TabsContent value="logs" className="space-y-4 mt-6">
            <SystemLogs />
          </TabsContent>
          
          <TabsContent value="config" className="space-y-4 mt-6">
            <SystemConfig />
          </TabsContent>
          
          <TabsContent value="auth" className="space-y-4 mt-6">
            {isSuperAdmin ? (
              <AuthConfig />
            ) : (
              <CustomCard className="p-6">
                <div className="text-center py-8">
                  <Shield className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">SuperAdmin Access Required</h3>
                  <p className="text-muted-foreground">
                    This section is only accessible to SuperAdmin users.
                  </p>
                </div>
              </CustomCard>
            )}
          </TabsContent>
          
          <TabsContent value="network" className="space-y-4 mt-6">
            {isSuperAdmin ? (
              <NetworkConfig />
            ) : (
              <CustomCard className="p-6">
                <div className="text-center py-8">
                  <Shield className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">SuperAdmin Access Required</h3>
                  <p className="text-muted-foreground">
                    This section is only accessible to SuperAdmin users.
                  </p>
                </div>
              </CustomCard>
            )}
          </TabsContent>
          
          <TabsContent value="maintenance" className="space-y-4 mt-6">
            {isSuperAdmin ? (
              <MaintenanceTools />
            ) : (
              <CustomCard className="p-6">
                <div className="text-center py-8">
                  <Shield className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <h3 className="text-lg font-medium mb-2">SuperAdmin Access Required</h3>
                  <p className="text-muted-foreground">
                    This section is only accessible to SuperAdmin users.
                  </p>
                </div>
              </CustomCard>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
};

export default Settings;
