import { Request, Response, NextFunction } from 'express';
import mongoose from 'mongoose';
import { Room, IRoomDocument } from '../models/room.model';
import { Class } from '../models/class.model';
import { AppError } from '../types/error.types';
import { TimeSlot, AvailabilitySlot } from '../types/room.types';

// Extend Express Request
declare module 'express' {
    interface Request {
        room?: IRoomDocument;
    }
}

export class RoomMiddleware {
    // Validate room exists
    static async validateRoomExists(
        req: Request,
        res: Response,
        next: NextFunction
    ): Promise<void> {
        try {
            const roomId = req.params.id;
            if (!mongoose.Types.ObjectId.isValid(roomId)) {
                throw new AppError(400, 'Invalid room ID format');
            }

            const room = await Room.findById(roomId);
            if (!room) {
                throw new AppError(404, 'Room not found');
            }

            // Attach room to request for later use
            req.room = room;
            next();
        } catch (error) {
            next(error instanceof Error ? error : new Error('Unknown error'));
        }
    }

    // Validate room name uniqueness
    static async validateRoomName(
        req: Request,
        res: Response,
        next: NextFunction
    ): Promise<void> {
        try {
            const { name } = req.body;
            const roomId = req.params.id; // For updates

            const existingRoom = await Room.findOne({ 
                name,
                _id: { $ne: roomId } // Exclude current room for updates
            });

            if (existingRoom) {
                throw new AppError(400, 'Room with this name already exists');
            }

            next();
        } catch (error) {
            next(error instanceof Error ? error : new Error('Unknown error'));
        }
    }

    // Validate capacity changes
    static async validateCapacityChange(
        req: Request,
        res: Response,
        next: NextFunction
    ): Promise<void> {
        try {
            const room = req.room;
            const { capacity } = req.body;

            if (!capacity || !room) {
                return next();
            }

            if (capacity < room.capacity) {
                // Check if there are any scheduled classes
                const hasScheduledClasses = room.availability.some((day: AvailabilitySlot) => 
                    day.timeSlots.length > 0
                );

                if (hasScheduledClasses) {
                    throw new AppError(400, 'Cannot reduce room capacity while classes are scheduled');
                }
            }

            next();
        } catch (error) {
            next(error instanceof Error ? error : new Error('Unknown error'));
        }
    }

    // Validate maintenance schedule
    static async validateMaintenanceSchedule(
        req: Request,
        res: Response,
        next: NextFunction
    ): Promise<void> {
        try {
            const { startDate, endDate } = req.body;
            const room = req.room;

            if (!startDate || !endDate || !room) {
                return next();
            }

            const maintenanceStart = new Date(startDate);
            const maintenanceEnd = new Date(endDate);

            // Validate dates
            if (maintenanceStart <= new Date()) {
                throw new AppError(400, 'Maintenance start date must be in the future');
            }

            if (maintenanceEnd <= maintenanceStart) {
                throw new AppError(400, 'Maintenance end date must be after start date');
            }

            // Check for scheduling conflicts
            const hasConflicts = room.availability.some((day: AvailabilitySlot) => 
                day.date >= maintenanceStart &&
                day.date <= maintenanceEnd &&
                day.timeSlots.length > 0
            );

            if (hasConflicts) {
                throw new AppError(400, 'Cannot schedule maintenance - room has scheduled classes during this period');
            }

            next();
        } catch (error) {
            next(error instanceof Error ? error : new Error('Unknown error'));
        }
    }

    // Validate time slot format
    static validateTimeSlotFormat(
        req: Request,
        res: Response,
        next: NextFunction
    ): void {
        try {
            const timeSlot: TimeSlot = {
                start: req.query.timeStart as string,
                end: req.query.timeEnd as string
            };
    
            const timeRegex = /^([01]\d|2[0-3]):([0-5]\d)$/;
            if (!timeRegex.test(timeSlot.start) || !timeRegex.test(timeSlot.end)) {
                throw new AppError(400, 'Time must be in HH:mm format (24-hour)');
            }
    
            const startMinutes = RoomMiddleware.convertTimeToMinutes(timeSlot.start);
            const endMinutes = RoomMiddleware.convertTimeToMinutes(timeSlot.end);
    
            if (startMinutes >= endMinutes) {
                throw new AppError(400, 'End time must be after start time');
            }
    
            next();
        } catch (error) {
            next(error instanceof Error ? error : new Error('Unknown error'));
        }
    }
    // Validate room status changes
    static async validateStatusChange(
        req: Request,
        res: Response,
        next: NextFunction
    ): Promise<void> {
        try {
            const room = req.room;
            const { status } = req.body;

            if (!status || !room) {
                return next();
            }

            if (status === 'inactive' && room.status !== 'inactive') {
                // Check for future bookings
                const hasBookings = room.availability.some((day: AvailabilitySlot) => 
                    day.date > new Date() && day.timeSlots.length > 0
                );

                if (hasBookings) {
                    throw new AppError(400, 'Cannot deactivate room with future bookings');
                }
            }

            next();
        } catch (error) {
            next(error instanceof Error ? error : new Error('Unknown error'));
        }
    }

    // Validate date range for reports
    static validateDateRange(
        req: Request,
        res: Response,
        next: NextFunction
    ): void {
        try {
            const { startDate, endDate } = req.query;
            
            if (!startDate || !endDate) {
                throw new AppError(400, 'Start and end dates are required');
            }

            const start = new Date(startDate as string);
            const end = new Date(endDate as string);

            if (isNaN(start.getTime()) || isNaN(end.getTime())) {
                throw new AppError(400, 'Invalid date format');
            }

            if (end <= start) {
                throw new AppError(400, 'End date must be after start date');
            }

            // Limit date range to reasonable period (e.g., 1 year)
            const maxRange = 365 * 24 * 60 * 60 * 1000; // 1 year in milliseconds
            if (end.getTime() - start.getTime() > maxRange) {
                throw new AppError(400, 'Date range cannot exceed 1 year');
            }

            next();
        } catch (error) {
            next(error instanceof Error ? error : new Error('Unknown error'));
        }
    }

    private static convertTimeToMinutes(time: string): number {
        const [hours, minutes] = time.split(':').map(Number);
        return hours * 60 + minutes;
    }
}