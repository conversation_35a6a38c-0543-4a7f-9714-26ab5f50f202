
import React, { useState, useEffect } from "react";
import { format } from "date-fns";
import { Clock } from "lucide-react";
import {
  Card,
  CardContent,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { UserRole } from "@/types";
import { hasPermission } from "@/lib/auth";
import { toast } from "sonner";
import { Link } from "react-router-dom";
import { getDailySchedule, ScheduleItem } from "@/services/scheduleService";

interface DailyScheduleViewProps {
  date: Date;
  userRole: UserRole | null;
}

const DailyScheduleView: React.FC<DailyScheduleViewProps> = ({ date, userRole }) => {
  const [scheduleItems, setScheduleItems] = useState<ScheduleItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  const canEditSchedule = hasPermission(['SuperAdmin', 'Manager']);

  useEffect(() => {
    const fetchSchedule = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const dateString = format(date, 'yyyy-MM-dd');
        const schedule = await getDailySchedule(dateString);

        setScheduleItems(schedule);
        setIsLoading(false);
      } catch (err) {
        console.error('Error fetching schedule:', err);
        setError('Failed to load schedule data');
        setIsLoading(false);
        toast.error('Failed to load schedule data');
      }
    };

    fetchSchedule();
  }, [date]);

  const handlePrintSchedule = () => {
    toast.info('Printing daily schedule...');
    // Implementation would go here
  };

  if (error) {
    return (
      <div className="text-center p-6 text-destructive">
        <p>{error}</p>
        <Button variant="outline" className="mt-4" onClick={() => window.location.reload()}>
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">
          Schedule for {format(date, 'EEEE, MMMM d, yyyy')}
        </h2>
        <Button variant="outline" onClick={handlePrintSchedule}>
          Print Schedule
        </Button>
      </div>

      {isLoading ? (
        <div className="space-y-4">
          <Skeleton className="h-28 w-full" />
          <Skeleton className="h-28 w-full" />
          <Skeleton className="h-28 w-full" />
        </div>
      ) : scheduleItems.length === 0 ? (
        <div className="text-center p-10 border border-dashed rounded-lg">
          <p className="text-muted-foreground">No classes scheduled for this day</p>
        </div>
      ) : (
        <div className="grid gap-4">
          {scheduleItems.map((item) => (
            <Card key={item.id} className="overflow-hidden">
              <div className="bg-primary h-1"></div>
              <CardContent className="p-0">
                <div className="grid grid-cols-1 md:grid-cols-12 gap-4">
                  <div className="p-4 md:col-span-2 bg-muted/30 flex flex-col justify-center items-center">
                    <Clock className="h-5 w-5 mb-1 text-muted-foreground" />
                    <div className="text-center">
                      <div className="font-medium">{item.timeStart}</div>
                      <div className="text-sm text-muted-foreground">to</div>
                      <div className="font-medium">{item.timeEnd}</div>
                    </div>
                  </div>
                  <div className="p-4 md:col-span-8">
                    <h3 className="font-semibold text-lg">
                      {item.className}
                      <span className="ml-2 text-sm font-normal text-muted-foreground">
                        ({item.classLevel})
                      </span>
                    </h3>
                    <div className="mt-2 text-sm text-muted-foreground grid grid-cols-1 md:grid-cols-2 gap-2">
                      <div>
                        <span className="font-medium">Teacher:</span> {item.teacher}
                      </div>
                      <div>
                        <span className="font-medium">Room:</span> {item.room}
                      </div>
                    </div>
                  </div>
                  {canEditSchedule && (
                    <div className="p-4 md:col-span-2 flex items-center justify-center border-t md:border-t-0 md:border-l">
                      <Link to={`/classes/${item.id}/scheduling`}>
                        <Button variant="outline" size="sm">
                          Manage
                        </Button>
                      </Link>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default DailyScheduleView;
