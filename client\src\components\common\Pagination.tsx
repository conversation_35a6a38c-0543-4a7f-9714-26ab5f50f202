
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Button } from "@/components/ui/button";

export interface PaginationProps {
  page?: number;
  currentPage?: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  count?: number; // Support both count and totalPages for backward compatibility
}

const Pagination = ({ 
  page, 
  currentPage, 
  totalPages, 
  onPageChange,
  count // This is used for backward compatibility
}: PaginationProps) => {
  // Use either page or currentPage, defaulting to 1
  const activePage = page ?? currentPage ?? 1;
  
  // If totalPages is not provided but count is, calculate totalPages
  const totalPageCount = totalPages || (count ? Math.ceil(count / 10) : 1);
  
  // Generate page numbers with ellipsis for larger pagination
  const getPageNumbers = () => {
    const pages = [];
    const MAX_VISIBLE_PAGES = 5;
    
    if (totalPageCount <= MAX_VISIBLE_PAGES) {
      // Show all pages if there are fewer than MAX_VISIBLE_PAGES
      for (let i = 1; i <= totalPageCount; i++) {
        pages.push(i);
      }
    } else {
      // Always show first and last page
      pages.push(1);
      
      // Determine range around current page
      let startPage = Math.max(2, activePage - 1);
      let endPage = Math.min(totalPageCount - 1, activePage + 1);
      
      // Adjust range to show MAX_VISIBLE_PAGES-2 pages (excluding first and last)
      if (endPage - startPage + 1 < MAX_VISIBLE_PAGES - 2) {
        if (startPage === 2) {
          // We're near the start, extend endPage
          endPage = Math.min(totalPageCount - 1, startPage + (MAX_VISIBLE_PAGES - 3));
        } else if (endPage === totalPageCount - 1) {
          // We're near the end, lower startPage
          startPage = Math.max(2, endPage - (MAX_VISIBLE_PAGES - 3));
        }
      }
      
      // Add ellipsis before middle pages if needed
      if (startPage > 2) {
        pages.push("...");
      }
      
      // Add middle pages
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }
      
      // Add ellipsis after middle pages if needed
      if (endPage < totalPageCount - 1) {
        pages.push("...");
      }
      
      // Add last page
      if (totalPageCount > 1) {
        pages.push(totalPageCount);
      }
    }
    
    return pages;
  };

  return (
    <div className="flex items-center justify-center space-x-1">
      <Button
        variant="outline"
        size="icon"
        onClick={() => onPageChange(activePage - 1)}
        disabled={activePage === 1}
        aria-label="Previous page"
      >
        <ChevronLeft className="h-4 w-4" />
      </Button>
      
      {getPageNumbers().map((pageNumber, index) => (
        <Button
          key={index}
          variant={pageNumber === activePage ? "default" : "outline"}
          size="sm"
          onClick={() => typeof pageNumber === "number" && onPageChange(pageNumber)}
          disabled={pageNumber === "..."}
          className="min-w-[32px]"
        >
          {pageNumber}
        </Button>
      ))}
      
      <Button
        variant="outline"
        size="icon"
        onClick={() => onPageChange(activePage + 1)}
        disabled={activePage === totalPageCount}
        aria-label="Next page"
      >
        <ChevronRight className="h-4 w-4" />
      </Button>
    </div>
  );
};

export default Pagination;
