
import { useState } from "react";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useFieldArray, useForm } from "react-hook-form";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Trash2, Plus } from "lucide-react";
import { TeacherFormData } from "@/types/class";

// Define the teacher schedule schema
const scheduleItemSchema = z.object({
  day: z.string().min(1, { message: "Day is required" }),
  timeStart: z.string().min(1, { message: "Start time is required" }),
  timeEnd: z.string().min(1, { message: "End time is required" }),
});

// Define the teacher schema
const teacherSchema = z.object({
  teacherId: z.string().min(1, { message: "Teacher is required" }),
  schedule: z.array(scheduleItemSchema).optional(),
});

// Define the form schema
const formSchema = z.object({
  teachers: z.array(teacherSchema).min(1, {
    message: "At least one teacher must be assigned",
  }),
});

export type TeachersFormValues = z.infer<typeof formSchema>;

// Define props for this component
interface ClassFormTeachersProps {
  onSubmit: (data: TeachersFormValues) => void;
  onBack: () => void;
  initialValues?: TeachersFormValues;
  defaultValues?: TeachersFormValues;
  availableTeachers: Array<{ id: string; name: string }>;
  isLoading?: boolean;
}

const DAYS_OF_WEEK = [
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
  "Sunday",
];

const ClassFormTeachers = ({
  onSubmit,
  onBack,
  defaultValues,
  availableTeachers = [],
  isLoading = false,
}: ClassFormTeachersProps) => {
  const [selectedTeachers, setSelectedTeachers] = useState<string[]>(
    defaultValues?.teachers.map((t) => t.teacherId) || []
  );

  // Create the form
  const form = useForm<TeachersFormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: defaultValues || {
      teachers: [{ teacherId: "", schedule: [] }],
    },
  });

  // Setup field array
  const { fields, append, remove } = useFieldArray({
    name: "teachers",
    control: form.control,
  });

  // Handle form submission
  const handleSubmit = (data: TeachersFormValues) => {
    onSubmit(data);
  };

  // Add a new teacher
  const addTeacher = () => {
    append({ teacherId: "", schedule: [] });
  };

  // Add a schedule item to a teacher
  const addScheduleItem = (teacherIndex: number) => {
    const currentTeachers = form.getValues().teachers;
    const currentSchedule = currentTeachers[teacherIndex].schedule || [];
    
    form.setValue(`teachers.${teacherIndex}.schedule`, [
      ...currentSchedule,
      { day: "", timeStart: "", timeEnd: "" },
    ]);
  };

  // Remove a schedule item from a teacher
  const removeScheduleItem = (teacherIndex: number, scheduleIndex: number) => {
    const currentTeachers = form.getValues().teachers;
    const currentSchedule = [...(currentTeachers[teacherIndex].schedule || [])];
    currentSchedule.splice(scheduleIndex, 1);
    
    form.setValue(`teachers.${teacherIndex}.schedule`, currentSchedule);
  };

  // Filter out already selected teachers
  const getAvailableTeachers = (currentTeacherId: string) => {
    return availableTeachers.filter(
      (teacher) =>
        teacher.id === currentTeacherId ||
        !selectedTeachers.includes(teacher.id)
    );
  };

  // Update selected teachers when a teacher is selected
  const handleTeacherChange = (teacherId: string, index: number) => {
    const currentValues = form.getValues();
    const oldTeacherId = currentValues.teachers[index].teacherId;
    
    // Remove old teacher ID from selected list if it exists
    if (oldTeacherId) {
      setSelectedTeachers((prev) =>
        prev.filter((id) => id !== oldTeacherId)
      );
    }
    
    // Add new teacher ID to selected list
    if (teacherId) {
      setSelectedTeachers((prev) => [...prev, teacherId]);
    }
    
    // Update the form
    form.setValue(`teachers.${index}.teacherId`, teacherId);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
        <div className="space-y-4">
          {fields.map((field, teacherIndex) => (
            <Card key={field.id} className="border-border">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg flex justify-between items-center">
                  <span>Teacher Assignment</span>
                  {fields.length > 1 && (
                    <Button
                      type="button"
                      variant="destructive"
                      size="sm"
                      onClick={() => {
                        // Remove teacher ID from selected list
                        const teacherId = form.getValues().teachers[teacherIndex].teacherId;
                        if (teacherId) {
                          setSelectedTeachers((prev) =>
                            prev.filter((id) => id !== teacherId)
                          );
                        }
                        remove(teacherIndex);
                      }}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </CardTitle>
                <CardDescription>
                  Assign a teacher and their schedule for this class
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name={`teachers.${teacherIndex}.teacherId`}
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Teacher</FormLabel>
                        <Select
                          onValueChange={(value) => handleTeacherChange(value, teacherIndex)}
                          value={field.value}
                          disabled={isLoading}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a teacher" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {isLoading ? (
                              <SelectItem value="loading">
                                Loading teachers...
                              </SelectItem>
                            ) : getAvailableTeachers(field.value).length === 0 ? (
                              <SelectItem value="none">
                                No teachers available
                              </SelectItem>
                            ) : (
                              getAvailableTeachers(field.value).map((teacher) => (
                                <SelectItem key={teacher.id} value={teacher.id}>
                                  {teacher.name}
                                </SelectItem>
                              ))
                            )}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <FormLabel className="text-base">Schedule</FormLabel>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => addScheduleItem(teacherIndex)}
                      >
                        <Plus className="h-4 w-4 mr-1" />
                        Add Time
                      </Button>
                    </div>

                    {form.getValues().teachers[teacherIndex].schedule?.map(
                      (scheduleItem, scheduleIndex) => (
                        <div
                          key={`schedule-${teacherIndex}-${scheduleIndex}`}
                          className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end border p-3 rounded-md"
                        >
                          <FormField
                            control={form.control}
                            name={`teachers.${teacherIndex}.schedule.${scheduleIndex}.day`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Day</FormLabel>
                                <Select
                                  onValueChange={field.onChange}
                                  value={field.value}
                                >
                                  <FormControl>
                                    <SelectTrigger>
                                      <SelectValue placeholder="Select day" />
                                    </SelectTrigger>
                                  </FormControl>
                                  <SelectContent>
                                    {DAYS_OF_WEEK.map((day) => (
                                      <SelectItem key={day} value={day}>
                                        {day}
                                      </SelectItem>
                                    ))}
                                  </SelectContent>
                                </Select>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name={`teachers.${teacherIndex}.schedule.${scheduleIndex}.timeStart`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>Start Time</FormLabel>
                                <FormControl>
                                  <Input type="time" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <FormField
                            control={form.control}
                            name={`teachers.${teacherIndex}.schedule.${scheduleIndex}.timeEnd`}
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel>End Time</FormLabel>
                                <FormControl>
                                  <Input type="time" {...field} />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          <Button
                            type="button"
                            variant="destructive"
                            size="icon"
                            onClick={() =>
                              removeScheduleItem(teacherIndex, scheduleIndex)
                            }
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      )
                    )}

                    {(!form.getValues().teachers[teacherIndex].schedule ||
                      form.getValues().teachers[teacherIndex].schedule
                        .length === 0) && (
                      <div className="text-center p-4 border border-dashed rounded-md text-muted-foreground">
                        No schedule items added yet. Click the "Add Time" button
                        to add a schedule.
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}

          <Button
            type="button"
            variant="outline"
            className="w-full"
            onClick={addTeacher}
            disabled={
              availableTeachers.length === 0 ||
              selectedTeachers.length >= availableTeachers.length
            }
          >
            <Plus className="h-4 w-4 mr-1" />
            Add Another Teacher
          </Button>
        </div>

        <div className="flex justify-between">
          <Button type="button" variant="outline" onClick={onBack}>
            Back
          </Button>
          <Button type="submit" disabled={isLoading}>
            {isLoading ? "Loading..." : "Continue"}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default ClassFormTeachers;
