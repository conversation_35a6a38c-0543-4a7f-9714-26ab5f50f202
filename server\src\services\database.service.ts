// server/src/services/database.service.ts
import mongoose from 'mongoose';
import { exec } from 'child_process';
import path from 'path';
import fs from 'fs';
import { DB_CONFIG } from '../config/database.config';
import { SystemLogger } from './logger.service';

export class DatabaseService {
    private static instance: DatabaseService;
    private backupInterval: NodeJS.Timeout | null = null;

    private constructor() {}

    static getInstance(): DatabaseService {
        if (!DatabaseService.instance) {
            DatabaseService.instance = new DatabaseService();
        }
        return DatabaseService.instance;
    }

    async connect(): Promise<void> {
        try {
            await mongoose.connect(DB_CONFIG.URI, DB_CONFIG.OPTIONS);
            
            console.log('Connected to MongoDB successfully');
            await SystemLogger.log({
                severity: 'info',
                category: 'system',
                action: 'database_connection',
                performedBy: 'system',
                details: { status: 'connected' },
                status: 'success',
                timestamp: new Date()
            });

            if (DB_CONFIG.BACKUP.ENABLED) {
                this.initializeBackupSchedule();
            }

            mongoose.connection.on('error', this.handleConnectionError);
            mongoose.connection.on('disconnected', this.handleDisconnection);

        } catch (error: unknown) {
          console.error('MongoDB connection error:', error);
          await SystemLogger.log({
              severity: 'error',
              category: 'system',
              action: 'database_connection',
              performedBy: 'system',
              details: { 
                  error: error instanceof Error ? error.message : 'Unknown error'
              },
              status: 'failed',
              timestamp: new Date()
          });
          throw error;
        }
    }

    private async handleConnectionError(error: Error): Promise<void> {
      await SystemLogger.log({
        severity: 'error',
        category: 'system',
        action: 'database_error',
        performedBy: 'system',
        details: { 
            error: error instanceof Error ? error.message : 'Unknown error'
        },
        status: 'failed',
        timestamp: new Date()
    });
        
        setTimeout(async () => {
            try {
                await mongoose.connect(DB_CONFIG.URI, DB_CONFIG.OPTIONS);
            } catch (error) {
                console.error('Reconnection failed:', error);
            }
        }, 5000);
    }

    private async handleDisconnection(): Promise<void> {
        await SystemLogger.log({
            severity: 'warning',
            category: 'system',
            action: 'database_disconnection',
            performedBy: 'system',
            details: { timestamp: new Date() },
            status: 'warning',
            timestamp: new Date()
        });
        
        try {
            await mongoose.connect(DB_CONFIG.URI, DB_CONFIG.OPTIONS);
        } catch (error) {
            console.error('Reconnection failed:', error);
        }
    }

    private initializeBackupSchedule(): void {
        if (!fs.existsSync(DB_CONFIG.BACKUP.PATH)) {
            fs.mkdirSync(DB_CONFIG.BACKUP.PATH, { recursive: true });
        }

        this.backupInterval = setInterval(
            () => this.performBackup(),
            DB_CONFIG.BACKUP.INTERVAL
        );

        this.performBackup();
    }

    private async performBackup(): Promise<void> {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupPath = path.join(DB_CONFIG.BACKUP.PATH, `backup-${timestamp}`);

        try {
            const cmd = `mongodump --uri="${DB_CONFIG.URI}" --out="${backupPath}" ${
                DB_CONFIG.BACKUP.COMPRESSION ? '--gzip' : ''
            }`;

            exec(cmd, async (error, stdout, stderr) => {
              if (error) {
                await SystemLogger.log({
                    severity: 'error',
                    category: 'system',
                    action: 'database_backup',
                    performedBy: 'system',
                    details: { 
                        error: error instanceof Error ? error.message : 'Unknown error'
                    },
                    status: 'failed',
                    timestamp: new Date()
                });
                console.error('Backup failed:', error);
                return;
            }

                await SystemLogger.log({
                    severity: 'info',
                    category: 'system',
                    action: 'database_backup',
                    performedBy: 'system',
                    details: { path: backupPath },
                    status: 'success',
                    timestamp: new Date()
                });

                this.cleanOldBackups();
            });
        } catch (error) {
            console.error('Backup error:', error);
            await SystemLogger.log({
                severity: 'error',
                category: 'system',
                action: 'database_backup',
                performedBy: 'system',
                details: { 
                  error: error instanceof Error ? error.message : 'Unknown error: database_backup failed'
              },
                status: 'failed',
                timestamp: new Date()
            });
        }
    }

    private async cleanOldBackups(): Promise<void> {
        const retentionDate = new Date();
        retentionDate.setDate(retentionDate.getDate() - DB_CONFIG.BACKUP.RETENTION_DAYS);

        try {
            const files = fs.readdirSync(DB_CONFIG.BACKUP.PATH);
            
            for (const file of files) {
                const filePath = path.join(DB_CONFIG.BACKUP.PATH, file);
                const stats = fs.statSync(filePath);

                if (stats.isDirectory() && stats.birthtime < retentionDate) {
                    fs.rmSync(filePath, { recursive: true, force: true });
                    
                    await SystemLogger.log({
                        severity: 'info',
                        category: 'system',
                        action: 'backup_cleanup',
                        performedBy: 'system',
                        details: { removedPath: filePath },
                        status: 'success',
                        timestamp: new Date()
                    });
                }
            }
        } catch (error: unknown) {
          console.error('Cleanup error:', error);
          await SystemLogger.log({
              severity: 'error',
              category: 'system',
              action: 'backup_cleanup',
              performedBy: 'system',
              details: { 
                  error: error instanceof Error ? error.message : 'Unknown error'
              },
              status: 'failed',
              timestamp: new Date()
          });
        }
    }

    async disconnect(): Promise<void> {
        if (this.backupInterval) {
            clearInterval(this.backupInterval);
        }
        await mongoose.disconnect();
    }
}