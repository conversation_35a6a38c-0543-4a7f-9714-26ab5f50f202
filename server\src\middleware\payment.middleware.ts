// server/src/middleware/payment.middleware.ts
import { Request, Response, NextFunction } from 'express';
import mongoose, { Types } from 'mongoose';
import { Payment } from '../models/payment.model';
import { Student } from '../models/student.model';
import { AppError } from '../types/error.types';
import { IUser } from '../types/auth.types';
import { PaymentMethod, PaymentStatus } from '../types/payment.types';

export class PaymentMiddleware {
    // Validate payment exists
    static async validatePaymentExists(
        req: Request,
        res: Response,
        next: NextFunction
    ): Promise<void> {
        try {
            const paymentId = req.params.id;
            if (!mongoose.Types.ObjectId.isValid(paymentId)) {
                throw new AppError(400, 'Invalid payment ID format');
            }

            const payment = await Payment.findById(paymentId);
            if (!payment) {
                throw new AppError(404, 'Payment not found');
            }

            // Attach payment to request for later use
            req.payment = payment;
            next();
        } catch (error) {
            next(error);
        }
    }

    // Validate payment access based on user role
    static async validatePaymentAccess(
        req: Request,
        res: Response,
        next: NextFunction
    ): Promise<void> {
        try {
            const currentUser = req.user as IUser;
            const payment = req.payment;

            if (!payment || !currentUser?._id) {
                throw new AppError(401, 'Authentication required');
            }

            // SuperAdmin and Manager can access all payments
            if (['superAdmin', 'manager'].includes(currentUser.role)) {
                return next();
            }

            // Secretary can only access non-voided payments
            if (currentUser.role === 'secretary' && payment.status === 'voided') {
                throw new AppError(403, 'Not authorized to access voided payments');
            }

            next();
        } catch (error) {
            next(error);
        }
    }

    // Validate student exists
    static async validateStudent(
        req: Request,
        res: Response,
        next: NextFunction
    ): Promise<void> {
        try {
            const studentId = req.params.studentId || req.body.studentId;
            if (!mongoose.Types.ObjectId.isValid(studentId)) {
                throw new AppError(400, 'Invalid student ID format');
            }

            const student = await Student.findById(studentId);
            if (!student) {
                throw new AppError(404, 'Student not found');
            }

            // Attach student to request for later use
            req.student = student;
            next();
        } catch (error) {
            next(error);
        }
    }

    // Validate payment amount
    static async validatePaymentAmount(
        req: Request,
        res: Response,
        next: NextFunction
    ): Promise<void> {
        try {
            const { amount } = req.body;

            if (amount <= 0) {
                throw new AppError(400, 'Payment amount must be greater than 0');
            }

            // Validate amount has maximum 2 decimal places
            const decimalPlaces = amount.toString().split('.')[1]?.length || 0;
            if (decimalPlaces > 2) {
                throw new AppError(400, 'Payment amount cannot have more than 2 decimal places');
            }

            next();
        } catch (error) {
            next(error);
        }
    }

    // Validate duplicate payment prevention
    static async validateDuplicatePayment(
        req: Request,
        res: Response,
        next: NextFunction
    ): Promise<void> {
        try {
            const { studentId, amount, date } = req.body;

            // Check for duplicate payment within same day
            const startOfDay = new Date(date);
            startOfDay.setHours(0, 0, 0, 0);
            
            const endOfDay = new Date(date);
            endOfDay.setHours(23, 59, 59, 999);

            const existingPayment = await Payment.findOne({
                studentId: new Types.ObjectId(studentId),
                amount,
                date: { $gte: startOfDay, $lte: endOfDay },
                status: { $ne: 'voided' }
            });

            if (existingPayment) {
                throw new AppError(400, 'Possible duplicate payment detected for the same student, amount, and date');
            }

            next();
        } catch (error) {
            next(error);
        }
    }

    // Validate payment modification
    static async validatePaymentModification(
        req: Request,
        res: Response,
        next: NextFunction
    ): Promise<void> {
        try {
            const payment = req.payment;
            if (!payment) {
                throw new AppError(404, 'Payment not found');
            }

            // Cannot modify voided payments
            if (payment.status === 'voided') {
                throw new AppError(400, 'Cannot modify a voided payment');
            }

            // Validate critical field modifications
            const updateData = req.body;
            
            // Prevent status modification through update endpoint
            if (updateData.status) {
                throw new AppError(400, 'Payment status cannot be modified directly');
            }

            // If amount is being modified, validate it's not less than any recorded payments
            if (updateData.amount && updateData.amount < payment.amount) {
                throw new AppError(400, 'New amount cannot be less than the original payment amount');
            }

            // Validate date modifications
            if (updateData.date) {
                const newDate = new Date(updateData.date);
                if (newDate > new Date()) {
                    throw new AppError(400, 'Payment date cannot be in the future');
                }
            }

            next();
        } catch (error) {
            next(error);
        }
    }

    // Validate payment void
    static async validatePaymentVoid(
        req: Request,
        res: Response,
        next: NextFunction
    ): Promise<void> {
        try {
            const payment = req.payment;
            if (!payment) {
                throw new AppError(404, 'Payment not found');
            }

            // Check if already voided
            if (payment.status === 'voided') {
                throw new AppError(400, 'Payment is already voided');
            }

            // Validate void reason
            const { reason } = req.body;
            if (!reason || reason.trim().length < 10) {
                throw new AppError(400, 'A detailed void reason (minimum 10 characters) is required');
            }

            // Check time limit for voiding (e.g., within 30 days)
            const thirtyDaysAgo = new Date();
            thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

            if (payment.date < thirtyDaysAgo) {
                throw new AppError(400, 'Cannot void payments older than 30 days');
            }

            next();
        } catch (error) {
            next(error);
        }
    }
}