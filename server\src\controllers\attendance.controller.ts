import { Request, Response } from 'express';
import { AttendanceService } from '../services/attendance.service';
import { AppError } from '../types/error.types';
import { SystemLogger } from '../services/logger.service';
import {
    AttendanceQueryOptions,
    MarkAttendanceDTO,
    BulkMarkAttendanceDTO,
    AddExcuseDTO,
    VerifyExcuseDTO,
    AttendanceExportOptions
} from '../types/attendance.types';
import mongoose from 'mongoose';

export class AttendanceController {
    static async getAttendanceRecords(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const {
            page,
            limit,
            sortBy,
            sortOrder,
            startDate,
            endDate,
            classId,
            teacherId,
            studentId,
            status,
            isMakeupClass
        } = req.query;

        const options: AttendanceQueryOptions = {
            page: page ? parseInt(page as string) : undefined,
            limit: limit ? parseInt(limit as string) : undefined,
            sortBy: sortBy as 'date' | 'modifiedAt',
            sortOrder: sortOrder as 'asc' | 'desc',
            startDate: startDate ? new Date(startDate as string) : undefined,
            endDate: endDate ? new Date(endDate as string) : undefined,
            classId: classId as string,
            teacherId: teacherId as string,
            studentId: studentId as string,
            status: status as any,
            isMakeupClass: isMakeupClass ? isMakeupClass === 'true' : undefined
        };

        const result = await AttendanceService.getAttendanceRecords(
            options,
            currentUser._id.toString()
        );

        res.json({
            success: true,
            data: result.records,
            pagination: result.pagination
        });
    }

    static async markAttendance(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { classId } = req.params;
        const { date, ...attendanceData } = req.body;

        if (!date) {
            throw new AppError(400, 'Date is required');
        }

        const record = await AttendanceService.markAttendance(
            classId,
            new Date(date),
            attendanceData as MarkAttendanceDTO,
            currentUser._id.toString()
        );

        await SystemLogger.log({
            severity: 'info',
            category: 'attendance',
            action: 'mark_attendance',
            performedBy: currentUser._id.toString(),
            targetId: classId,
            details: {
                studentId: attendanceData.studentId,
                date,
                status: attendanceData.status
            },
            status: 'success',
            timestamp: new Date()
        });

        res.json({
            success: true,
            message: 'Attendance marked successfully',
            data: record
        });
    }

    static async bulkMarkAttendance(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { classId } = req.params;
        const { date, records } = req.body;

        if (!date || !records) {
            throw new AppError(400, 'Date and attendance records are required');
        }

        const result = await AttendanceService.bulkMarkAttendance(
            classId,
            new Date(date),
            { records } as BulkMarkAttendanceDTO,
            currentUser._id.toString()
        );

        await SystemLogger.log({
            severity: 'info',
            category: 'attendance',
            action: 'bulk_mark_attendance',
            performedBy: currentUser._id.toString(),
            targetId: classId,
            details: {
                date,
                recordCount: records.length
            },
            status: 'success',
            timestamp: new Date()
        });

        res.json({
            success: true,
            message: 'Bulk attendance marked successfully',
            data: result
        });
    }

    static async addExcuse(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { classId } = req.params;
        const { date, ...excuseData } = req.body;

        if (!date) {
            throw new AppError(400, 'Date is required');
        }

        const record = await AttendanceService.addExcuse(
            classId,
            new Date(date),
            excuseData as AddExcuseDTO,
            currentUser._id.toString()
        );

        await SystemLogger.log({
            severity: 'info',
            category: 'attendance',
            action: 'add_excuse',
            performedBy: currentUser._id.toString(),
            targetId: classId,
            details: {
                studentId: excuseData.studentId,
                date,
                reason: excuseData.reason
            },
            status: 'success',
            timestamp: new Date()
        });

        res.json({
            success: true,
            message: 'Excuse added successfully',
            data: record
        });
    }

    static async verifyExcuse(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { classId, studentId } = req.params;
        const { date, ...verificationData } = req.body;

        if (!date) {
            throw new AppError(400, 'Date is required');
        }

        const record = await AttendanceService.verifyExcuse(
            classId,
            new Date(date),
            studentId,
            verificationData as VerifyExcuseDTO,
            currentUser._id.toString()
        );

        await SystemLogger.log({
            severity: 'info',
            category: 'attendance',
            action: 'verify_excuse',
            performedBy: currentUser._id.toString(),
            targetId: classId,
            details: {
                studentId,
                date,
                status: verificationData.status
            },
            status: 'success',
            timestamp: new Date()
        });

        res.json({
            success: true,
            message: 'Excuse verified successfully',
            data: record
        });
    }

    static async getStudentAttendanceStats(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }
    
        const { studentId } = req.params;
        const today = new Date();
        const thirtyDaysAgo = new Date(today);
        thirtyDaysAgo.setDate(today.getDate() - 30);
        
        // Default to last 30 days if not provided
        const startDate = req.query.startDate ? new Date(req.query.startDate as string) : thirtyDaysAgo;
        const endDate = req.query.endDate ? new Date(req.query.endDate as string) : today;
    
        const stats = await AttendanceService.getStudentAttendanceStats(
            studentId,
            startDate,
            endDate
        );
    
        res.json({
            success: true,
            data: stats
        });
    }

    static async getClassAttendanceStats(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { classId } = req.params;
        const { startDate, endDate } = req.query;

        if (!startDate || !endDate) {
            throw new AppError(400, 'Start date and end date are required');
        }

        const stats = await AttendanceService.getClassAttendanceStats(
            classId,
            new Date(startDate as string),
            new Date(endDate as string)
        );

        res.json({
            success: true,
            data: stats
        });
    }

    static async exportAttendance(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const {
            format = 'csv',
            startDate,
            endDate,
            includeStudentDetails,
            includeExcuses,
            includeMakeupClasses,
            groupBy
        } = req.query;

        if (!startDate || !endDate) {
            throw new AppError(400, 'Start date and end date are required');
        }

        const options: AttendanceExportOptions = {
            format: format as 'csv' | 'json',
            dateRange: {
                start: new Date(startDate as string),
                end: new Date(endDate as string)
            },
            includeStudentDetails: includeStudentDetails === 'true',
            includeExcuses: includeExcuses === 'true',
            includeMakeupClasses: includeMakeupClasses === 'true',
            groupBy: groupBy as 'date' | 'student' | 'class'
        };

        const exportData = await AttendanceService.exportAttendance(
            options,
            currentUser._id.toString()
        );

        const filename = `attendance_export_${new Date().toISOString()}.${format}`;
        res.setHeader('Content-Disposition', `attachment; filename=${filename}`);
        res.setHeader('Content-Type', format === 'csv' ? 'text/csv' : 'application/json');
        res.send(exportData);
    }

    static async getUnifiedStudentAttendance(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }
    
        const { studentId } = req.params;
        const { startDate, endDate } = req.query;
        
        if (!mongoose.Types.ObjectId.isValid(studentId)) {
            throw new AppError(400, 'Invalid student ID format');
        }
        
        const unifiedAttendance = await AttendanceService.getUnifiedStudentAttendance(
            studentId,
            currentUser._id.toString(),
            startDate ? new Date(startDate as string) : undefined,
            endDate ? new Date(endDate as string) : undefined
        );
    
        res.json({
            success: true,
            data: unifiedAttendance
        });
    }
}