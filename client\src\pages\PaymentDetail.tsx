
import { useParams, useNavigate, useSearchParams } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { fetchPayment } from "@/services/paymentService";
import MainLayout from "@/components/layout/MainLayout";
import PaymentDetailComponent from "@/components/payments/PaymentDetail";
import ReceiptTemplate from "@/components/payments/ReceiptTemplate";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ChevronLeft } from "lucide-react";
import { useState, useEffect } from "react";

export default function PaymentDetail() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const initialTab = searchParams.get('tab') === 'receipt' ? 'receipt' : 'details';
  const [activeTab, setActiveTab] = useState(initialTab);

  const { data, isLoading, isError, refetch } = useQuery({
    queryKey: ["payment", id],
    queryFn: () => fetchPayment(id!),
    enabled: !!id
  });

  // Update the active tab if the URL search parameter changes
  useEffect(() => {
    if (searchParams.get('tab') === 'receipt') {
      setActiveTab('receipt');
    }
  }, [searchParams]);

  const payment = data?.data;

  const handlePaymentUpdated = () => {
    refetch();
    // Switch to receipt tab when explicitly requested
    setActiveTab("receipt");
  };

  if (isLoading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
        </div>
      </MainLayout>
    );
  }

  if (isError || !payment) {
    return (
      <MainLayout>
        <div className="flex flex-col items-center justify-center min-h-[60vh]">
          <p className="text-xl text-muted-foreground mb-4">
            Could not load payment details
          </p>
          <Button onClick={() => refetch()}>Try Again</Button>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header with navigation */}
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => navigate("/payments")}
          >
            <ChevronLeft size={18} />
          </Button>
          <h1 className="text-2xl font-bold">Payment Details</h1>
        </div>

        <Tabs
          defaultValue="details"
          value={activeTab}
          onValueChange={setActiveTab}
        >
          <TabsList>
            <TabsTrigger value="details">Details</TabsTrigger>
            <TabsTrigger value="receipt">Receipt</TabsTrigger>
          </TabsList>

          <TabsContent value="details" className="mt-6">
            <PaymentDetailComponent
              paymentId={id!}
              onPaymentUpdated={handlePaymentUpdated}
            />
          </TabsContent>

          <TabsContent value="receipt" className="mt-6">
            <ReceiptTemplate payment={payment} />
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
}
