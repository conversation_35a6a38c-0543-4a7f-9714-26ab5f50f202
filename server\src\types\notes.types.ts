// server/src/types/notes.types.ts
import { Types } from 'mongoose';

// Base types
export type NoteVisibility = 'teacher_only' | 'all_staff' | 'manager_only';
export type NoteCategory = 'academic' | 'behavioral' | 'attendance' | 'general';

export type RelatedEntityType = 'attendance' | 'assessment' | 'payment' | 'class' | 'behavior';
export type RelatedModelType = 'Attendance' | 'Assessment' | 'Payment' | 'Class' | 'Behavior';

export const entityTypeToModel: Record<string, string> = {
    'attendance': 'Attendance',
    'assessment': 'Assessment',
    'payment': 'Payment',
    'class': 'Class',
    'behavior': 'Behavior'
};

// Note tag structure
export interface NoteTag {
    name: string;
    color?: string;
}

// Main note interface
export interface INote {
    _id?: Types.ObjectId;
    type: NoteCategory;
    visibility: NoteVisibility;
    content: string;
    tags: string[];
    createdBy: Types.ObjectId;
    createdAt: Date;
    modifiedAt: Date;
    modifiedBy: Types.ObjectId;

    // References (optional based on context)
    studentId?: Types.ObjectId;
    classId?: Types.ObjectId;

    // For linking to other entities
    relatedTo?: {
        type: RelatedEntityType | RelatedModelType;  // Allow both forms
        id: Types.ObjectId;
    };

    // Track modifications
    modificationHistory: Array<{
        modifiedBy: Types.ObjectId;
        timestamp: Date;
        changes: {
            field: string;
            oldValue: any;
            newValue: any;
        };
    }>;
}

// Query options for fetching notes
export interface NoteQueryOptions {
    page?: number;
    limit?: number;
    sortBy?: 'createdAt' | 'modifiedAt';
    sortOrder?: 'asc' | 'desc';
    type?: NoteCategory;
    visibility?: NoteVisibility;
    studentId?: string;
    classId?: string;
    tags?: string[];
    search?: string;
    fromDate?: Date;
    toDate?: Date;
    createdBy?: string;
}

// DTO for creating a new note
export interface CreateNoteDTO {
    type: NoteCategory;
    visibility: NoteVisibility;
    content: string;
    tags?: string[];
    studentId?: string;
    classId?: string;
    relatedTo?: {
        type: 'attendance' | 'assessment' | 'payment' | 'class' | 'behavior';
        id: string;
    };
}

// DTO for updating a note
export interface UpdateNoteDTO {
    content?: string;
    visibility?: NoteVisibility;
    tags?: string[];
}

// Response DTO
export interface NoteResponseDTO {
    id: string;
    type: NoteCategory;
    visibility: NoteVisibility;
    content: string;
    tags: string[];
    createdBy: {
        id: string;
        username: string;
    };
    createdAt: Date;
    modifiedAt: Date;
    modifiedBy: {
        id: string;
        username: string;
    };
    student?: {
        id: string;
        name: string;
    };
    class?: {
        id: string;
        name: string;
    };
    relatedTo?: {
        type: string;
        id: string;
        reference?: string; // Additional context about the related entity
    };
    modificationHistory: Array<{
        modifiedBy: string;
        timestamp: Date;
        changes: {
            field: string;
            oldValue: any;
            newValue: any;
        };
    }>;
}

// Bulk operations
export interface NoteBulkOperationDTO {
    noteIds: string[];
    operation: 'delete' | 'updateVisibility' | 'updateTags';
    value?: any;
}

// Search and filtering
export interface NoteSearchOptions extends NoteQueryOptions {
    searchFields?: Array<'content' | 'tags'>;
    includeArchived?: boolean;
    groupBy?: 'student' | 'class' | 'type' | 'date';
}

// Export options
export interface NoteExportOptions {
    format: 'csv' | 'json';
    fields?: string[];
    includeHistory?: boolean;
    dateRange?: {
        start: Date;
        end: Date;
    };
    groupBy?: 'student' | 'class' | 'type' | 'date';
}

// Error types
export interface NoteValidationError {
    field: string;
    message: string;
    code: string;
}

export interface NoteOperationError {
    operation: string;
    noteId: string;
    reason: string;
    details?: any;
}

// Event types for note-related notifications
export interface NoteEvent {
    type: 'created' | 'updated' | 'deleted';
    noteId: string;
    timestamp: Date;
    details: any;
    notifyUsers: string[];
}