// server/src/middleware/auth.middleware.ts
import { Request, Response, NextFunction } from 'express';
import jwt from 'jsonwebtoken';
import { AUTH_CONFIG } from '../config/auth.config';
import { User } from '../models/user.model';
import { UserRole } from '../types/user.types';

export const authenticateToken = async (
    req: Request,
    res: Response,
    next: NextFunction
): Promise<void> => {
    try {
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1];

        if (!token) {
            res.status(401).json({ message: 'Authentication token required' });
            return;
        }

        const decoded = jwt.verify(token, AUTH_CONFIG.JWT_SECRET) as { userId: string };
        const user = await User.findById(decoded.userId);

        if (!user || user.status !== 'active') {
            res.status(403).json({ message: 'Invalid or inactive user' });
            return;
        }

        req.user = user;
        next();
    } catch (error) {
        res.status(403).json({ message: 'Invalid token' });
        return;
    }
};

export const authorizeRoles = (...roles: UserRole[]) => {
    return (req: Request, res: Response, next: NextFunction): void => {
        if (!req.user || !roles.includes(req.user.role as UserRole)) {
            res.status(403).json({ message: 'Unauthorized access' });
            return;
        }
        next();
    };
};