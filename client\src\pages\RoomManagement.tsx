
import MainLayout from "@/components/layout/MainLayout";
import RoomDirectory from "@/components/rooms/RoomDirectory";
import { hasRole } from "@/lib/auth";
import { Navigate } from "react-router-dom";

const RoomManagement = () => {
  // Check if user has proper permissions
  const canViewRooms = hasRole(['superAdmin', 'manager', 'secretary', 'teacher']);

  if (!canViewRooms) {
    return <Navigate to="/dashboard" replace />;
  }

  return (
    <MainLayout>
      <RoomDirectory />
    </MainLayout>
  );
};

export default RoomManagement;
