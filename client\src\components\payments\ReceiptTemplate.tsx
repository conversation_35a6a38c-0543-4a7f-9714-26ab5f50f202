
import { useRef } from "react";
import { Payment } from "@/types/payment";
import { format } from "date-fns";
import { useReactToPrint } from "react-to-print";
import { Button } from "@/components/ui/button";
import { Printer, Download } from "lucide-react";
import { toast } from "sonner";

interface ReceiptTemplateProps {
  payment: Payment;
  logoUrl?: string;
  schoolName?: string;
  schoolAddress?: string;
  schoolContact?: string;
}

const ReceiptTemplate = ({ 
  payment, 
  logoUrl = "/placeholder.svg", 
  schoolName = "Vertex Education Center",
  schoolAddress = "123 Education St, Anytown, USA",
  schoolContact = "Tel: (************* | Email: <EMAIL>"
}: ReceiptTemplateProps) => {
  const receiptRef = useRef<HTMLDivElement>(null);

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), "MMMM d, yyyy");
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const handlePrintAction = useReactToPrint({
    contentRef: receiptRef,
    documentTitle: `Receipt-${payment.receiptNumber || payment.id}`,
    onAfterPrint: () => toast.success("Receipt printed successfully"),
    onPrintError: () => toast.error("Printing failed"),
  });

  // Wrapper function to handle prep before printing
  const handlePrint = () => {
    toast.info("Preparing receipt for printing...");
    setTimeout(() => {
      handlePrintAction();
    }, 500);
  };

  // Function to generate PDF
  const handleDownload = () => {
    // In a real app, this would generate and download a PDF
    toast.info("Downloading receipt as PDF...");
    setTimeout(() => {
      toast.success("Receipt downloaded successfully");
    }, 1500);
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-end space-x-2 print:hidden">
        <Button variant="outline" onClick={handlePrint}>
          <Printer className="mr-2 h-4 w-4" />
          Print
        </Button>
        <Button variant="outline" onClick={handleDownload}>
          <Download className="mr-2 h-4 w-4" />
          Download PDF
        </Button>
      </div>
      
      <div
        ref={receiptRef}
        className="bg-white border rounded-md p-8 max-w-2xl mx-auto"
      >
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div className="flex items-center gap-4">
            <img
              src={logoUrl}
              alt={schoolName}
              className="w-16 h-16 object-contain"
            />
            <div>
              <h1 className="text-2xl font-bold">{schoolName}</h1>
              <p className="text-sm text-gray-600">{schoolAddress}</p>
              <p className="text-sm text-gray-600">{schoolContact}</p>
            </div>
          </div>
          <div className="text-right">
            <h2 className="text-xl font-bold">RECEIPT</h2>
            <p className="text-sm font-medium">
              #{payment.receiptNumber || "N/A"}
            </p>
            <p className="text-sm text-gray-600">
              Date: {formatDate(payment.date)}
            </p>
          </div>
        </div>

        {/* Student Information */}
        <div className="mb-8">
          <h3 className="text-sm font-medium uppercase text-gray-500 mb-2">
            Student Information
          </h3>
          <div className="border-t border-b py-4">
            <p className="font-medium">
              {payment.student
                ? `${payment.student.firstName} ${payment.student.lastName}`
                : "Student Information Not Available"}
            </p>
            <p className="text-sm text-gray-600">Student ID: {payment.studentId}</p>
          </div>
        </div>

        {/* Payment Details */}
        <div className="mb-8">
          <h3 className="text-sm font-medium uppercase text-gray-500 mb-2">
            Payment Details
          </h3>
          <table className="w-full border-collapse">
            <thead>
              <tr className="border-b">
                <th className="py-2 text-left font-medium text-gray-600">Description</th>
                <th className="py-2 text-right font-medium text-gray-600">Amount</th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-b">
                <td className="py-4 text-left">{payment.description}</td>
                <td className="py-4 text-right font-bold">
                  {formatCurrency(payment.amount)}
                </td>
              </tr>
              <tr>
                <td className="py-4 text-right font-bold" colSpan={1}>
                  Total:
                </td>
                <td className="py-4 text-right font-bold">
                  {formatCurrency(payment.amount)}
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        {/* Payment Method */}
        <div className="mb-8">
          <h3 className="text-sm font-medium uppercase text-gray-500 mb-2">
            Payment Method
          </h3>
          <p className="capitalize">{payment.method.replace("_", " ")}</p>
          {payment.notes && (
            <p className="text-sm text-gray-600 mt-2">{payment.notes}</p>
          )}
        </div>

        {/* Next Payment Due */}
        {payment.nextDueDate && (
          <div className="mb-8">
            <h3 className="text-sm font-medium uppercase text-gray-500 mb-2">
              Next Payment Due
            </h3>
            <p className="font-medium">{formatDate(payment.nextDueDate)}</p>
          </div>
        )}

        {/* Footer */}
        <div className="mt-12 pt-8 border-t">
          <div className="flex justify-between">
            <div>
              <p className="text-sm text-gray-600">
                Recorded by: {payment.recordedBy}
              </p>
              <p className="text-sm text-gray-600">
                Date: {formatDate(payment.recordedAt)}
              </p>
            </div>
            <div className="text-right">
              <div className="mt-8 border-t border-dashed pt-2 w-48">
                <p className="text-sm text-gray-600">Authorized Signature</p>
              </div>
            </div>
          </div>
          <p className="text-xs text-center text-gray-500 mt-12">
            This is an official receipt from {schoolName}. Thank you for your payment.
          </p>
        </div>

        {/* Void Watermark */}
        {payment.status === "voided" && (
          <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
            <div className="rotate-45 text-red-200 text-9xl font-bold opacity-30">
              VOID
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ReceiptTemplate;
