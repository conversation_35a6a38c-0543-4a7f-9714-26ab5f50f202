// server/src/services/payment.service.ts
import mongoose, { FilterQuery } from 'mongoose';
import { Payment } from '../models/payment.model';
import { Student } from '../models/student.model';
import { AppError } from '../types/error.types';
import { SystemLogger } from './logger.service';
import {
    IPayment,
    PaymentQueryOptions,
    CreatePaymentDTO,
    UpdatePaymentDTO,
    VoidPaymentDTO,
    PaymentResponseDTO,
    PaymentExportOptions,
    StudentPaymentSummary,
    PaymentStatus,
    PaymentMethod
} from '../types/payment.types';


interface PaymentGroup {
    group: string;
    totalAmount: number;
    count: number;
    payments: any[];
}

interface PaymentGroupExport {
    group: string;
    totalAmount: number;
    count: number;
    payments: PaymentResponseDTO[];
}

interface PaymentStatistics {
    totalAmount: number;
    totalCount: number;
    averageAmount: number;
    groups: Array<{
        key: string;
        count: number;
        amount: number;
        percentage: number;
    }>;
    dateRange: {
        start: Date;
        end: Date;
    };
}

type PaymentExportData = PaymentResponseDTO[] | PaymentGroupExport[];

export class PaymentService {
    /**
     * Private Helper Methods
     */
    private static groupPaymentsByDaily(payments: any[]): { key: string; count: number; amount: number; percentage: number; }[] {
        const grouped = payments.reduce((acc: { [key: string]: { key: string; count: number; amount: number; percentage: number; } }, payment) => {
            const date = new Date(payment.date).toISOString().split('T')[0];
            if (!acc[date]) {
                acc[date] = { key: date, count: 0, amount: 0, percentage: 0 }; // Initialize with percentage
            }
            acc[date].count++;
            acc[date].amount += payment.amount;
            return acc;
        }, {});
    
        const totalAmount = Object.values(grouped).reduce((sum, group) => sum + group.amount, 0);
    
        // Calculate percentages
        Object.values(grouped).forEach(group => {
            group.percentage = (group.amount / totalAmount) * 100;
        });
    
        return Object.values(grouped).sort((a, b) => a.key.localeCompare(b.key));
    }

    private static groupPaymentsByMonthly(payments: any[]): { key: string; count: number; amount: number; percentage: number; }[] {
        const grouped = payments.reduce((acc: { [key: string]: { key: string; count: number; amount: number; percentage: number; } }, payment) => {
            const date = new Date(payment.date);
            const monthYear = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
            if (!acc[monthYear]) {
                acc[monthYear] = { key: monthYear, count: 0, amount: 0, percentage: 0 }; // Initialize with percentage
            }
            acc[monthYear].count++;
            acc[monthYear].amount += payment.amount;
            return acc;
        }, {});
    
        const totalAmount = Object.values(grouped).reduce((sum, group) => sum + group.amount, 0);
    
        // Calculate percentages
        Object.values(grouped).forEach(group => {
            group.percentage = (group.amount / totalAmount) * 100;
        });
    
        return Object.values(grouped).sort((a, b) => a.key.localeCompare(b.key));
    }

    private static groupPaymentsByMethod(payments: any[]): { key: string; count: number; amount: number; percentage: number; }[] {
        const grouped = payments.reduce((acc: { [key: string]: { key: string; count: number; amount: number; percentage: number; } }, payment) => {
            const method = payment.method;
            if (!acc[method]) {
                acc[method] = { key: method, count: 0, amount: 0, percentage: 0 }; // Initialize with percentage
            }
            acc[method].count++;
            acc[method].amount += payment.amount;
            return acc;
        }, {});
    
        const totalAmount = Object.values(grouped).reduce((sum, group) => sum + group.amount, 0);
    
        // Calculate percentages
        Object.values(grouped).forEach(group => {
            group.percentage = (group.amount / totalAmount) * 100;
        });
    
        return Object.values(grouped);
    }

    private static groupPaymentsByStatus(payments: any[]): { key: string; count: number; amount: number; percentage: number; }[] {
        const grouped = payments.reduce((acc: { [key: string]: { key: string; count: number; amount: number; percentage: number; } }, payment) => {
            const status = payment.status;
            if (!acc[status]) {
                acc[status] = { key: status, count: 0, amount: 0, percentage: 0 }; // Initialize with percentage
            }
            acc[status].count++;
            acc[status].amount += payment.amount;
            return acc;
        }, {});
    
        const totalAmount = Object.values(grouped).reduce((sum, group) => sum + group.amount, 0);
    
        // Calculate percentages
        Object.values(grouped).forEach(group => {
            group.percentage = (group.amount / totalAmount) * 100;
        });
    
        return Object.values(grouped);
    }

    /**
     * Gets the role-specific query filter for payments
     */
    private static getRoleAccessQuery(userRole: string): FilterQuery<IPayment> {
        switch (userRole) {
            case 'superAdmin':
            case 'manager':
                return {};
            case 'secretary':
                return { status: { $ne: 'voided' } };
            default:
                throw new AppError(403, 'Invalid role for payment access');
        }
    }

    /**
     * Formats a payment object into a standardized response DTO
     */
    private static formatPaymentResponse(payment: any): PaymentResponseDTO {
        if (!payment) {
            throw new AppError(500, 'Cannot format undefined payment');
        }

        try {
            return {
                id: payment._id.toString(),
                student: {
                    id: payment.studentId._id.toString(),
                    name: payment.studentId.name
                },
                amount: payment.amount,
                remainingBalance: payment.remainingBalance,
                status: payment.status,
                method: payment.method,
                date: payment.date,
                nextDueDate: payment.nextDueDate,
                description: payment.description,
                period: payment.period,
                recordedBy: {
                    id: payment.recordedBy._id.toString(),
                    username: payment.recordedBy.username
                },
                recordedAt: payment.recordedAt,
                modifiedBy: payment.modifiedBy ? {
                    id: payment.modifiedBy._id.toString(),
                    username: payment.modifiedBy.username
                } : undefined,
                modifiedAt: payment.modifiedAt,
                voidedBy: payment.voidedBy ? {
                    id: payment.voidedBy._id.toString(),
                    username: payment.voidedBy.username
                } : undefined,
                voidedAt: payment.voidedAt,
                voidReason: payment.voidReason,
                receiptNumber: payment.receiptNumber,
                notes: payment.notes,
                attachments: payment.attachments
            };
        } catch (error) {
            console.error('Error in formatPaymentResponse:', {
                error,
                paymentId: payment?._id?.toString(),
                paymentData: payment
            });
            throw new AppError(500, `Error formatting payment response: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    /**
     * Checks if a payment matches role-based access query
     */
    private static checkPaymentAgainstQuery(payment: any, query: FilterQuery<IPayment>): boolean {
        if (!query.status) return true;

        if (Array.isArray(query.status.$ne)) {
            return !query.status.$ne.includes(payment.status);
        }

        if (query.status.$ne) {
            return payment.status !== query.status.$ne;
        }

        return payment.status === query.status;
    }

    /**
     * Generates receipt content in specified format
     */
    private static generateReceiptContent(payment: any, format: 'pdf' | 'html'): Buffer {
        const receiptData = {
            receiptNumber: payment.receiptNumber,
            date: new Date(payment.date).toLocaleDateString(),
            studentName: payment.studentId.name,
            amount: payment.amount.toFixed(2),
            method: payment.method,
            description: payment.description,
            recordedBy: payment.recordedBy.username,
            notes: payment.notes || ''
        };

        if (format === 'html') {
            const htmlContent = `
                <!DOCTYPE html>
                <html>
                <head>
                    <title>Payment Receipt</title>
                    <style>
                        body { font-family: Arial, sans-serif; }
                        .receipt { max-width: 800px; margin: 0 auto; padding: 20px; }
                        .header { text-align: center; margin-bottom: 20px; }
                        .details { margin: 20px 0; }
                        .row { display: flex; justify-content: space-between; margin: 10px 0; }
                        .footer { margin-top: 40px; text-align: center; }
                    </style>
                </head>
                <body>
                    <div class="receipt">
                        <div class="header">
                            <h1>Payment Receipt</h1>
                            <p>Receipt #: ${receiptData.receiptNumber}</p>
                            <p>Date: ${receiptData.date}</p>
                        </div>
                        <div class="details">
                            <div class="row">
                                <span>Student Name:</span>
                                <span>${receiptData.studentName}</span>
                            </div>
                            <div class="row">
                                <span>Amount:</span>
                                <span>$${receiptData.amount}</span>
                            </div>
                            <div class="row">
                                <span>Payment Method:</span>
                                <span>${receiptData.method}</span>
                            </div>
                            <div class="row">
                                <span>Description:</span>
                                <span>${receiptData.description}</span>
                            </div>
                            ${receiptData.notes ? `
                            <div class="row">
                                <span>Notes:</span>
                                <span>${receiptData.notes}</span>
                            </div>` : ''}
                        </div>
                        <div class="footer">
                            <p>Recorded by: ${receiptData.recordedBy}</p>
                            <p>This is a computer-generated receipt.</p>
                        </div>
                    </div>
                </body>
                </html>
            `;
            return Buffer.from(htmlContent);
        }

        throw new AppError(501, 'PDF generation not implemented');
    }

    /**
     * Groups payments by specified field
     */
    private static groupPaymentsByField(payments: PaymentResponseDTO[], groupBy: string): PaymentGroupExport[] {
        const grouped = payments.reduce<Record<string, PaymentGroupExport>>((acc, payment) => {
            let key: string;
            switch (groupBy) {
                case 'student':
                    key = payment.student.name;
                    break;
                case 'date':
                    key = new Date(payment.date).toISOString().split('T')[0];
                    break;
                case 'method':
                    key = payment.method;
                    break;
                case 'period':
                    key = payment.period || 'No Period';
                    break;
                default:
                    key = 'Other';
            }

            if (!acc[key]) {
                acc[key] = {
                    group: key,
                    totalAmount: 0,
                    count: 0,
                    payments: []
                };
            }

            acc[key].payments.push(payment);
            acc[key].totalAmount += payment.amount;
            acc[key].count++;

            return acc;
        }, {});

        return Object.values(grouped).map(group => ({
            ...group,
            payments: group.payments.sort((a, b) =>
                new Date(b.date).getTime() - new Date(a.date).getTime()
            )
        }));
    }

    /**
     * Converts data to CSV format with proper value formatting
     */
    private static convertToCSV(data: any[], fields: string[]): string {
        const flattenObject = (obj: any, prefix = ''): any => {
            return Object.keys(obj).reduce((acc: any, key: string) => {
                const value = obj[key];
                const newPrefix = prefix ? `${prefix}_${key}` : key;

                if (value && typeof value === 'object' && !Array.isArray(value) && !(value instanceof Date)) {
                    Object.assign(acc, flattenObject(value, newPrefix));
                } else {
                    acc[newPrefix] = value;
                }

                return acc;
            }, {});
        };

        const flattenedData = data.map(item => flattenObject(item));
        const header = fields.join(',');
        const rows = flattenedData.map(item =>
            fields.map(field => {
                const value = field.includes('_') ?
                    field.split('_').reduce((obj, key) => obj?.[key], item) :
                    item[field];
                return this.formatCSVValue(value);
            }).join(',')
        );

        return [header, ...rows].join('\n');
    }

    /**
     * Formats a value for CSV output with proper escaping
     */
    private static formatCSVValue(value: any): string {
        if (value === null || value === undefined) return '';
        if (value instanceof Date) return value.toISOString();
        if (typeof value === 'object') return JSON.stringify(value);
        return `"${String(value).replace(/"/g, '""')}"`;
    }

    

    /**
     * Core CRUD Operations
     */

    /**
     * Get payments with filtering and pagination
     */
    static async getPayments(
        options: PaymentQueryOptions,
        requestingUserId: string,
        userRole: string
    ) {
        try {
            const {
                page = 1,
                limit = 10,
                sortBy = 'date',
                sortOrder = 'desc',
                status,
                method,
                studentId,
                dateRange,
                amountRange,
                dueDateRange,
                period,
                search
            } = options;

            const query: FilterQuery<IPayment> = {
                ...this.getRoleAccessQuery(userRole)
            };

            // Apply filters
            if (status) query.status = status;
            if (method) query.method = method;
            if (studentId) query.studentId = new mongoose.Types.ObjectId(studentId);
            if (period) query.period = period;

            // Date range filters
            if (dateRange) {
                query.date = {
                    $gte: dateRange.start,
                    $lte: dateRange.end
                };
            }

            // Amount range filter
            if (amountRange) {
                query.amount = {
                    $gte: amountRange.min,
                    $lte: amountRange.max
                };
            }

            // Due date range filter
            if (dueDateRange) {
                query.nextDueDate = {
                    $gte: dueDateRange.start,
                    $lte: dueDateRange.end
                };
            }

            // Search in description and notes
            if (search) {
                query.$or = [
                    { description: { $regex: search, $options: 'i' } },
                    { notes: { $regex: search, $options: 'i' } },
                    { receiptNumber: { $regex: search, $options: 'i' } }
                ];
            }

            const [payments, total] = await Promise.all([
                Payment.find(query)
                    .populate('studentId', 'name')
                    .populate('recordedBy', 'username')
                    .populate('modifiedBy', 'username')
                    .populate('voidedBy', 'username')
                    .sort({ [sortBy]: sortOrder })
                    .skip((page - 1) * limit)
                    .limit(limit)
                    .lean(),
                Payment.countDocuments(query)
            ]);

            await SystemLogger.log({
                severity: 'info',
                category: 'payments',
                action: 'list_payments',
                performedBy: requestingUserId,
                details: { filters: options },
                status: 'success',
                timestamp: new Date()
            });

            return {
                payments: payments.map(payment => this.formatPaymentResponse(payment)),
                pagination: {
                    total,
                    page,
                    limit,
                    pages: Math.ceil(total / limit)
                }
            };
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error fetching payments');
        }
    }

    /**
     * Get a single payment by ID
     */
    static async getPaymentById(
        id: string,
        requestingUserId: string,
        userRole: string
    ): Promise<PaymentResponseDTO> {
        try {
            const payment = await Payment.findById(id)
                .populate('studentId', 'name')
                .populate('recordedBy', 'username')
                .populate('modifiedBy', 'username')
                .populate('voidedBy', 'username')
                .lean();

            if (!payment) {
                throw new AppError(404, 'Payment not found');
            }

            // Role-based access check
            const roleQuery = this.getRoleAccessQuery(userRole);
            if (Object.keys(roleQuery).length > 0 && !this.checkPaymentAgainstQuery(payment, roleQuery)) {
                throw new AppError(403, 'Not authorized to access this payment');
            }

            await SystemLogger.log({
                severity: 'info',
                category: 'payments',
                action: 'view_payment',
                performedBy: requestingUserId,
                targetId: id,
                details: {  // Added this required property
                    paymentId: id,
                    viewedAt: new Date()
                },
                status: 'success',
                timestamp: new Date()
            });

            return this.formatPaymentResponse(payment);
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error fetching payment');
        }
    }

    /**
     * Create a new payment
     */
    static async createPayment(
        paymentData: CreatePaymentDTO,
        createdBy: string
    ): Promise<PaymentResponseDTO> {
        try {
            // Validate student exists
            const student = await Student.findById(paymentData.studentId);
            if (!student) {
                throw new AppError(404, 'Student not found');
            }

            // Create payment
            const payment = new Payment({
                ...paymentData,
                studentId: new mongoose.Types.ObjectId(paymentData.studentId),
                recordedBy: new mongoose.Types.ObjectId(createdBy),
                recordedAt: new Date(),
                status: 'completed' as PaymentStatus,
                modificationHistory: []
            });

            // Validate payment
            await payment.validatePayment();

            // Generate receipt number
            payment.receiptNumber = await Payment.generateUniqueReceiptNumber();

            await payment.save();

            const populatedPayment = await Payment.findById(payment._id)
                .populate('studentId', 'name')
                .populate('recordedBy', 'username')
                .lean();

            if (!populatedPayment) {
                throw new AppError(500, 'Error retrieving created payment');
            }

            // Update student's payment history
            await Student.findByIdAndUpdate(paymentData.studentId, {
                $push: {
                    payments: {
                        amount: payment.amount,
                        date: payment.date,
                        recordedBy: createdBy,
                        remainingBalance: payment.remainingBalance,
                        nextDueDate: payment.nextDueDate
                    }
                }
            });

            await SystemLogger.log({
                severity: 'info',
                category: 'payments',
                action: 'create_payment',
                performedBy: createdBy,
                targetId: payment._id.toString(),
                details: {
                    amount: payment.amount,
                    studentId: payment.studentId.toString()
                },
                status: 'success',
                timestamp: new Date()
            });

            return this.formatPaymentResponse(populatedPayment);
        } catch (error) {
            if (error instanceof mongoose.Error.ValidationError) {
                throw new AppError(400, `Validation error: ${Object.values(error.errors).map(e => e.message).join(', ')}`);
            }
            throw error instanceof AppError ? error : new AppError(500, 'Error creating payment');
        }
    }

    /**
     * Update an existing payment
     */
    static async updatePayment(
        id: string,
        updateData: UpdatePaymentDTO,
        updatedBy: string,
        userRole: string
    ): Promise<PaymentResponseDTO> {
        try {
            const payment = await Payment.findById(id);
            if (!payment) {
                throw new AppError(404, 'Payment not found');
            }

            // Check if payment can be modified
            if (payment.status === 'voided') {
                throw new AppError(400, 'Cannot modify a voided payment');
            }

            // Track changes for modification history
            const changes: Record<string, { oldValue: any; newValue: any }> = {};

            // Update allowed fields
            Object.entries(updateData).forEach(([field, value]) => {
                if (value !== undefined && field !== 'status') {  // Prevent status changes through update
                    changes[field] = {
                        oldValue: payment.get(field),
                        newValue: value
                    };
                    payment.set(field, value);
                }
            });

            // Add modification record
            payment.modifiedBy = new mongoose.Types.ObjectId(updatedBy);
            payment.modifiedAt = new Date();
            await payment.addModificationRecord(
                new mongoose.Types.ObjectId(updatedBy),
                changes
            );

            // Save and populate
            await payment.save();
            const updatedPayment = await Payment.findById(payment._id)
                .populate('studentId', 'name')
                .populate('recordedBy', 'username')
                .populate('modifiedBy', 'username')
                .lean();

            if (!updatedPayment) {
                throw new AppError(500, 'Error retrieving updated payment');
            }

            await SystemLogger.log({
                severity: 'info',
                category: 'payments',
                action: 'update_payment',
                performedBy: updatedBy,
                targetId: id,
                details: { changes },
                status: 'success',
                timestamp: new Date()
            });

            return this.formatPaymentResponse(updatedPayment);
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error updating payment');
        }
    }
    /**
     * Payment Processing Operations
     */

    /**
     * Void an existing payment
     */
    // static async voidPayment(
    //     id: string,
    //     voidData: VoidPaymentDTO,
    //     voidedBy: string,
    //     userRole: string
    // ): Promise<PaymentResponseDTO> {
    //     try {
    //         // Only superAdmin and manager can void payments
    //         if (!['superAdmin', 'manager'].includes(userRole)) {
    //             throw new AppError(403, 'Not authorized to void payments');
    //         }

    //         const payment = await Payment.findById(id);
    //         if (!payment) {
    //             throw new AppError(404, 'Payment not found');
    //         }

    //         // Check if already voided
    //         if (payment.status === 'voided') {
    //             throw new AppError(400, 'Payment is already voided');
    //         }

    //         await payment.voidPayment(
    //             new mongoose.Types.ObjectId(voidedBy),
    //             voidData.reason
    //         );

    //         const voidedPayment = await Payment.findById(payment._id)
    //             .populate('studentId', 'name')
    //             .populate('recordedBy', 'username')
    //             .populate('voidedBy', 'username')
    //             .lean();

    //         if (!voidedPayment) {
    //             throw new AppError(500, 'Error retrieving voided payment');
    //         }

    //         // Update student's payment records
    //         await Student.findByIdAndUpdate(payment.studentId, {
    //             $set: {
    //                 'payments.$[elem].status': 'voided'
    //             }
    //         }, {
    //             arrayFilters: [{ 'elem.date': payment.date }]
    //         });

    //         await SystemLogger.log({
    //             severity: 'info',
    //             category: 'payments',
    //             action: 'void_payment',
    //             performedBy: voidedBy,
    //             targetId: id,
    //             details: {
    //                 reason: voidData.reason,
    //                 originalAmount: payment.amount
    //             },
    //             status: 'success',
    //             timestamp: new Date()
    //         });

    //         return this.formatPaymentResponse(voidedPayment);
    //     } catch (error) {
    //         throw error instanceof AppError ? error : new AppError(500, 'Error voiding payment');
    //     }
    // }

    static async voidPayment(
        id: string,
        voidData: VoidPaymentDTO,
        voidedBy: string,
        userRole: string
    ): Promise<PaymentResponseDTO> {
        console.log('Starting voidPayment service method:', {
            id,
            voidData,
            userRole
        });
    
        try {
            if (!['superAdmin', 'manager'].includes(userRole)) {
                console.log('Unauthorized void attempt:', { userRole });
                throw new AppError(403, 'Not authorized to void payments');
            }
    
            const payment = await Payment.findById(id);
            console.log('Payment found:', {
                exists: !!payment,
                status: payment?.status
            });
    
            if (!payment) {
                throw new AppError(404, 'Payment not found');
            }
    
            if (payment.status === 'voided') {
                console.log('Payment already voided');
                throw new AppError(400, 'Payment is already voided');
            }
    
            try {
                console.log('Attempting to void payment');
                await payment.voidPayment(
                    new mongoose.Types.ObjectId(voidedBy),
                    voidData.reason
                );
                console.log('Payment voided successfully');
            } catch (voidError) {
                console.error('Error during void operation:', voidError);
                throw voidError;
            }
    
            const voidedPayment = await Payment.findById(payment._id)
                .populate('studentId', 'name')
                .populate('recordedBy', 'username')
                .populate('voidedBy', 'username')
                .lean();
    
            if (!voidedPayment) {
                throw new AppError(500, 'Error retrieving voided payment');
            }
    
            return this.formatPaymentResponse(voidedPayment);
        } catch (error) {
            console.error('Error in voidPayment service:', {
                error,
                stack: error instanceof Error ? error.stack : undefined,
                id,
                voidData
            });
            throw error instanceof AppError ? error : new AppError(500, 'Error voiding payment');
        }
    }
    /**
     * Student Payment History and Statistics
     */

    /**
     * Get complete payment history for a student
     */
    static async getStudentPaymentHistory(
        studentId: string,
        startDate: Date | undefined,
        endDate: Date | undefined,
        userRole: string
    ): Promise<StudentPaymentSummary> {
        try {
            const student = await Student.findById(studentId);
            if (!student) {
                throw new AppError(404, 'Student not found');
            }

            const payments = await Payment.getStudentPaymentHistory(
                new mongoose.Types.ObjectId(studentId),
                startDate,
                endDate
            );

            // Calculate payment summary
            const summary: StudentPaymentSummary = {
                totalPaid: 0,
                totalDue: 0,
                paymentStatus: 'up_to_date',
                recentPayments: [],
                upcomingPayments: []
            };

            // Process completed payments
            payments.forEach(payment => {
                if (payment.status === 'completed') {
                    summary.totalPaid += payment.amount;
                }
            });

            // Get recent and upcoming payments
            const now = new Date();
            summary.recentPayments = payments
                .filter(p => p.date <= now && p.status !== 'voided')
                .slice(0, 5)
                .map(p => this.formatPaymentResponse(p));

            const upcomingPayments = payments
                .filter(p => p.nextDueDate && p.nextDueDate > now && p.status !== 'voided')
                .sort((a, b) => a.nextDueDate!.getTime() - b.nextDueDate!.getTime());

            if (upcomingPayments.length > 0) {
                summary.nextDueDate = upcomingPayments[0].nextDueDate;
                summary.upcomingPayments = upcomingPayments
                    .slice(0, 5)
                    .map(p => ({
                        dueDate: p.nextDueDate!,
                        expectedAmount: p.remainingBalance
                    }));

                // Calculate total due
                summary.totalDue = upcomingPayments.reduce(
                    (total, payment) => total + payment.remainingBalance,
                    0
                );
            }

            // Determine payment status
            const hasOverdue = upcomingPayments.some(p =>
                p.nextDueDate && p.nextDueDate < now && p.remainingBalance > 0
            );

            summary.paymentStatus = hasOverdue ? 'overdue' :
                upcomingPayments.length > 0 ? 'pending' : 'up_to_date';

            return summary;
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error fetching payment history');
        }
    }

    static async getPaymentStatistics(
        options: {
            startDate?: Date;
            endDate?: Date;
            groupBy: 'daily' | 'monthly' | 'method' | 'status';
            includeVoided?: boolean; // New option
        },
        userRole: string
    ): Promise<PaymentStatistics> {
        try {
            console.log('Getting payment statistics with options:', {
                options,
                userRole
            });
    
            const query: FilterQuery<IPayment> = {
                ...this.getRoleAccessQuery(userRole)
            };
    
            // Only exclude voided payments if specifically requested
            if (!options.includeVoided) {
                query.status = { $ne: 'voided' };
            }
    
            // Apply date range filter
            if (options.startDate || options.endDate) {
                query.date = {};
                if (options.startDate) query.date.$gte = options.startDate;
                if (options.endDate) query.date.$lte = options.endDate;
            }
    
            console.log('Executing query:', JSON.stringify(query, null, 2));
    
            const payments = await Payment.find(query)
                .sort({ date: 1 })
                .lean();
    
            console.log(`Found ${payments.length} payments`);
    
            // If no payments found, return empty statistics
            if (!payments.length) {
                return {
                    totalAmount: 0,
                    totalCount: 0,
                    averageAmount: 0,
                    groups: [],
                    dateRange: {
                        start: options.startDate || new Date(),
                        end: options.endDate || new Date()
                    }
                };
            }
    
            // Calculate basic statistics
            const totalAmount = payments.reduce((sum, p) => sum + p.amount, 0);
            const totalCount = payments.length;
    
            let groups: Array<{
                key: string;
                count: number;
                amount: number;
                percentage: number;
            }> = [];
    
            // Group payments based on specified criteria
            switch (options.groupBy) {
                case 'daily':
                    groups = payments.reduce((acc: any[], payment) => {
                        const date = new Date(payment.date).toISOString().split('T')[0];
                        const existing = acc.find(g => g.key === date);
    
                        if (existing) {
                            existing.count++;
                            existing.amount += payment.amount;
                        } else {
                            acc.push({
                                key: date,
                                count: 1,
                                amount: payment.amount,
                                percentage: 0 // Will be calculated later
                            });
                        }
                        return acc;
                    }, []);
                    break;
    
                case 'monthly':
                    groups = payments.reduce((acc: any[], payment) => {
                        const date = new Date(payment.date);
                        const monthYear = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
                        const existing = acc.find(g => g.key === monthYear);
    
                        if (existing) {
                            existing.count++;
                            existing.amount += payment.amount;
                        } else {
                            acc.push({
                                key: monthYear,
                                count: 1,
                                amount: payment.amount,
                                percentage: 0 // Will be calculated later
                            });
                        }
                        return acc;
                    }, []);
                    break;
    
                case 'method':
                    groups = payments.reduce((acc: any[], payment) => {
                        const existing = acc.find(g => g.key === payment.method);
    
                        if (existing) {
                            existing.count++;
                            existing.amount += payment.amount;
                        } else {
                            acc.push({
                                key: payment.method,
                                count: 1,
                                amount: payment.amount,
                                percentage: 0 // Will be calculated later
                            });
                        }
                        return acc;
                    }, []);
                    break;
    
                case 'status':
                    groups = payments.reduce((acc: any[], payment) => {
                        const existing = acc.find(g => g.key === payment.status);
    
                        if (existing) {
                            existing.count++;
                            existing.amount += payment.amount;
                        } else {
                            acc.push({
                                key: payment.status,
                                count: 1,
                                amount: payment.amount,
                                percentage: 0 // Will be calculated later
                            });
                        }
                        return acc;
                    }, []);
                    break;
            }
    
            // Calculate percentages
            groups.forEach(group => {
                group.percentage = (group.amount / totalAmount) * 100;
            });
    
            // Sort groups
            if (['daily', 'monthly'].includes(options.groupBy)) {
                groups.sort((a, b) => a.key.localeCompare(b.key));
            } else {
                groups.sort((a, b) => b.amount - a.amount);
            }
    
            const result = {
                totalAmount,
                totalCount,
                averageAmount: totalAmount / totalCount,
                groups,
                dateRange: {
                    start: options.startDate || payments[0].date,
                    end: options.endDate || payments[payments.length - 1].date
                }
            };
    
            console.log('Returning statistics:', JSON.stringify(result, null, 2));
            return result;
    
        } catch (error) {
            console.error('Error in getPaymentStatistics:', error);
            throw error instanceof AppError 
                ? error 
                : new AppError(500, `Error calculating payment statistics: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    

    /**
     * Export and Receipt Generation
     */

    /**
     * Export payments data in specified format
     */
    static async exportPayments(
        options: PaymentExportOptions,
        exportedBy: string,
        userRole: string
    ): Promise<string> {
        try {
            const query: FilterQuery<IPayment> = {
                ...this.getRoleAccessQuery(userRole)
            };

            if (options.dateRange) {
                query.date = {
                    $gte: options.dateRange.start,
                    $lte: options.dateRange.end
                };
            }

            if (!options.includeVoided) {
                query.status = { $ne: 'voided' };
            }

            const payments = await Payment.find(query)
                .populate('studentId', 'name')
                .populate('recordedBy', 'username')
                .populate('modifiedBy', 'username')
                .populate('voidedBy', 'username')
                .sort({ date: -1 })
                .lean();

            // Use type assertion here since we know the structure
            let exportData: PaymentExportData = payments.map(payment =>
                this.formatPaymentResponse(payment)
            );

            // Group data if specified
            if (options.groupBy) {
                exportData = this.groupPaymentsByField(exportData as PaymentResponseDTO[], options.groupBy);
            }

            await SystemLogger.log({
                severity: 'info',
                category: 'payments',
                action: 'export_payments',
                performedBy: exportedBy,
                details: {
                    format: options.format,
                    recordCount: payments.length,
                    dateRange: options.dateRange
                },
                status: 'success',
                timestamp: new Date()
            });

            return options.format === 'json'
                ? JSON.stringify(exportData, null, 2)
                : this.convertToCSV(exportData, options.fields || Object.keys(exportData[0]));
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error exporting payments');
        }
    }
    /**
 * Get payment statistics grouped by different criteria
 */

    /**
     * Generate payment receipt
     */
    static async generateReceipt(
        paymentId: string,
        format: 'pdf' | 'html',
        requestingUserId: string
    ): Promise<{ receiptNumber: string; content: Buffer }> {
        try {
            const payment = await Payment.findById(paymentId)
                .populate('studentId', 'name')
                .populate('recordedBy', 'username')
                .lean();

            if (!payment) {
                throw new AppError(404, 'Payment not found');
            }

            // Check if payment is voided
            if (payment.status === 'voided') {
                throw new AppError(400, 'Cannot generate receipt for voided payment');
            }

            const receiptContent = this.generateReceiptContent(payment, format);

            await SystemLogger.log({
                severity: 'info',
                category: 'payments',
                action: 'generate_receipt',
                performedBy: requestingUserId,
                targetId: paymentId,
                details: {
                    format,
                    receiptNumber: payment.receiptNumber
                },
                status: 'success',
                timestamp: new Date()
            });

            return {
                receiptNumber: payment.receiptNumber,
                content: receiptContent
            };
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error generating receipt');
        }
    }



}