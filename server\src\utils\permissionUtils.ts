// New file: src/utils/permissionUtils.ts
import { Types } from 'mongoose';
import { AppError } from '../types/error.types';
import { NoteVisibility } from '../types/notes.types';

export type UserRole = 'superAdmin' | 'manager' | 'secretary' | 'teacher';

export const validateUserRole = (role: string): role is UserRole => {
  const validRoles: UserRole[] = ['superAdmin', 'manager', 'secretary', 'teacher'];
  return validRoles.includes(role as UserRole);
};

export const getVisibilityQuery = (userRole: string, userId: string) => {
  if (!validateUserRole(userRole)) {
    throw new AppError(403, `Invalid role: ${userRole}`);
  }

  switch (userRole) {
    case 'superAdmin':
      return {};
    case 'manager':
      return { visibility: { $in: ['all_staff', 'manager_only'] } };
    case 'secretary':
      return { visibility: 'all_staff' };
    case 'teacher':
      return {
        $or: [
          { visibility: 'all_staff' },
          { visibility: 'teacher_only', createdBy: new Types.ObjectId(userId) }
        ]
      };
    default:
      throw new AppError(403, 'Invalid role for note access');
  }
};

export const canAccessNote = (
  noteVisibility: NoteVisibility, 
  userRole: string, 
  isCreator: boolean
): boolean => {
  if (!validateUserRole(userRole)) {
    return false;
  }

  switch (noteVisibility) {
    case 'teacher_only':
      return isCreator || userRole === 'superAdmin';
    case 'manager_only':
      return ['superAdmin', 'manager'].includes(userRole);
    case 'all_staff':
      return true;
    default:
      return false;
  }
};

export const canCreateNoteWithVisibility = (
  userRole: string,
  visibility: NoteVisibility
): boolean => {
  if (!validateUserRole(userRole)) {
    return false;
  }

  switch (userRole) {
    case 'superAdmin':
    case 'manager':
      return true;
    case 'teacher':
      return ['teacher_only', 'all_staff'].includes(visibility);
    default:
      return false;
  }
};