// server/src/validations/room.validation.ts
import <PERSON><PERSON> from 'joi';
import { RoomStatus } from '../types/room.types';

// Reusable sub-schemas
const timeSchema = Joi.string()
    .pattern(/^([01]\d|2[0-3]):([0-5]\d)$/)
    .messages({
        'string.pattern.base': 'Time must be in HH:mm format (24-hour)'
    });

const dateSchema = Joi.date()
    .iso()
    .messages({
        'date.base': 'Invalid date format',
        'date.format': 'Date must be in ISO format'
    });

export const roomValidation = {
    // GET /api/rooms query parameters
    getRoomsQuery: Joi.object({
        page: Joi.number()
            .min(1)
            .messages({
                'number.min': 'Page number must be greater than 0'
            }),
        limit: Joi.number()
            .min(1)
            .max(100)
            .messages({
                'number.min': 'Limit must be greater than 0',
                'number.max': 'Limit cannot exceed 100'
            }),
        sortBy: Joi.string()
            .valid('name', 'capacity', 'building', 'floor', 'status')
            .messages({
                'any.only': 'Invalid sort field'
            }),
        sortOrder: Joi.string()
            .valid('asc', 'desc')
            .messages({
                'any.only': 'Sort order must be either asc or desc'
            }),
        status: Joi.string()
            .valid('active', 'maintenance', 'inactive')
            .messages({
                'any.only': 'Invalid room status'
            }),
        building: Joi.string()
            .trim()
            .messages({
                'string.empty': 'Building cannot be empty'
            }),
        floor: Joi.number()
            .integer()
            .min(0)
            .messages({
                'number.base': 'Floor must be a number',
                'number.integer': 'Floor must be an integer',
                'number.min': 'Floor cannot be negative'
            }),
        minCapacity: Joi.number()
            .integer()
            .min(1)
            .messages({
                'number.base': 'Minimum capacity must be a number',
                'number.integer': 'Minimum capacity must be an integer',
                'number.min': 'Minimum capacity must be at least 1'
            }),
        features: Joi.string()
            .pattern(/^[a-zA-Z_]+(,[a-zA-Z_]+)*$/)
            .messages({
                'string.pattern.base': 'Invalid features format. Use comma-separated values'
            }),
        search: Joi.string()
            .min(2)
            .max(50)
            .messages({
                'string.min': 'Search term must be at least 2 characters long',
                'string.max': 'Search term cannot exceed 50 characters'
            }),
        date: dateSchema,
        timeStart: timeSchema,
        timeEnd: timeSchema.when('timeStart', {
            is: Joi.exist(),
            then: Joi.required(),
            otherwise: Joi.forbidden()
        })
    }),

    // POST /api/rooms
    createRoom: Joi.object({
        name: Joi.string()
            .required()
            .trim()
            .min(2)
            .max(50)
            .pattern(/^[a-zA-Z0-9\s-_]+$/)
            .messages({
                'string.empty': 'Room name is required',
                'string.min': 'Room name must be at least 2 characters long',
                'string.max': 'Room name cannot exceed 50 characters',
                'string.pattern.base': 'Room name can only contain letters, numbers, spaces, hyphens and underscores',
                'any.required': 'Room name is required'
            }),
        capacity: Joi.number()
            .required()
            .integer()
            .min(1)
            .max(500)
            .messages({
                'number.base': 'Capacity must be a number',
                'number.integer': 'Capacity must be an integer',
                'number.min': 'Capacity must be at least 1',
                'number.max': 'Capacity cannot exceed 500',
                'any.required': 'Capacity is required'
            }),
        building: Joi.string()
            .required()
            .trim()
            .min(1)
            .max(100)
            .messages({
                'string.empty': 'Building name is required',
                'string.min': 'Building name must be at least 1 character long',
                'string.max': 'Building name cannot exceed 100 characters',
                'any.required': 'Building name is required'
            }),
        floor: Joi.number()
            .required()
            .integer()
            .min(0)
            .messages({
                'number.base': 'Floor must be a number',
                'number.integer': 'Floor must be an integer',
                'number.min': 'Floor cannot be negative',
                'any.required': 'Floor is required'
            }),
        features: Joi.array()
            .items(
                Joi.string()
                    .trim()
                    .min(2)
                    .max(50)
                    .pattern(/^[a-zA-Z0-9\s-_]+$/)
                    .messages({
                        'string.min': 'Feature name must be at least 2 characters long',
                        'string.max': 'Feature name cannot exceed 50 characters',
                        'string.pattern.base': 'Feature name can only contain letters, numbers, spaces, hyphens and underscores'
                    })
            )
            .unique()
            .max(20)
            .messages({
                'array.unique': 'Duplicate features are not allowed',
                'array.max': 'Cannot exceed 20 features per room'
            })
    }),

    // PATCH /api/rooms/:id
    updateRoom: Joi.object({
        name: Joi.string()
            .trim()
            .min(2)
            .max(50)
            .pattern(/^[a-zA-Z0-9\s-_]+$/),
        capacity: Joi.number()
            .integer()
            .min(1)
            .max(500),
        building: Joi.string()
            .trim()
            .min(1)
            .max(100),
        floor: Joi.number()
            .integer()
            .min(0),
        features: Joi.array()
            .items(
                Joi.string()
                    .trim()
                    .min(2)
                    .max(50)
                    .pattern(/^[a-zA-Z0-9\s-_]+$/)
            )
            .unique()
            .max(20),
        status: Joi.string()
            .valid('active', 'maintenance', 'inactive')
    }).min(1).messages({
        'object.min': 'At least one field must be provided for update'
    }),

    // POST /api/rooms/:id/maintenance
    scheduleMaintenance: Joi.object({
        startDate: dateSchema
            .required()
            .min('now')
            .messages({
                'date.min': 'Maintenance start date must be in the future',
                'any.required': 'Start date is required'
            }),
        endDate: dateSchema
            .required()
            .min(Joi.ref('startDate'))
            .messages({
                'date.min': 'End date must be after start date',
                'any.required': 'End date is required'
            }),
        reason: Joi.string()
            .required()
            .trim()
            .min(10)
            .max(500)
            .messages({
                'string.empty': 'Maintenance reason is required',
                'string.min': 'Reason must be at least 10 characters long',
                'string.max': 'Reason cannot exceed 500 characters',
                'any.required': 'Reason is required'
            })
    }),

    // GET /api/rooms/export
    exportRooms: Joi.object({
        format: Joi.string()
            .valid('csv', 'json')
            .default('csv')
            .messages({
                'any.only': 'Export format must be either csv or json'
            }),
        startDate: Joi.date().iso(),
        endDate: Joi.date()
            .iso()
            .when('startDate', {
                is: Joi.exist(),
                then: Joi.date()
                    .iso()
                    .required()
                    .greater(Joi.ref('startDate'))
                    .messages({
                        'date.greater': 'End date must be after start date'
                    }),
                otherwise: Joi.optional()
            }),
        includeSchedule: Joi.boolean(),
        includeMaintenance: Joi.boolean(),
        fields: Joi.string()
            .pattern(/^[a-zA-Z_]+(,[a-zA-Z_]+)*$/)
            .messages({
                'string.pattern.base': 'Invalid fields format. Use comma-separated field names'
            })
    })
};

export default roomValidation;