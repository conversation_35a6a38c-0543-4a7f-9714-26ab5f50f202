
import { useEffect, useState } from "react";
import { 
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>, 
  Pie, 
  Cell, 
  Tooltip, 
  <PERSON>Chart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  Bar
} from 'recharts';
import { getPaymentStatistics } from "@/services/dashboardService";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Download, RefreshCw } from "lucide-react";
import DashboardSection from "./DashboardSection";
import { Skeleton } from "@/components/ui/skeleton";
import { toast } from "sonner";

interface PaymentStatisticsWidgetProps {
  fromDate?: string;
  toDate?: string;
  refreshInterval?: number; // in milliseconds
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];

const PaymentStatisticsWidget = ({ 
  fromDate, 
  toDate,
  refreshInterval = 0 // 0 means no auto-refresh
}: PaymentStatisticsWidgetProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("overview");
  const [paymentStats, setPaymentStats] = useState<any>(null);
  const [methodChartData, setMethodChartData] = useState<any[]>([]);
  const [periodChartData, setPeriodChartData] = useState<any[]>([]);
  
  const fetchPaymentStats = async () => {
    setIsLoading(true);
    try {
      const stats = await getPaymentStatistics({
        startDate: fromDate,
        endDate: toDate,
        groupBy: 'monthly'
      });
      
      setPaymentStats(stats);
      
      // Format data for method chart
      if (stats?.revenueByMethod) {
        setMethodChartData(stats.revenueByMethod.map((item: any) => ({
          name: item.method,
          value: item.amount,
          percentage: item.percentage
        })));
      }
      
      // Format data for period chart
      if (stats?.revenueByPeriod) {
        setPeriodChartData(stats.revenueByPeriod.map((item: any) => ({
          name: item.period,
          amount: item.amount,
          count: item.count
        })));
      }
      
    } catch (error) {
      console.error("Error fetching payment statistics:", error);
      toast.error("Failed to load payment statistics");
    } finally {
      setIsLoading(false);
    }
  };
  
  useEffect(() => {
    fetchPaymentStats();
    
    // Set up auto-refresh if interval is provided
    let intervalId: number | undefined;
    if (refreshInterval > 0) {
      intervalId = window.setInterval(fetchPaymentStats, refreshInterval);
    }
    
    return () => {
      if (intervalId) clearInterval(intervalId);
    };
  }, [fromDate, toDate, refreshInterval]);
  
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };
  
  const exportData = (type: string) => {
    let data;
    let filename;
    
    switch (type) {
      case 'methods':
        data = methodChartData;
        filename = 'payment-methods';
        break;
      case 'periods':
        data = periodChartData;
        filename = 'payment-periods';
        break;
      default:
        data = paymentStats?.recentPayments || [];
        filename = 'recent-payments';
    }
    
    // Create CSV
    const headers = Object.keys(data[0] || {}).join(',');
    const csvRows = data.map((row: any) => 
      Object.values(row).join(',')
    );
    const csvContent = `${headers}\n${csvRows.join('\n')}`;
    
    // Create download
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}-${new Date().toISOString().split('T')[0]}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };
  
  const renderMethodsChart = () => {
    if (isLoading) return <Skeleton className="h-64 w-full" />;
    if (!methodChartData.length) return <div className="text-center py-10 text-muted-foreground">No payment method data available</div>;
    
    return (
      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={methodChartData}
              cx="50%"
              cy="50%"
              labelLine={false}
              outerRadius={80}
              fill="#8884d8"
              dataKey="value"
              nameKey="name"
              label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
            >
              {methodChartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
              ))}
            </Pie>
            <Tooltip 
              formatter={(value) => formatCurrency(value as number)}
              contentStyle={{
                backgroundColor: 'rgba(255, 255, 255, 0.9)',
                borderRadius: '8px',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                border: '1px solid #eaeaea',
              }}
            />
          </PieChart>
        </ResponsiveContainer>
      </div>
    );
  };
  
  const renderPeriodChart = () => {
    if (isLoading) return <Skeleton className="h-64 w-full" />;
    if (!periodChartData.length) return <div className="text-center py-10 text-muted-foreground">No payment period data available</div>;
    
    return (
      <div className="h-64">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart data={periodChartData}>
            <CartesianGrid strokeDasharray="3 3" vertical={false} opacity={0.3} />
            <XAxis 
              dataKey="name"
              axisLine={false}
              tickLine={false}
              tick={{ fontSize: 12 }}
            />
            <YAxis 
              axisLine={false}
              tickLine={false} 
              tick={{ fontSize: 12 }}
              tickFormatter={(value) => formatCurrency(value)}
            />
            <Tooltip 
              formatter={(value, name) => [formatCurrency(value as number), name === 'amount' ? 'Revenue' : 'Transactions']}
              contentStyle={{
                backgroundColor: 'rgba(255, 255, 255, 0.9)',
                borderRadius: '8px',
                boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                border: '1px solid #eaeaea',
              }}
            />
            <Legend />
            <Bar dataKey="amount" name="Revenue" fill="#8884d8" radius={[4, 4, 0, 0]} />
            <Bar dataKey="count" name="Transactions" fill="#82ca9d" radius={[4, 4, 0, 0]} />
          </BarChart>
        </ResponsiveContainer>
      </div>
    );
  };
  
  const renderRecentPayments = () => {
    if (isLoading) return (
      <div className="space-y-2">
        {Array.from({ length: 5 }).map((_, i) => (
          <div key={i} className="flex justify-between py-2 border-b">
            <Skeleton className="h-5 w-36" />
            <Skeleton className="h-5 w-24" />
          </div>
        ))}
      </div>
    );
    
    if (!paymentStats?.recentPayments?.length) {
      return <div className="text-center py-10 text-muted-foreground">No recent payments available</div>;
    }
    
    return (
      <div className="space-y-1">
        {paymentStats.recentPayments.map((payment: any) => (
          <div key={payment.id} className="flex justify-between py-2 border-b last:border-0">
            <div>
              <p className="font-medium">{payment.studentName}</p>
              <p className="text-xs text-muted-foreground">{new Date(payment.date).toLocaleDateString()}</p>
            </div>
            <div className="text-right">
              <p className="font-medium">{formatCurrency(payment.amount)}</p>
              <p className="text-xs text-muted-foreground">{payment.method}</p>
            </div>
          </div>
        ))}
      </div>
    );
  };
  
  return (
    <DashboardSection 
      title="Payment Statistics" 
      description="Financial overview and payment trends"
      isLoading={false}
      actions={
        <Button size="sm" variant="outline" onClick={fetchPaymentStats}>
          <RefreshCw className="h-4 w-4 mr-1" />
          Refresh
        </Button>
      }
    >
      {/* Payment Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <div className="p-4 rounded-lg border bg-card text-card-foreground shadow-sm">
          <h3 className="text-sm font-medium text-muted-foreground">Total Revenue</h3>
          {isLoading ? (
            <Skeleton className="h-8 w-28 mt-1" />
          ) : (
            <p className="text-2xl font-bold mt-1">{formatCurrency(paymentStats?.totalRevenue || 0)}</p>
          )}
        </div>
        <div className="p-4 rounded-lg border bg-card text-card-foreground shadow-sm">
          <h3 className="text-sm font-medium text-muted-foreground">Pending Payments</h3>
          {isLoading ? (
            <Skeleton className="h-8 w-28 mt-1" />
          ) : (
            <p className="text-2xl font-bold mt-1">{formatCurrency(paymentStats?.pendingPayments || 0)}</p>
          )}
        </div>
        <div className="p-4 rounded-lg border bg-card text-card-foreground shadow-sm">
          <h3 className="text-sm font-medium text-muted-foreground">Overdue Payments</h3>
          {isLoading ? (
            <Skeleton className="h-8 w-28 mt-1" />
          ) : (
            <p className="text-2xl font-bold mt-1">{formatCurrency(paymentStats?.overduePayments || 0)}</p>
          )}
        </div>
        <div className="p-4 rounded-lg border bg-card text-card-foreground shadow-sm">
          <h3 className="text-sm font-medium text-muted-foreground">Average Payment</h3>
          {isLoading ? (
            <Skeleton className="h-8 w-28 mt-1" />
          ) : (
            <p className="text-2xl font-bold mt-1">
              {formatCurrency(
                paymentStats?.totalRevenue && paymentStats?.totalCount
                  ? paymentStats.totalRevenue / paymentStats.totalCount
                  : 0
              )}
            </p>
          )}
        </div>
      </div>
      
      {/* Tabs for Different Views */}
      <Tabs defaultValue="methods" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="methods">Payment Methods</TabsTrigger>
          <TabsTrigger value="trends">Revenue Trends</TabsTrigger>
          <TabsTrigger value="recent">Recent Payments</TabsTrigger>
        </TabsList>
        
        <TabsContent value="methods" className="space-y-4">
          <div className="flex justify-between">
            <h3 className="text-sm font-medium">Payment Method Distribution</h3>
            <Button size="sm" variant="outline" onClick={() => exportData('methods')}>
              <Download className="h-4 w-4 mr-1" />
              Export
            </Button>
          </div>
          {renderMethodsChart()}
        </TabsContent>
        
        <TabsContent value="trends" className="space-y-4">
          <div className="flex justify-between">
            <h3 className="text-sm font-medium">Revenue by Period</h3>
            <Button size="sm" variant="outline" onClick={() => exportData('periods')}>
              <Download className="h-4 w-4 mr-1" />
              Export
            </Button>
          </div>
          {renderPeriodChart()}
        </TabsContent>
        
        <TabsContent value="recent" className="space-y-4">
          <div className="flex justify-between">
            <h3 className="text-sm font-medium">Recent Transactions</h3>
            <Button size="sm" variant="outline" onClick={() => exportData('recent')}>
              <Download className="h-4 w-4 mr-1" />
              Export
            </Button>
          </div>
          {renderRecentPayments()}
        </TabsContent>
      </Tabs>
    </DashboardSection>
  );
};

export default PaymentStatisticsWidget;
