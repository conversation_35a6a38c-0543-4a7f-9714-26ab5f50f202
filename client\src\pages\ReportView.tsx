
import { useState, useEffect } from "react";
import { useNavigate, useParams } from "react-router-dom";
import MainLayout from "@/components/layout/MainLayout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ChevronLeft, Download, Printer, Share2 } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { formatDistance } from "date-fns";
import { Separator } from "@/components/ui/separator";
import { ReportResponse, SavedReport } from "@/types/reports";
import { getSavedReports } from "@/services/reportService";

export default function ReportView() {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const { toast } = useToast();
  
  const [report, setReport] = useState<ReportResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadReport = async () => {
      try {
        setIsLoading(true);
        
        // In a real implementation, this would fetch the specific report by ID
        // For now, we'll simulate this with our mock data
        const savedReports = await getSavedReports();
        
        // Mock a report response based on saved report data
        const savedReport = savedReports.find(r => r.id === id);
        
        if (savedReport) {
          const mockReport: ReportResponse = {
            id: savedReport.id,
            name: savedReport.name,
            reportType: savedReport.type,
            format: savedReport.format,
            url: savedReport.url || "/reports/sample.pdf",
            createdAt: savedReport.createdAt,
            generatedAt: savedReport.generatedAt || savedReport.createdAt,
            generatedBy: "Current User",
            parameters: {},
            status: "completed"
          };
          
          setReport(mockReport);
        } else {
          // If we can't find a matching saved report, create a mock one
          setReport({
            id: id || "unknown",
            name: "Sample Report",
            reportType: "class_attendance",
            format: "pdf",
            url: "/reports/sample.pdf",
            createdAt: new Date().toISOString(),
            generatedAt: new Date().toISOString(),
            generatedBy: "Current User",
            parameters: {
              dateRange: {
                startDate: "2023-09-01",
                endDate: "2023-09-30"
              }
            },
            status: "completed"
          });
        }
      } catch (error) {
        console.error("Error loading report:", error);
        toast({
          title: "Error loading report",
          description: "Failed to load report details. Please try again.",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    loadReport();
  }, [id, toast]);

  const handleDownload = () => {
    if (report?.url) {
      window.open(report.url, '_blank');
      
      toast({
        title: "Download started",
        description: "Your report is being downloaded.",
      });
    }
  };

  const handlePrint = () => {
    toast({
      title: "Print functionality",
      description: "Print functionality will be implemented here.",
    });
  };

  const handleShare = () => {
    toast({
      title: "Share functionality",
      description: "Share functionality will be implemented here.",
    });
  };

  if (isLoading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
        </div>
      </MainLayout>
    );
  }

  if (!report) {
    return (
      <MainLayout>
        <div className="flex flex-col items-center justify-center min-h-[60vh]">
          <p className="text-xl text-muted-foreground mb-4">
            Report not found
          </p>
          <Button onClick={() => navigate("/reports")}>
            Return to Reports
          </Button>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header with navigation */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate("/reports")}
            >
              <ChevronLeft size={18} />
            </Button>
            <h1 className="text-2xl font-bold">{report.name}</h1>
          </div>
          
          <div className="flex items-center gap-3">
            <Button 
              variant="outline" 
              onClick={handleShare}
            >
              <Share2 className="h-4 w-4 mr-2" />
              Share
            </Button>
            <Button 
              variant="outline" 
              onClick={handlePrint}
            >
              <Printer className="h-4 w-4 mr-2" />
              Print
            </Button>
            <Button onClick={handleDownload}>
              <Download className="h-4 w-4 mr-2" />
              Download
            </Button>
          </div>
        </div>

        {/* Report information */}
        <div className="grid gap-6 md:grid-cols-3">
          <div className="md:col-span-2 space-y-6">
            {/* Report viewer */}
            <Card className="min-h-[500px]">
              <CardHeader>
                <CardTitle>Report Preview</CardTitle>
              </CardHeader>
              <CardContent>
                {report.format === 'pdf' ? (
                  <div className="flex items-center justify-center h-[400px] border rounded-md bg-muted/50">
                    <div className="text-center">
                      <p className="mb-4">PDF Preview</p>
                      <p className="text-sm text-muted-foreground">
                        PDF preview is not available in this demo. Click Download to view the report.
                      </p>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-[400px] border rounded-md bg-muted/50">
                    <div className="text-center">
                      <p className="mb-4">{report.format.toUpperCase()} Preview</p>
                      <p className="text-sm text-muted-foreground">
                        Preview is not available in this demo. Click Download to view the report.
                      </p>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            {/* Report details */}
            <Card>
              <CardHeader>
                <CardTitle>Report Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="text-sm text-muted-foreground">Report Type</div>
                  <div className="font-medium">
                    {report.reportType?.split('_').map(word => 
                      word.charAt(0).toUpperCase() + word.slice(1)
                    ).join(' ') || "Unknown Report Type"}
                  </div>
                </div>

                <div>
                  <div className="text-sm text-muted-foreground">Format</div>
                  <div className="font-medium uppercase">{report.format}</div>
                </div>

                <div>
                  <div className="text-sm text-muted-foreground">Generated</div>
                  <div className="font-medium">
                    {new Date(report.generatedAt || report.createdAt).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </div>
                  <div className="text-xs text-muted-foreground">
                    ({formatDistance(new Date(report.generatedAt || report.createdAt), new Date(), { addSuffix: true })})
                  </div>
                </div>

                <div>
                  <div className="text-sm text-muted-foreground">Generated By</div>
                  <div className="font-medium">{report.generatedBy || "Unknown"}</div>
                </div>

                <Separator />

                {report.parameters && (
                  <div>
                    <div className="text-sm font-medium mb-2">Parameters</div>
                    <div className="space-y-2">
                      {report.parameters.dateRange && (
                        <div>
                          <div className="text-xs text-muted-foreground">Date Range</div>
                          <div className="text-sm">
                            {new Date(report.parameters.dateRange.startDate).toLocaleDateString()} 
                            {' - '} 
                            {new Date(report.parameters.dateRange.endDate).toLocaleDateString()}
                          </div>
                        </div>
                      )}
                      
                      {report.parameters.classId && (
                        <div>
                          <div className="text-xs text-muted-foreground">Class</div>
                          <div className="text-sm">Class ID: {report.parameters.classId}</div>
                        </div>
                      )}
                      
                      {report.parameters.studentId && (
                        <div>
                          <div className="text-xs text-muted-foreground">Student</div>
                          <div className="text-sm">Student ID: {report.parameters.studentId}</div>
                        </div>
                      )}
                      
                      {/* Display other parameters */}
                      {Object.entries(report.parameters).map(([key, value]) => {
                        // Skip parameters we've already displayed
                        if (key === 'dateRange' || key === 'classId' || key === 'studentId') {
                          return null;
                        }
                        
                        return (
                          <div key={key}>
                            <div className="text-xs text-muted-foreground">
                              {key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1')}
                            </div>
                            <div className="text-sm">
                              {typeof value === 'object' 
                                ? JSON.stringify(value) 
                                : value.toString()}
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}
