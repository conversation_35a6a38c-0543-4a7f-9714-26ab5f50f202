// server/src/routes/report.routes.ts
import express from 'express';
import { ReportController } from '../controllers/teacher.report.controller';
import { authenticateToken, authorizeRoles } from '../middleware/auth.middleware';
import { validate } from '../middleware/validate.middleware';
import { reportValidation } from '../validations/teacher.report.validation';
import { catchAsync } from '../middleware/error.middleware';
import { apiLimiter } from '../middleware/rateLimiter.middleware';

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Report Generation Routes
router.post(
    '/generate',
    authorizeRoles('superAdmin', 'manager', 'teacher'),
    validate(reportValidation.generateTeacherReport),
    apiLimiter,
    catchAsync(ReportController.generateReport)
);

// Preview report before generation
router.post(
    '/preview',
    authorizeRoles('superAdmin', 'manager', 'teacher'),
    validate(reportValidation.generateTeacherReport),
    apiLimiter,
    catchAsync(ReportController.previewReport)
);

export default router;