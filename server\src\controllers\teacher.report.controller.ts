// server/src/controllers/teacher.report.controller.ts
import { NextFunction, Request, Response } from 'express';
import { ReportService } from '../services/teacher.report.service';
import { AppError } from '../types/error.types';
import { SystemLogger } from '../services/logger.service';
import { TeacherReportRequestDTO } from '../types/teacher.report.types';

export class ReportController {
    static async generateReport(req: Request, res: Response, next: NextFunction) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        try {
            // Parse and validate the request
            const reportRequest: TeacherReportRequestDTO = {
                ...req.body,
                dateRange: {
                    startDate: new Date(req.body.dateRange.startDate),
                    endDate: new Date(req.body.dateRange.endDate)
                }
            };

            // Generate the report
            const { data, contentType, filename } = await ReportService.generateTeacherReport(
                reportRequest,
                currentUser._id.toString()
            );

            // Log successful report generation
            await SystemLogger.log({
                severity: 'info',
                category: 'reports',
                action: 'generate_report',
                performedBy: currentUser._id.toString(),
                details: {
                    reportType: reportRequest.type,
                    format: reportRequest.format,
                    dateRange: reportRequest.dateRange
                },
                status: 'success',
                timestamp: new Date()
            });

            // Set response headers
            res.setHeader('Content-Type', contentType);
            res.setHeader('Content-Disposition', `attachment; filename=${filename}`);
            
            // Send the response
            if (data instanceof Buffer || typeof data === 'string') {
                res.send(data);
            } else {
                res.json(data);
            }

        } catch (error) {
            // Log error
            await SystemLogger.log({
                severity: 'error',
                category: 'reports',
                action: 'generate_report',
                performedBy: currentUser._id.toString(),
                details: {
                    error: error instanceof Error ? error.message : 'Unknown error',
                    reportRequest: req.body
                },
                status: 'failed',
                timestamp: new Date()
            });

            next(error);
        }
    }

    static async previewReport(req: Request, res: Response, next: NextFunction) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }
    
        try {
            const reportRequest: TeacherReportRequestDTO = {
                ...req.body,
                dateRange: {
                    startDate: new Date(req.body.dateRange.startDate),
                    endDate: new Date(req.body.dateRange.endDate)
                },
                format: 'json' // Force JSON format for preview
            };
    
            // Use the new public method
            const reportData = await ReportService.generateReportPreview(
                reportRequest,
                currentUser._id.toString()
            );
    
            res.json({
                success: true,
                data: reportData
            });
    
        } catch (error) {
            await SystemLogger.log({
                severity: 'error',
                category: 'reports',
                action: 'preview_report',
                performedBy: currentUser._id.toString(),
                details: {
                    error: error instanceof Error ? error.message : 'Unknown error',
                    reportRequest: req.body
                },
                status: 'failed',
                timestamp: new Date()
            });
    
            next(error);
        }
    }
}