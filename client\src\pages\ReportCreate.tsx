
import { useState, useEffect } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import MainLayout from "@/components/layout/MainLayout";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ChevronLeft } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";

import { 
  getReportOptions, 
  previewReport, 
  generateReport,
  getReportTemplates 
} from "@/services/reportService";

import { 
  ReportOption, 
  ReportGenerationRequest, 
  ReportFormat,
  ReportTemplate,
  ReportPreviewData,
  ReportType
} from "@/types/reports";
import ReportFormFields from "@/components/reports/ReportFormFields";
import ReportPreviewPanel from "@/components/reports/ReportPreviewPanel";

export default function ReportCreate() {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { toast } = useToast();
  
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isPreviewLoading, setIsPreviewLoading] = useState(false);
  const [activeTab, setActiveTab] = useState("configure");
  
  const [reportOptions, setReportOptions] = useState<ReportOption[]>([]);
  const [templates, setTemplates] = useState<ReportTemplate[]>([]);
  const [selectedReport, setSelectedReport] = useState<ReportOption | null>(null);
  const [previewData, setPreviewData] = useState<ReportPreviewData | null>(null);
  
  // Form state
  const [formData, setFormData] = useState<ReportGenerationRequest>({
    reportType: "class_attendance", // Initialize with a valid reportType
    format: "pdf",
    dateRange: {
      startDate: new Date(new Date().setDate(new Date().getDate() - 30)).toISOString().split('T')[0],
      endDate: new Date().toISOString().split('T')[0],
    },
    filters: {}
  });

  // Load report options and templates
  useEffect(() => {
    const initPage = async () => {
      try {
        setIsLoading(true);
        
        // Get report options
        const options = await getReportOptions("superadmin");
        setReportOptions(options);
        
        // Get report templates
        const templateList = await getReportTemplates();
        setTemplates(templateList);
        
        // Check if template ID is specified in URL
        const templateId = searchParams.get('template');
        if (templateId) {
          const template = templateList.find(t => t.id === templateId);
          if (template) {
            setFormData({
              reportType: template.reportType,
              format: template.format,
              dateRange: {
                startDate: new Date(new Date().setDate(new Date().getDate() - 30)).toISOString().split('T')[0],
                endDate: new Date().toISOString().split('T')[0],
              },
              filters: { ...template.parameters }
            });
            
            // Find and set the selected report
            const report = options.find(opt => opt.type === template.reportType);
            if (report) {
              setSelectedReport(report);
            }
            
            toast({
              title: "Template loaded",
              description: `Loaded template: ${template.name}`,
            });
          }
        } else {
          // Check if report type is specified in URL
          const reportId = searchParams.get('type');
          if (reportId) {
            const report = options.find(opt => opt.id === reportId);
            if (report) {
              setSelectedReport(report);
              setFormData(prev => ({
                ...prev,
                reportType: report.type || "",
              }));
            }
          }
        }
      } catch (error) {
        console.error("Error initializing report page:", error);
        toast({
          title: "Error loading data",
          description: "Failed to load report options. Please try again.",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };
    
    initPage();
  }, [searchParams, toast]);

  // Handle form field changes
  const handleFormChange = (field: string, value: any) => {
    setFormData(prev => {
      if (field.startsWith('filter.')) {
        const filterKey = field.replace('filter.', '');
        return {
          ...prev,
          filters: {
            ...prev.filters,
            [filterKey]: value
          }
        };
      }
      
      if (field === 'format') {
        return {
          ...prev,
          format: value as ReportFormat
        };
      }
      
      if (field === 'startDate' || field === 'endDate') {
        return {
          ...prev,
          dateRange: {
            ...prev.dateRange,
            [field]: value
          }
        };
      }
      
      return {
        ...prev,
        [field]: value
      };
    });
  };

  // Load a template
  const handleLoadTemplate = (templateId: string) => {
    const template = templates.find(t => t.id === templateId);
    if (template) {
      setFormData({
        reportType: template.reportType,
        format: template.format,
        dateRange: {
          startDate: new Date(new Date().setDate(new Date().getDate() - 30)).toISOString().split('T')[0],
          endDate: new Date().toISOString().split('T')[0],
        },
        filters: { ...template.parameters }
      });
      
      // Find and set the selected report
      const report = reportOptions.find(opt => opt.type === template.reportType);
      if (report) {
        setSelectedReport(report);
      }
      
      toast({
        title: "Template loaded",
        description: `Loaded template: ${template.name}`,
      });
    }
  };

  // Generate report preview
  const handlePreview = async () => {
    if (!selectedReport) return;
    
    try {
      setIsPreviewLoading(true);
      // Updated to pass the formData as a single parameter
      const preview = await previewReport(formData);
      setPreviewData(preview);
      setActiveTab("preview");
    } catch (error) {
      console.error("Error generating preview:", error);
      toast({
        title: "Preview error",
        description: "Failed to generate report preview. Please check your parameters and try again.",
        variant: "destructive"
      });
    } finally {
      setIsPreviewLoading(false);
    }
  };

  // Generate final report
  const handleGenerate = async () => {
    if (!selectedReport) return;
    
    try {
      setIsSubmitting(true);
      const result = await generateReport(formData);
      
      toast({
        title: "Report generated",
        description: "Your report has been successfully generated.",
      });
      
      // Navigate to the report view page
      navigate(`/reports/view/${result.id}`);
    } catch (error) {
      console.error("Error generating report:", error);
      toast({
        title: "Generation error",
        description: "Failed to generate report. Please try again.",
        variant: "destructive"
      });
      setIsSubmitting(false);
    }
  };

  // Handle template saving (this would open a dialog in a complete implementation)
  const handleSaveTemplate = () => {
    toast({
      title: "Template saving",
      description: "Template saving functionality will be implemented here.",
    });
  };

  if (isLoading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header with navigation */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => navigate("/reports")}
            >
              <ChevronLeft size={18} />
            </Button>
            <h1 className="text-2xl font-bold">Create Report</h1>
          </div>
          
          <div className="flex items-center gap-3">
            {activeTab === "configure" && (
              <>
                <Button 
                  variant="outline" 
                  onClick={handleSaveTemplate}
                  disabled={!selectedReport || isSubmitting}
                >
                  Save as Template
                </Button>
                <Button 
                  variant="outline" 
                  onClick={handlePreview}
                  disabled={!selectedReport || isPreviewLoading}
                >
                  {isPreviewLoading ? "Loading..." : "Preview"}
                </Button>
                <Button 
                  onClick={handleGenerate}
                  disabled={!selectedReport || isSubmitting}
                >
                  {isSubmitting ? "Generating..." : "Generate Report"}
                </Button>
              </>
            )}
            
            {activeTab === "preview" && (
              <Button 
                onClick={handleGenerate}
                disabled={isSubmitting}
              >
                {isSubmitting ? "Generating..." : "Generate Report"}
              </Button>
            )}
          </div>
        </div>

        {/* Report Configuration */}
        {!selectedReport ? (
          <Card>
            <CardHeader>
              <CardTitle>Select a Report Type</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3">
                {reportOptions.map(report => (
                  <div 
                    key={report.id}
                    className="bg-card rounded-lg border p-6 hover:shadow-md transition-all cursor-pointer"
                    onClick={() => {
                      setSelectedReport(report);
                      setFormData(prev => ({
                        ...prev,
                        reportType: report.type || "",
                      }));
                    }}
                  >
                    <h3 className="font-semibold">{report.name}</h3>
                    <p className="text-sm text-muted-foreground mt-2">{report.description}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ) : (
          <Tabs defaultValue="configure" value={activeTab} onValueChange={setActiveTab}>
            <TabsList>
              <TabsTrigger value="configure">Configure</TabsTrigger>
              {previewData && <TabsTrigger value="preview">Preview</TabsTrigger>}
            </TabsList>
            
            <TabsContent value="configure">
              <Card>
                <CardHeader>
                  <CardTitle>{selectedReport.name}</CardTitle>
                </CardHeader>
                <CardContent>
                  <ReportFormFields 
                    reportOption={selectedReport}
                    formData={formData}
                    onFormChange={handleFormChange}
                    templates={templates.filter(t => t.reportType === selectedReport.type)}
                    onLoadTemplate={handleLoadTemplate}
                  />
                </CardContent>
              </Card>
            </TabsContent>
            
            <TabsContent value="preview">
              {previewData && (
                <ReportPreviewPanel 
                  reportType={selectedReport.type || ""}
                  previewData={previewData}
                  onEdit={() => setActiveTab("configure")}
                />
              )}
            </TabsContent>
          </Tabs>
        )}
      </div>
    </MainLayout>
  );
}
