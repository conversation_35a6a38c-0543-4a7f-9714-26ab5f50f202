// server/src/services/note.service.ts
import mongoose, { FilterQuery } from 'mongoose';
import { Note } from '../models/note.model';
import { Student } from '../models/student.model';
import { Class } from '../models/class.model';
import { AppError } from '../types/error.types';
import { SystemLogger } from './logger.service';
import {
    INote,
    NoteQueryOptions,
    CreateNoteDTO,
    UpdateNoteDTO,
    NoteBulkOperationDTO,
    NoteExportOptions,
    NoteSearchOptions,
    NoteResponseDTO,
    NoteCategory,
    NoteVisibility,
    entityTypeToModel
} from '../types/notes.types';
import { getVisibilityQuery, validateUserRole } from '../utils/permissionUtils';
import { isValidNoteCategory } from '../utils/noteUtils';

export class NoteService {


    private static handleServiceError(error: unknown, operation: string, details?: any): never {
        console.error(`Note service error during ${operation}:`, {
            error,
            details,
            errorDetails: error instanceof Error ? {
                name: error.name,
                message: error.message,
                stack: error.stack
            } : 'Unknown error type'
        });

        if (error instanceof AppError) {
            throw error;
        }

        if (error instanceof mongoose.Error.ValidationError) {
            throw new AppError(400, `Validation error: ${Object.values(error.errors)
                .map(e => e.message).join(', ')}`);
        }

        if (error instanceof mongoose.Error.CastError) {
            throw new AppError(400, `Invalid ID format: ${error.path}`);
        }

        throw new AppError(500, `Error ${operation}: ${error instanceof Error ?
            error.message : 'Unknown error'}`);
    }

    // Helper method to get visibility query based on user role
    private static getVisibilityQuery(userRole: string, userId: string): FilterQuery<INote> {
        // Validate role first
        validateUserRole(userRole);

        return getVisibilityQuery(userRole, userId);
    }

    // Main CRUD operations
    static async getNotes(
        options: NoteQueryOptions,
        requestingUserId: string,
        userRole: string
    ) {
        try {
            const {
                page = 1,
                limit = 10,
                sortBy = 'createdAt',
                sortOrder = 'desc',
                type,
                visibility,
                studentId,
                classId,
                tags,
                search,
                fromDate,
                toDate,
                createdBy
            } = options;

            // Validate userRole is valid
            if (!validateUserRole(userRole)) {
                throw new AppError(403, `Invalid role: ${userRole}`);
            }

            const query: FilterQuery<INote> = {
                ...this.getVisibilityQuery(userRole, requestingUserId)
            };

            // Apply filters with proper validation
            if (type !== undefined) {
                if (isValidNoteCategory(type)) {
                    query.type = type;
                } else {
                    throw new AppError(400, `Invalid note type: ${type}`);
                }
            }

            if (studentId !== undefined) {
                if (mongoose.Types.ObjectId.isValid(studentId)) {
                    query.studentId = new mongoose.Types.ObjectId(studentId);
                } else {
                    throw new AppError(400, `Invalid student ID format: ${studentId}`);
                }
            }

            if (classId !== undefined) {
                if (mongoose.Types.ObjectId.isValid(classId)) {
                    query.classId = new mongoose.Types.ObjectId(classId);
                } else {
                    throw new AppError(400, `Invalid class ID format: ${classId}`);
                }
            }

            // Handle tags array with proper validation
            if (tags !== undefined) {
                if (Array.isArray(tags)) {
                    const filteredTags = tags.filter(tag => tag !== null && tag !== undefined && tag !== '');
                    if (filteredTags.length > 0) {
                        query.tags = { $all: filteredTags };
                    }
                } else if (typeof tags === 'string') {
                    query.tags = { $all: [tags] };
                }
            }

            // Handle search term with proper validation
            if (search !== undefined) {
                const searchTerm = typeof search === 'string' ? search.trim() : '';
                if (searchTerm.length > 0) {
                    query.$or = [
                        { content: { $regex: searchTerm, $options: 'i' } },
                        { tags: { $regex: searchTerm, $options: 'i' } }
                    ];
                }
            }

            // Handle date range with proper validation
            if (fromDate !== undefined || toDate !== undefined) {
                query.createdAt = {};

                if (fromDate !== undefined) {
                    const fromDateObj = fromDate instanceof Date ? fromDate : new Date(fromDate);
                    if (!isNaN(fromDateObj.getTime())) {
                        query.createdAt.$gte = fromDateObj;
                    } else {
                        throw new AppError(400, 'Invalid fromDate format');
                    }
                }

                if (toDate !== undefined) {
                    const toDateObj = toDate instanceof Date ? toDate : new Date(toDate);
                    if (!isNaN(toDateObj.getTime())) {
                        query.createdAt.$lte = toDateObj;
                    } else {
                        throw new AppError(400, 'Invalid toDate format');
                    }
                }
            }

            if (createdBy !== undefined) {
                if (mongoose.Types.ObjectId.isValid(createdBy)) {
                    query.createdBy = new mongoose.Types.ObjectId(createdBy);
                } else {
                    throw new AppError(400, `Invalid user ID format: ${createdBy}`);
                }
            }

            // Validate and bound pagination parameters
            const safePage = Math.max(1, Math.min(1000, typeof page === 'number' ? page : parseInt(String(page)) || 1));
            const safeLimit = Math.max(1, Math.min(100, typeof limit === 'number' ? limit : parseInt(String(limit)) || 10));
            const skip = (safePage - 1) * safeLimit;

            // Validate sort parameters
            const validSortFields = ['createdAt', 'modifiedAt', 'type', 'visibility'];
            const safeSortBy = validSortFields.includes(String(sortBy)) ? String(sortBy) : 'createdAt';
            const safeSortOrder = ['asc', 'desc'].includes(String(sortOrder)) ? String(sortOrder) : 'desc';

            const sort: Record<string, 1 | -1> = {
                [safeSortBy]: safeSortOrder === 'asc' ? 1 : -1
            };

            // Execute queries with pagination and sorting
            const [notes, total] = await Promise.all([
                Note.find(query)
                    .populate({
                        path: 'createdBy',
                        select: 'username'
                    })
                    .populate({
                        path: 'modifiedBy',
                        select: 'username'
                    })
                    .populate({
                        path: 'studentId',
                        select: 'name',
                        options: { lean: true }
                    })
                    .populate({
                        path: 'classId',
                        select: 'name',
                        options: { lean: true }
                    })
                    .sort(sort)
                    .skip(skip)
                    .limit(safeLimit)
                    .lean(),
                Note.countDocuments(query)
            ]);

            // Log the operation with sanitized parameters
            await SystemLogger.log({
                severity: 'info',
                category: 'notes',
                action: 'list_notes',
                performedBy: requestingUserId,
                details: {
                    filters: {
                        page: safePage,
                        limit: safeLimit,
                        sortBy: safeSortBy,
                        sortOrder: safeSortOrder,
                        hasResults: (notes?.length ?? 0) > 0,
                        totalResults: total ?? 0
                    }
                },
                status: 'success',
                timestamp: new Date()
            });

            // Return results with safe handling for null/undefined
            return {
                notes: Array.isArray(notes) ? notes.map(note => this.formatNoteResponse(note)) : [],
                pagination: {
                    total: total ?? 0,
                    page: safePage,
                    limit: safeLimit,
                    pages: Math.ceil((total ?? 0) / safeLimit)
                }
            };
        } catch (error) {
            // Properly categorize and rethrow errors
            if (error instanceof AppError) throw error;

            if (error instanceof mongoose.Error.ValidationError) {
                throw new AppError(400, `Validation error: ${Object.values(error.errors)
                    .map(e => e.message).join(', ')}`);
            }

            if (error instanceof mongoose.Error.CastError) {
                throw new AppError(400, `Invalid format for field: ${error.path}`);
            }

            throw new AppError(500, `Error fetching notes: ${error instanceof Error
                ? error.message : 'Unknown error'}`);
        }
    }

    static async getNoteById(
        id: string,
        requestingUserId: string,
        userRole: string
    ): Promise<NoteResponseDTO> {
        try {
            if (!mongoose.Types.ObjectId.isValid(id)) {
                throw new AppError(400, 'Invalid note ID format');
            }

            const note = await Note.findById(id)
                .populate({
                    path: 'createdBy',
                    select: 'username'
                })
                .populate({
                    path: 'modifiedBy',
                    select: 'username'
                })
                .populate({
                    path: 'studentId',
                    select: 'name',
                    options: { lean: true }
                })
                .populate({
                    path: 'classId',
                    select: 'name',
                    options: { lean: true }
                })
                .lean();

            if (!note) {
                throw new AppError(404, 'Note not found');
            }

            await SystemLogger.log({
                severity: 'info',
                category: 'notes',
                action: 'view_note',
                performedBy: requestingUserId,
                targetId: id,
                details: { noteId: id },
                status: 'success',
                timestamp: new Date()
            });

            return this.formatNoteResponse(note);
        } catch (error) {
            if (error instanceof mongoose.Error.CastError) {
                throw new AppError(400, 'Invalid note ID format');
            }
            throw error instanceof AppError ? error : new AppError(500, 'Error fetching note');
        }
    }


    static async createNote(
        noteData: CreateNoteDTO,
        createdBy: string
    ): Promise<NoteResponseDTO> {
        try {
            console.log('Starting note creation with data:', JSON.stringify({
                ...noteData,
                createdBy
            }, null, 2));

            // Validate entity references
            await this.validateEntityReferences(noteData);

            const note = new Note({
                ...noteData,
                createdBy: new mongoose.Types.ObjectId(createdBy),
                modifiedBy: new mongoose.Types.ObjectId(createdBy),
                modificationHistory: [] // Initialize empty array
            });

            console.log('Note model created:', JSON.stringify(note.toObject(), null, 2));

            await note.save();
            console.log('Note saved successfully with ID:', note._id);

            // Modified population to avoid virtual fields
            const populatedNote = await Note.findById(note._id)
                .populate({
                    path: 'createdBy',
                    select: 'username'
                })
                .populate({
                    path: 'modifiedBy',
                    select: 'username'
                })
                .populate({
                    path: 'studentId',
                    select: 'name',
                    options: { lean: true }  // Use lean to get plain object
                })
                .populate({
                    path: 'classId',
                    select: 'name',
                    options: { lean: true }  // Use lean to get plain object
                })
                .lean();  // Convert the main document to a plain object

            if (!populatedNote) {
                throw new AppError(500, 'Failed to retrieve created note');
            }

            await SystemLogger.log({
                severity: 'info',
                category: 'notes',
                action: 'create_note',
                performedBy: createdBy,
                targetId: note._id.toString(),
                details: {
                    type: note.type,
                    studentId: note.studentId?.toString(),
                    classId: note.classId?.toString()
                },
                status: 'success',
                timestamp: new Date()
            });

            // Format the response
            const formattedResponse = this.formatNoteResponse(populatedNote);
            console.log('Formatted response:', JSON.stringify(formattedResponse, null, 2));

            return formattedResponse;
        } catch (error) {
            console.error('Note creation failed:', {
                error,
                noteData,
                errorDetails: error instanceof Error ? {
                    message: error.message,
                    stack: error.stack,
                    name: error.name
                } : 'Unknown error type'
            });

            if (error instanceof mongoose.Error.ValidationError) {
                throw new AppError(400, `Validation error: ${Object.values(error.errors).map(e => e.message).join(', ')}`);
            }

            if (error instanceof mongoose.Error.CastError) {
                throw new AppError(400, `Invalid ID format: ${error.path}`);
            }

            if (error instanceof AppError) {
                throw error;
            }

            throw new AppError(500, `Error creating note: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    // private static formatNoteResponse(note: any): NoteResponseDTO {
    //     if (!note) {
    //         console.error('Attempting to format undefined note');
    //         throw new AppError(500, 'Cannot format undefined note response');
    //     }

    //     try {
    //         console.log('Formatting note response for:', note._id);

    //         return {
    //             id: note._id.toString(),
    //             type: note.type,
    //             visibility: note.visibility,
    //             content: note.content,
    //             tags: Array.isArray(note.tags) ? note.tags : [],
    //             createdBy: {
    //                 id: note.createdBy?._id 
    //                     ? note.createdBy._id.toString() 
    //                     : note.createdBy.toString(),
    //                 username: note.createdBy?.username || 'Unknown'
    //             },
    //             createdAt: note.createdAt,
    //             modifiedAt: note.modifiedAt,
    //             modifiedBy: {
    //                 id: note.modifiedBy?._id 
    //                     ? note.modifiedBy._id.toString() 
    //                     : note.modifiedBy.toString(),
    //                 username: note.modifiedBy?.username || 'Unknown'
    //             },
    //             student: note.studentId ? {
    //                 id: note.studentId._id 
    //                     ? note.studentId._id.toString() 
    //                     : note.studentId.toString(),
    //                 name: note.studentId.name || 'Unknown'
    //             } : undefined,
    //             class: note.classId ? {
    //                 id: note.classId._id 
    //                     ? note.classId._id.toString() 
    //                     : note.classId.toString(),
    //                 name: note.classId.name || 'Unknown'
    //             } : undefined,
    //             relatedTo: note.relatedTo ? {
    //                 type: note.relatedTo.type,
    //                 id: note.relatedTo.id.toString()
    //             } : undefined,
    //             modificationHistory: Array.isArray(note.modificationHistory) 
    //                 ? note.modificationHistory.map((record: any) => ({
    //                     modifiedBy: record.modifiedBy.toString(),
    //                     timestamp: record.timestamp,
    //                     changes: record.changes
    //                 })) 
    //                 : []
    //         };
    //     } catch (error) {
    //         console.error('Error in formatNoteResponse:', {
    //             error,
    //             noteId: note?._id?.toString(),
    //             noteData: note
    //         });
    //         throw new AppError(500, `Error formatting note response: ${error instanceof Error ? error.message : 'Unknown error'}`);
    //     }
    // }

    static async updateNote(
        id: string,
        updateData: UpdateNoteDTO,
        updatedBy: string,
        userRole: string
    ): Promise<NoteResponseDTO> {
        try {
            const note = await Note.findById(id)
                .populate({
                    path: 'createdBy',
                    select: 'username'
                })
                .populate({
                    path: 'modifiedBy',
                    select: 'username'
                })
                .populate({
                    path: 'studentId',
                    select: 'name',
                    options: { lean: true }
                })
                .populate({
                    path: 'classId',
                    select: 'name',
                    options: { lean: true }
                });

            if (!note) {
                throw new AppError(404, 'Note not found');
            }

            // Update fields
            Object.entries(updateData).forEach(([field, value]) => {
                if (value !== undefined) {
                    note.set(field, value);
                }
            });

            // Update modification related fields
            note.modifiedBy = new mongoose.Types.ObjectId(updatedBy);
            note.modifiedAt = new Date();

            // Save the updated note
            await note.save();

            // Fetch the updated note with populated fields
            const updatedNote = await Note.findById(note._id)
                .populate({
                    path: 'createdBy',
                    select: 'username'
                })
                .populate({
                    path: 'modifiedBy',
                    select: 'username'
                })
                .populate({
                    path: 'studentId',
                    select: 'name',
                    options: { lean: true }
                })
                .populate({
                    path: 'classId',
                    select: 'name',
                    options: { lean: true }
                })
                .lean();

            if (!updatedNote) {
                throw new AppError(500, 'Failed to retrieve updated note');
            }

            await SystemLogger.log({
                severity: 'info',
                category: 'notes',
                action: 'update_note',
                performedBy: updatedBy,
                targetId: id,
                details: { updateData },
                status: 'success',
                timestamp: new Date()
            });

            return this.formatNoteResponse(updatedNote);
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error updating note');
        }
    }

    static async deleteNote(
        id: string,
        deletedBy: string,
        userRole: string
    ): Promise<void> {
        try {
            const note = await Note.findById(id);
            if (!note) {
                throw new AppError(404, 'Note not found');
            }

            const hasAccess = await note.validateVisibilityAccess(
                new mongoose.Types.ObjectId(deletedBy),
                userRole
            );

            if (!hasAccess) {
                throw new AppError(403, 'Not authorized to delete this note');
            }

            await note.deleteOne();

            await SystemLogger.log({
                severity: 'info',
                category: 'notes',
                action: 'delete_note',
                performedBy: deletedBy,
                targetId: id,
                details: {
                    noteType: note.type,
                    studentId: note.studentId?.toString(),
                    classId: note.classId?.toString()
                },
                status: 'success',
                timestamp: new Date()
            });
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error deleting note');
        }
    }

    // Specialized note retrieval methods
    // Fixed getStudentNotes method
    static async getStudentNotes(
        studentId: string,
        options: {
            page: number;
            limit: number;
            type?: NoteCategory;
            fromDate?: Date;
            toDate?: Date;
        },
        requestingUserId: string,
        userRole: string
    ) {
        try {
            // Validate studentId
            if (!studentId || !mongoose.Types.ObjectId.isValid(studentId)) {
                throw new AppError(400, `Invalid student ID format: ${studentId}`);
            }

            // Validate userRole
            if (!validateUserRole(userRole)) {
                throw new AppError(403, `Invalid role: ${userRole}`);
            }

            // Safe defaults with null coalescing
            const {
                page = 1,
                limit = 10,
                type,
                fromDate,
                toDate
            } = options;

            // Create query with proper validation
            const query: FilterQuery<INote> = {
                studentId: new mongoose.Types.ObjectId(studentId),
                ...this.getVisibilityQuery(userRole, requestingUserId)
            };

            // Validate and add type filter
            if (type !== undefined) {
                if (isValidNoteCategory(type)) {
                    query.type = type;
                } else {
                    throw new AppError(400, `Invalid note type: ${type}`);
                }
            }

            // Handle date range with proper validation
            if (fromDate !== undefined || toDate !== undefined) {
                query.createdAt = {};

                if (fromDate !== undefined) {
                    const fromDateObj = fromDate instanceof Date ? fromDate : new Date(fromDate);
                    if (!isNaN(fromDateObj.getTime())) {
                        query.createdAt.$gte = fromDateObj;
                    } else {
                        throw new AppError(400, 'Invalid fromDate format');
                    }
                }

                if (toDate !== undefined) {
                    const toDateObj = toDate instanceof Date ? toDate : new Date(toDate);
                    if (!isNaN(toDateObj.getTime())) {
                        query.createdAt.$lte = toDateObj;
                    } else {
                        throw new AppError(400, 'Invalid toDate format');
                    }
                }
            }

            // Validate and bound pagination parameters
            const safePage = Math.max(1, Math.min(1000, page));
            const safeLimit = Math.max(1, Math.min(100, limit));
            const skip = (safePage - 1) * safeLimit;

            // Execute queries with safe error handling
            const [notes, total] = await Promise.all([
                Note.find(query)
                    .populate({
                        path: 'createdBy',
                        select: 'username'
                    })
                    .populate({
                        path: 'modifiedBy',
                        select: 'username'
                    })
                    .populate({
                        path: 'classId',
                        select: 'name',
                        options: { lean: true }
                    })
                    .sort({ createdAt: -1 })
                    .skip(skip)
                    .limit(safeLimit)
                    .lean(),
                Note.countDocuments(query)
            ]);

            // Log the operation
            await SystemLogger.log({
                severity: 'info',
                category: 'notes',
                action: 'get_student_notes',
                performedBy: requestingUserId,
                targetId: studentId,
                details: {
                    studentId,
                    filters: {
                        page: safePage,
                        limit: safeLimit,
                        resultCount: notes?.length ?? 0
                    }
                },
                status: 'success',
                timestamp: new Date()
            });

            // Return results with safe handling for null/undefined
            return {
                notes: Array.isArray(notes) ? notes.map(note => this.formatNoteResponse(note)) : [],
                pagination: {
                    total: total ?? 0,
                    page: safePage,
                    limit: safeLimit,
                    pages: Math.ceil((total ?? 0) / safeLimit)
                }
            };
        } catch (error) {
            // Handle errors with proper categorization
            if (error instanceof AppError) throw error;

            if (error instanceof mongoose.Error.ValidationError) {
                throw new AppError(400, `Validation error: ${Object.values(error.errors)
                    .map(e => e.message).join(', ')}`);
            }

            if (error instanceof mongoose.Error.CastError) {
                throw new AppError(400, `Invalid format for field: ${error.path}`);
            }

            throw new AppError(500, `Error fetching student notes: ${error instanceof Error
                ? error.message : 'Unknown error'}`);
        }
    }

    // Fixed getClassNotes method
    static async getClassNotes(
        classId: string,
        options: {
            page: number;
            limit: number;
            type?: NoteCategory;
            fromDate?: Date;
            toDate?: Date;
        },
        requestingUserId: string,
        userRole: string
    ) {
        try {
            // Validate classId
            if (!classId || !mongoose.Types.ObjectId.isValid(classId)) {
                throw new AppError(400, `Invalid class ID format: ${classId}`);
            }

            // Validate userRole
            if (!validateUserRole(userRole)) {
                throw new AppError(403, `Invalid role: ${userRole}`);
            }

            // Safe defaults with null coalescing
            const {
                page = 1,
                limit = 10,
                type,
                fromDate,
                toDate
            } = options;

            // Create query with proper validation
            const query: FilterQuery<INote> = {
                classId: new mongoose.Types.ObjectId(classId),
                ...this.getVisibilityQuery(userRole, requestingUserId)
            };

            // Validate and add type filter
            if (type !== undefined) {
                if (isValidNoteCategory(type)) {
                    query.type = type;
                } else {
                    throw new AppError(400, `Invalid note type: ${type}`);
                }
            }

            // Handle date range with proper validation
            if (fromDate !== undefined || toDate !== undefined) {
                query.createdAt = {};

                if (fromDate !== undefined) {
                    const fromDateObj = fromDate instanceof Date ? fromDate : new Date(fromDate);
                    if (!isNaN(fromDateObj.getTime())) {
                        query.createdAt.$gte = fromDateObj;
                    } else {
                        throw new AppError(400, 'Invalid fromDate format');
                    }
                }

                if (toDate !== undefined) {
                    const toDateObj = toDate instanceof Date ? toDate : new Date(toDate);
                    if (!isNaN(toDateObj.getTime())) {
                        query.createdAt.$lte = toDateObj;
                    } else {
                        throw new AppError(400, 'Invalid toDate format');
                    }
                }
            }

            // Validate and bound pagination parameters
            const safePage = Math.max(1, Math.min(1000, page));
            const safeLimit = Math.max(1, Math.min(100, limit));
            const skip = (safePage - 1) * safeLimit;

            // Execute queries with safe error handling
            const [notes, total] = await Promise.all([
                Note.find(query)
                    .populate({
                        path: 'createdBy',
                        select: 'username'
                    })
                    .populate({
                        path: 'modifiedBy',
                        select: 'username'
                    })
                    .populate({
                        path: 'studentId',
                        select: 'name',
                        options: { lean: true }
                    })
                    .sort({ createdAt: -1 })
                    .skip(skip)
                    .limit(safeLimit)
                    .lean(),
                Note.countDocuments(query)
            ]);

            // Log the operation
            await SystemLogger.log({
                severity: 'info',
                category: 'notes',
                action: 'get_class_notes',
                performedBy: requestingUserId,
                targetId: classId,
                details: {
                    classId,
                    filters: {
                        page: safePage,
                        limit: safeLimit,
                        resultCount: notes?.length ?? 0
                    }
                },
                status: 'success',
                timestamp: new Date()
            });

            // Return results with safe handling for null/undefined
            return {
                notes: Array.isArray(notes) ? notes.map(note => this.formatNoteResponse(note)) : [],
                pagination: {
                    total: total ?? 0,
                    page: safePage,
                    limit: safeLimit,
                    pages: Math.ceil((total ?? 0) / safeLimit)
                }
            };
        } catch (error) {
            // Handle errors with proper categorization
            if (error instanceof AppError) throw error;

            if (error instanceof mongoose.Error.ValidationError) {
                throw new AppError(400, `Validation error: ${Object.values(error.errors)
                    .map(e => e.message).join(', ')}`);
            }

            if (error instanceof mongoose.Error.CastError) {
                throw new AppError(400, `Invalid format for field: ${error.path}`);
            }

            throw new AppError(500, `Error fetching class notes: ${error instanceof Error
                ? error.message : 'Unknown error'}`);
        }
    }

    // Bulk operations and search
    static async bulkOperation(
        operation: NoteBulkOperationDTO,
        performedBy: string,
        userRole: string
    ) {
        try {
            // First verify notes exist
            const noteIds = operation.noteIds.map(id => new mongoose.Types.ObjectId(id));

            // Check permissions in bulk instead of per-note
            const hasPermission = await this.verifyBulkPermissions(noteIds, userRole, performedBy);
            if (!hasPermission) {
                throw new AppError(403, 'Not authorized to perform this operation on one or more notes');
            }

            // Use bulk operations instead of individual calls
            const modifiedAt = new Date();
            const performedById = new mongoose.Types.ObjectId(performedBy);
            let successCount = 0;

            switch (operation.operation) {
                case 'delete':
                    const deleteResult = await Note.deleteMany({ _id: { $in: noteIds } });
                    // Safe access with type check
                    successCount = typeof deleteResult.deletedCount === 'number' ? deleteResult.deletedCount : 0;
                    break;

                case 'updateVisibility':
                case 'updateTags':
                    const updateField = operation.operation === 'updateVisibility' ? 'visibility' : 'tags';

                    const updateResult = await Note.updateMany(
                        { _id: { $in: noteIds } },
                        {
                            $set: {
                                [updateField]: operation.value,
                                modifiedAt,
                                modifiedBy: performedById
                            },
                            $push: {
                                modificationHistory: {
                                    modifiedBy: performedById,
                                    timestamp: modifiedAt,
                                    changes: {
                                        field: updateField,
                                        oldValue: '(multiple)',
                                        newValue: operation.value
                                    }
                                }
                            }
                        }
                    );
                    // Safe access with type check
                    successCount = typeof updateResult.modifiedCount === 'number' ? updateResult.modifiedCount : 0;
                    break;

                default:
                    throw new AppError(400, 'Invalid operation');
            }

            const summary = {
                total: noteIds.length,
                successful: successCount,
                failed: noteIds.length - successCount,
                errors: successCount < noteIds.length ?
                    [{ message: 'Some operations failed, see count for details' }] : []
            };

            await SystemLogger.log({
                severity: 'info',
                category: 'notes',
                action: 'bulk_operation',
                performedBy,
                details: {
                    operation: operation.operation,
                    summary,
                    noteIds: operation.noteIds
                },
                status: 'success',
                timestamp: new Date()
            });

            return summary;
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error performing bulk operation');
        }
    }

    // Search and export functionality
    static async searchNotes(
        options: NoteSearchOptions,
        requestingUserId: string,
        userRole: string
    ) {
        try {
            // Default values with null coalescing
            const {
                search,
                searchFields = ['content', 'tags'],
                includeArchived = false,
                groupBy,
                page = 1,
                limit = 10,
                type,
                fromDate,
                toDate
            } = options;

            // Validate userRole
            if (!validateUserRole(userRole)) {
                throw new AppError(403, `Invalid role: ${userRole}`);
            }

            const query: FilterQuery<INote> = {
                ...this.getVisibilityQuery(userRole, requestingUserId)
            };

            // Add search criteria with proper validation
            if (search !== undefined && search !== null) {
                const searchTerm = typeof search === 'string' ? search.trim() : '';

                if (searchTerm.length > 0) {
                    const searchQuery: any[] = [];

                    // Only add valid search fields
                    if (Array.isArray(searchFields)) {
                        if (searchFields.includes('content')) {
                            searchQuery.push({ content: { $regex: searchTerm, $options: 'i' } });
                        }

                        if (searchFields.includes('tags')) {
                            searchQuery.push({ tags: { $regex: searchTerm, $options: 'i' } });
                        }
                    } else {
                        // Default to content and tags if searchFields is invalid
                        searchQuery.push({ content: { $regex: searchTerm, $options: 'i' } });
                        searchQuery.push({ tags: { $regex: searchTerm, $options: 'i' } });
                    }

                    if (searchQuery.length > 0) {
                        query.$or = searchQuery;
                    }
                }
            }

            // Apply additional filters with proper validation
            if (type !== undefined) {
                if (isValidNoteCategory(type)) {
                    query.type = type;
                } else {
                    throw new AppError(400, `Invalid note type: ${type}`);
                }
            }

            // Handle date range with proper validation
            if (fromDate !== undefined || toDate !== undefined) {
                query.createdAt = {};

                if (fromDate !== undefined) {
                    const fromDateObj = fromDate instanceof Date ? fromDate : new Date(fromDate);
                    if (!isNaN(fromDateObj.getTime())) {
                        query.createdAt.$gte = fromDateObj;
                    } else {
                        throw new AppError(400, 'Invalid fromDate format');
                    }
                }

                if (toDate !== undefined) {
                    const toDateObj = toDate instanceof Date ? toDate : new Date(toDate);
                    if (!isNaN(toDateObj.getTime())) {
                        query.createdAt.$lte = toDateObj;
                    } else {
                        throw new AppError(400, 'Invalid toDate format');
                    }
                }
            }

            // Validate pagination parameters
            const safePage = Math.max(1, Math.min(1000, typeof page === 'number' ? page : parseInt(String(page)) || 1));
            const safeLimit = Math.max(1, Math.min(100, typeof limit === 'number' ? limit : parseInt(String(limit)) || 10));
            const skip = (safePage - 1) * safeLimit;

            // Handle grouping with proper validation
            let notes;
            let total;

            if (groupBy !== undefined) {
                const validGroupFields = ['student', 'class', 'type', 'date'];
                const safeGroupBy = validGroupFields.includes(String(groupBy)) ? String(groupBy) : null;

                if (safeGroupBy) {
                    // Define aggregation field based on group type
                    const groupField = safeGroupBy === 'date'
                        ? { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } }
                        : safeGroupBy === 'type'
                            ? '$type'
                            : `$${safeGroupBy}Id`;

                    // Run aggregation with safe error handling
                    try {
                        const aggregation = await Note.aggregate([
                            { $match: query },
                            {
                                $group: {
                                    _id: groupField,
                                    notes: { $push: '$$ROOT' },
                                    count: { $sum: 1 }
                                }
                            },
                            { $sort: { _id: 1 } },
                            { $skip: skip },
                            { $limit: safeLimit }
                        ]);

                        const totalGroups = await Note.aggregate([
                            { $match: query },
                            { $group: { _id: groupField } },
                            { $count: 'total' }
                        ]);

                        notes = aggregation || [];
                        total = totalGroups[0]?.total ?? 0;
                    } catch (aggregateError) {
                        console.error('Aggregation error:', aggregateError);
                        throw new AppError(500, 'Error performing grouped search');
                    }
                } else {
                    // Fall back to regular search if groupBy is invalid
                    [notes, total] = await Promise.all([
                        Note.find(query)
                            .populate({
                                path: 'createdBy',
                                select: 'username'
                            })
                            .populate({
                                path: 'modifiedBy',
                                select: 'username'
                            })
                            .populate({
                                path: 'studentId',
                                select: 'name',
                                options: { lean: true }
                            })
                            .populate({
                                path: 'classId',
                                select: 'name',
                                options: { lean: true }
                            })
                            .sort({ createdAt: -1 })
                            .skip(skip)
                            .limit(safeLimit)
                            .lean(),
                        Note.countDocuments(query)
                    ]);

                    notes = Array.isArray(notes) ? notes.map(note => this.formatNoteResponse(note)) : [];
                }
            } else {
                // Regular search without grouping
                [notes, total] = await Promise.all([
                    Note.find(query)
                        .populate({
                            path: 'createdBy',
                            select: 'username'
                        })
                        .populate({
                            path: 'modifiedBy',
                            select: 'username'
                        })
                        .populate({
                            path: 'studentId',
                            select: 'name',
                            options: { lean: true }
                        })
                        .populate({
                            path: 'classId',
                            select: 'name',
                            options: { lean: true }
                        })
                        .sort({ createdAt: -1 })
                        .skip(skip)
                        .limit(safeLimit)
                        .lean(),
                    Note.countDocuments(query)
                ]);

                notes = Array.isArray(notes) ? notes.map(note => this.formatNoteResponse(note)) : [];
            }

            // Log the search operation
            await SystemLogger.log({
                severity: 'info',
                category: 'notes',
                action: 'search_notes',
                performedBy: requestingUserId,
                details: {
                    searchCriteria: {
                        search: typeof search === 'string' ? search.trim() : null,
                        resultCount: Array.isArray(notes) ? notes.length : 0,
                        groupBy: groupBy,
                        page: safePage,
                        limit: safeLimit
                    }
                },
                status: 'success',
                timestamp: new Date()
            });

            // Return results with safe handling for null/undefined
            return {
                notes: notes ?? [],
                pagination: {
                    total: total ?? 0,
                    page: safePage,
                    limit: safeLimit,
                    pages: Math.ceil((total ?? 0) / safeLimit)
                }
            };
        } catch (error) {
            // Handle errors with proper categorization
            if (error instanceof AppError) throw error;

            if (error instanceof mongoose.Error.ValidationError) {
                throw new AppError(400, `Validation error: ${Object.values(error.errors)
                    .map(e => e.message).join(', ')}`);
            }

            if (error instanceof mongoose.Error.CastError) {
                throw new AppError(400, `Invalid format for field: ${error.path}`);
            }

            throw new AppError(500, `Error searching notes: ${error instanceof Error
                ? error.message : 'Unknown error'}`);
        }
    }

    static async exportNotes(
        options: NoteExportOptions,
        exportedBy: string,
        userRole: string
    ): Promise<string> {
        try {
            const query: FilterQuery<INote> = {
                ...this.getVisibilityQuery(userRole, exportedBy)
            };

            if (options.dateRange) {
                query.createdAt = {
                    $gte: options.dateRange.start,
                    $lte: options.dateRange.end
                };
            }

            const notes = await Note.find(query)
                .populate({
                    path: 'createdBy',
                    select: 'username'
                })
                .populate({
                    path: 'modifiedBy',
                    select: 'username'
                })
                .populate({
                    path: 'studentId',
                    select: 'name',
                    options: { lean: true }
                })
                .populate({
                    path: 'classId',
                    select: 'name',
                    options: { lean: true }
                })
                .sort({ createdAt: -1 })
                .lean();

            let exportData = notes.map(note => {
                const baseData = this.formatNoteResponse(note);
                if (options.includeHistory) {
                    (baseData as any).modificationHistory = note.modificationHistory;
                }
                return baseData;
            });

            if (options.groupBy) {
                exportData = this.groupNotesByField(exportData, options.groupBy);
            }

            await SystemLogger.log({
                severity: 'info',
                category: 'notes',
                action: 'export_notes',
                performedBy: exportedBy,
                details: {
                    format: options.format,
                    resultCount: notes.length
                },
                status: 'success',
                timestamp: new Date()
            });

            return options.format === 'json'
                ? JSON.stringify(exportData, null, 2)
                : this.convertToCSV(exportData, options.fields || Object.keys(exportData[0]));
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error exporting notes');
        }
    }

    /**
 * Validates that all entities referenced in a note actually exist in the database.
 * Handles both student, class, and related entity references.
 * 
 * @param noteData The note data to validate
 * @throws AppError if any referenced entity doesn't exist
 */
    private static async validateEntityReferences(noteData: CreateNoteDTO): Promise<void> {
        try {
            // Prepare validation promises for all references
            const validationPromises = [];

            if (noteData.studentId) {
                if (!mongoose.Types.ObjectId.isValid(noteData.studentId)) {
                    throw new AppError(400, 'Invalid student ID format');
                }

                validationPromises.push(
                    Student.findById(noteData.studentId)
                        .then(student => {
                            if (!student) throw new AppError(404, 'Referenced student not found');
                            return true;
                        })
                );
            }

            if (noteData.classId) {
                if (!mongoose.Types.ObjectId.isValid(noteData.classId)) {
                    throw new AppError(400, 'Invalid class ID format');
                }

                validationPromises.push(
                    Class.findById(noteData.classId)
                        .then(classDoc => {
                            if (!classDoc) throw new AppError(404, 'Referenced class not found');
                            return true;
                        })
                );
            }

            if (noteData.relatedTo && noteData.relatedTo.type && noteData.relatedTo.id) {
                // Handle related entity validation
                const type = noteData.relatedTo.type.toLowerCase();
                const modelName = Object.prototype.hasOwnProperty.call(entityTypeToModel, type)
                    ? entityTypeToModel[type]
                    : noteData.relatedTo.type;

                validationPromises.push(
                    Promise.resolve().then(() => {
                        try {
                            const Model = mongoose.model(modelName);
                            return Model.findById(noteData.relatedTo?.id) // Optional chaining
                                .then(entity => {
                                    if (!entity) throw new AppError(404, `Referenced ${noteData.relatedTo?.type} not found`);
                                    return true;
                                });
                        } catch (err) {
                            const error = err as Error;
                            if (error?.name === 'MissingSchemaError') {
                                throw new AppError(400, `Invalid model type: ${modelName}`);
                            }
                            throw error;
                        }
                    })
                );
            }

            // Ensure at least one reference exists
            if (!noteData.studentId && !noteData.classId && !noteData.relatedTo) {
                throw new AppError(400, 'Note must be associated with at least one entity');
            }

            // Run all validations in parallel
            await Promise.all(validationPromises);
        } catch (error) {
            if (error instanceof AppError) throw error;
            console.error('Entity reference validation error:', error);
            throw new AppError(500, `Error validating entity references: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    private static formatNoteResponse(note: any): NoteResponseDTO {
        if (!note) {
            // Return a safe default object instead of throwing
            return {
                id: '',
                type: 'general',
                visibility: 'teacher_only',
                content: '',
                tags: [],
                createdBy: {
                    id: '',
                    username: 'Unknown'
                },
                createdAt: new Date(),
                modifiedAt: new Date(),
                modifiedBy: {
                    id: '',
                    username: 'Unknown'
                },
                modificationHistory: []
            };
        }

        try {
            // Safe getters with defaults and proper null handling
            const getId = (obj: any): string => {
                if (obj === null || obj === undefined) return '';
                return obj._id ? obj._id.toString() : String(obj);
            };

            const getUsername = (obj: any): string => {
                return obj?.username ?? 'Unknown';
            };

            const getName = (obj: any): string => {
                return obj?.name ?? 'Unknown';
            };

            // Safe date conversion
            const toDate = (value: any): Date => {
                if (value instanceof Date) return value;
                if (typeof value === 'string' || typeof value === 'number') {
                    const date = new Date(value);
                    return isNaN(date.getTime()) ? new Date() : date;
                }
                return new Date();
            };

            // Filter null/undefined/empty values from tags
            const processTags = (tagArray: any): string[] => {
                if (!Array.isArray(tagArray)) return [];
                return tagArray.filter(tag =>
                    tag !== null &&
                    tag !== undefined &&
                    tag !== '' &&
                    typeof tag === 'string'
                );
            };

            // Safe processing of modification history
            const processModificationHistory = (history: any): any[] => {
                if (!Array.isArray(history)) return [];

                return history.map(record => ({
                    modifiedBy: record?.modifiedBy ? getId(record.modifiedBy) : '',
                    timestamp: toDate(record?.timestamp),
                    changes: record?.changes ?? {}
                }));
            };

            // Build response with null coalescing for all properties
            return {
                id: getId(note._id),
                type: note.type ?? 'general',
                visibility: note.visibility ?? 'teacher_only',
                content: note.content ?? '',
                tags: processTags(note.tags),
                createdBy: {
                    id: getId(note.createdBy),
                    username: getUsername(note.createdBy)
                },
                createdAt: toDate(note.createdAt),
                modifiedAt: toDate(note.modifiedAt),
                modifiedBy: {
                    id: getId(note.modifiedBy),
                    username: getUsername(note.modifiedBy)
                },
                student: note.studentId ? {
                    id: getId(note.studentId),
                    name: getName(note.studentId)
                } : undefined,
                class: note.classId ? {
                    id: getId(note.classId),
                    name: getName(note.classId)
                } : undefined,
                relatedTo: note.relatedTo ? {
                    type: note.relatedTo.type ?? 'unknown',
                    id: getId(note.relatedTo.id)
                } : undefined,
                modificationHistory: processModificationHistory(note.modificationHistory)
            };
        } catch (error) {
            // Log error but return a safe default object instead of throwing
            console.error('Error formatting note response:', {
                error,
                noteId: note?._id?.toString(),
                errorDetails: error instanceof Error ? {
                    message: error.message,
                    stack: error.stack
                } : 'Unknown error type'
            });

            return {
                id: note?._id?.toString() ?? '',
                type: 'general',
                visibility: 'teacher_only',
                content: note?.content ?? '',
                tags: [],
                createdBy: {
                    id: '',
                    username: 'Error formatting data'
                },
                createdAt: new Date(),
                modifiedAt: new Date(),
                modifiedBy: {
                    id: '',
                    username: 'Error formatting data'
                },
                modificationHistory: []
            };
        }
    }

    private static groupNotesByField(notes: any[], groupBy: string): any[] {
        const grouped = notes.reduce((acc: any, note: any) => {
            let key: string;
            switch (groupBy) {
                case 'date':
                    key = new Date(note.createdAt).toISOString().split('T')[0];
                    break;
                case 'student':
                    key = note.student?.name || 'Unassigned';
                    break;
                case 'class':
                    key = note.class?.name || 'Unassigned';
                    break;
                case 'type':
                    key = note.type;
                    break;
                default:
                    key = 'Other';
            }
            if (!acc[key]) {
                acc[key] = [];
            }
            acc[key].push(note);
            return acc;
        }, {});

        return Object.entries(grouped).map(([key, notes]) => ({
            group: key,
            notes,
            count: (notes as any[]).length
        }));
    }

    private static convertToCSV(data: any[], fields: string[]): string {
        const header = fields.join(',');
        const rows = data.map(item =>
            fields.map(field => {
                const value = this.getNestedValue(item, field);
                return this.formatCSVValue(value);
            }).join(',')
        );
        return [header, ...rows].join('\n');
    }

    private static getNestedValue(obj: any, path: string): any {
        return path.split('.').reduce((current, key) => current?.[key], obj);
    }

    private static formatCSVValue(value: any): string {
        if (value === null || value === undefined) return '';
        if (value instanceof Date) return value.toISOString();
        if (typeof value === 'object') return JSON.stringify(value);
        return `"${String(value).replace(/"/g, '""')}"`;
    }

    private static async verifyBulkPermissions(
        noteIds: mongoose.Types.ObjectId[],
        userRole: string,
        userId: string
    ): Promise<boolean> {
        if (userRole === 'superAdmin') return true;

        const visibilityQuery = this.getVisibilityQuery(userRole, userId);
        const count = await Note.countDocuments({
            _id: { $in: noteIds },
            ...visibilityQuery
        });

        return count === noteIds.length;
    }
}