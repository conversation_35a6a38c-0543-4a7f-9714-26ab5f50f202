
import { RoomScheduleItem } from "@/types/room";
import { format, addDays, startOfDay, isSameDay, parseISO } from "date-fns";
import {
  Card,
  CardContent,
} from "@/components/ui/card";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface RoomScheduleCalendarProps {
  scheduleItems: RoomScheduleItem[];
  weekStart: Date;
  roomName: string;
}

const RoomScheduleCalendar: React.FC<RoomScheduleCalendarProps> = ({ 
  scheduleItems, 
  weekStart,
  roomName
}) => {
  // Generate weekdays
  const weekDays = Array.from({ length: 7 }, (_, i) => addDays(weekStart, i));
  
  // Map hour labels
  const hours = Array.from({ length: 15 }, (_, i) => i + 7); // 7:00 to 21:00
  
  // Get events for a specific day
  const getDayEvents = (day: Date) => {
    return scheduleItems.filter(item => 
      isSameDay(parseISO(item.start), day)
    );
  };
  
  // Calculate position for an event
  const getEventPosition = (event: RoomScheduleItem) => {
    const startTime = parseISO(event.start);
    const endTime = parseISO(event.end);
    
    const startHour = startTime.getHours() + startTime.getMinutes() / 60;
    const endHour = endTime.getHours() + endTime.getMinutes() / 60;
    
    // Calculate relative to our time range (7:00 - 21:00)
    const top = Math.max(0, (startHour - 7) * 60); // 60px per hour
    const height = Math.min((endHour - startHour) * 60, (21 - startHour) * 60);
    
    return { top, height };
  };
  
  return (
    <div className="w-full overflow-auto">
      <div className="min-w-[800px]">
        <div className="grid grid-cols-[60px_repeat(7,1fr)] border-b">
          <div className="border-r p-2"></div>
          {weekDays.map((day, index) => (
            <div 
              key={index} 
              className={`p-2 text-center border-r ${
                isSameDay(day, new Date()) ? 'bg-muted font-medium' : ''
              }`}
            >
              <div className="text-sm font-medium">
                {format(day, 'EEE')}
              </div>
              <div className="text-xs text-muted-foreground">
                {format(day, 'MMM d')}
              </div>
            </div>
          ))}
        </div>
        
        <div className="relative grid grid-cols-[60px_repeat(7,1fr)]" style={{ height: `${hours.length * 60}px` }}>
          {/* Time labels */}
          <div className="border-r">
            {hours.map((hour) => (
              <div 
                key={hour} 
                className="h-[60px] border-b flex items-start justify-end pr-1 text-xs text-muted-foreground"
                style={{ marginTop: '-0.5rem' }}
              >
                {hour}:00
              </div>
            ))}
          </div>
          
          {/* Day columns with hour lines */}
          {weekDays.map((day, dayIndex) => (
            <div key={dayIndex} className="relative border-r">
              {/* Hour lines */}
              {hours.map((hour) => (
                <div 
                  key={hour} 
                  className="h-[60px] border-b border-dashed"
                ></div>
              ))}
              
              {/* Events */}
              {getDayEvents(day).map((event, eventIndex) => {
                const { top, height } = getEventPosition(event);
                const eventColor = event.backgroundColor || (event.type === 'maintenance' ? '#F44336' : '#2196F3');
                
                return (
                  <TooltipProvider key={eventIndex}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div
                          className="absolute left-1 right-1 px-2 py-1 rounded text-white text-xs overflow-hidden"
                          style={{
                            top: `${top}px`,
                            height: `${height}px`,
                            backgroundColor: eventColor,
                          }}
                        >
                          <div className="font-medium truncate">
                            {event.title}
                          </div>
                          {event.teacherName && (
                            <div className="truncate opacity-80 text-[10px]">
                              {event.teacherName}
                            </div>
                          )}
                        </div>
                      </TooltipTrigger>
                      <TooltipContent>
                        <div className="space-y-1">
                          <p className="font-medium">{event.title}</p>
                          {event.teacherName && (
                            <p className="text-xs">Teacher: {event.teacherName}</p>
                          )}
                          <p className="text-xs">
                            {format(parseISO(event.start), 'h:mm a')} - {format(parseISO(event.end), 'h:mm a')}
                          </p>
                          <p className="text-xs">
                            Room: {roomName}
                          </p>
                        </div>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                );
              })}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default RoomScheduleCalendar;
