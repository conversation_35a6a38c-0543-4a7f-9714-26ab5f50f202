
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { 
  Tabs, 
  TabsContent, 
  TabsList, 
  TabsTrigger 
} from "@/components/ui/tabs";
import { 
  Calendar, 
  Book, 
  ChevronRight, 
  User, 
  Users,
  ArrowRight
} from "lucide-react";
import { LevelChange, ClassAssignment } from "@/types/student";

interface StudentHistoryTabProps {
  studentId: string;
  levelHistory: LevelChange[];
  classHistory: ClassAssignment[];
}

const StudentHistoryTab = ({ 
  studentId, 
  levelHistory, 
  classHistory 
}: StudentHistoryTabProps) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <Tabs defaultValue="all">
      <TabsList className="mb-6">
        <TabsTrigger value="all">All History</TabsTrigger>
        <TabsTrigger value="level">Level Changes</TabsTrigger>
        <TabsTrigger value="class">Class Assignments</TabsTrigger>
      </TabsList>
      
      <TabsContent value="all">
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Student History Timeline</CardTitle>
              <CardDescription>
                Complete history of student's levels and class assignments
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-8">
                {/* Combine and sort all history events */}
                {[
                  ...levelHistory.map(item => ({
                    type: 'level',
                    date: item.date,
                    data: item
                  })),
                  ...classHistory.map(item => ({
                    type: 'class',
                    date: item.fromDate, 
                    data: item
                  }))
                ]
                  .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
                  .map((event, index) => (
                    <div key={index} className="flex">
                      <div className="flex flex-col items-center mr-4">
                        <div className={`rounded-full p-2 ${
                          event.type === 'level' 
                            ? 'bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400' 
                            : 'bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400'
                        }`}>
                          {event.type === 'level' ? (
                            <Book size={18} />
                          ) : (
                            <Users size={18} />
                          )}
                        </div>
                        {index < 
                          (levelHistory.length + classHistory.length - 1) && (
                          <div className="w-0.5 h-full bg-border mt-2"></div>
                        )}
                      </div>
                      
                      <div className="pb-8">
                        <div className="flex items-center text-sm text-muted-foreground mb-1">
                          <Calendar size={14} className="mr-2" />
                          <time dateTime={event.date}>
                            {formatDate(event.date)}
                          </time>
                        </div>
                        
                        {event.type === 'level' ? (
                          // Level change event
                          <div className="bg-purple-50 dark:bg-purple-900/10 border border-purple-100 dark:border-purple-900/30 rounded-lg p-4 mt-1">
                            <h3 className="font-medium">Level Changed</h3>
                            <div className="flex items-center gap-2 mt-2 text-sm">
                              <span>{(event.data as LevelChange).fromLevel}</span>
                              <ChevronRight size={16} />
                              <span className="font-medium">{(event.data as LevelChange).toLevel}</span>
                            </div>
                            <p className="text-sm mt-3">
                              <span className="text-muted-foreground">Reason: </span>
                              {(event.data as LevelChange).reason}
                            </p>
                            <div className="flex items-center gap-2 mt-3 text-xs text-muted-foreground">
                              <User size={12} />
                              <span>Changed by: {(event.data as LevelChange).changedBy}</span>
                            </div>
                          </div>
                        ) : (
                          // Class assignment event
                          <div className="bg-blue-50 dark:bg-blue-900/10 border border-blue-100 dark:border-blue-900/30 rounded-lg p-4 mt-1">
                            <h3 className="font-medium">Class Assignment</h3>
                            <div className="mt-2">
                              <div className="flex items-center gap-2 text-sm">
                                <Users size={16} />
                                <span className="font-medium">
                                  {(event.data as ClassAssignment).className}
                                </span>
                              </div>
                              <div className="flex items-center gap-2 mt-2 text-sm">
                                <span>From: {formatDate((event.data as ClassAssignment).fromDate)}</span>
                                {(event.data as ClassAssignment).toDate && (
                                  <>
                                    <ArrowRight size={14} />
                                    <span>To: {formatDate((event.data as ClassAssignment).toDate)}</span>
                                  </>
                                )}
                              </div>
                            </div>
                            {(event.data as ClassAssignment).reason && (
                              <p className="text-sm mt-3">
                                <span className="text-muted-foreground">Reason: </span>
                                {(event.data as ClassAssignment).reason}
                              </p>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                
                {levelHistory.length === 0 && classHistory.length === 0 && (
                  <div className="text-center p-8 text-muted-foreground">
                    No history records found for this student.
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </TabsContent>
      
      <TabsContent value="level">
        <Card>
          <CardHeader>
            <CardTitle>Level Change History</CardTitle>
            <CardDescription>
              Record of all level changes for this student
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>From Level</TableHead>
                  <TableHead>To Level</TableHead>
                  <TableHead>Reason</TableHead>
                  <TableHead>Changed By</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {levelHistory.length > 0 ? (
                  levelHistory.map((change) => (
                    <TableRow key={change.id}>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Calendar size={16} className="text-muted-foreground" />
                          {formatDate(change.date)}
                        </div>
                      </TableCell>
                      <TableCell>{change.fromLevel}</TableCell>
                      <TableCell className="font-medium">{change.toLevel}</TableCell>
                      <TableCell>{change.reason}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <User size={16} className="text-muted-foreground" />
                          {change.changedBy}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={5} className="h-24 text-center">
                      No level change history found.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </TabsContent>
      
      <TabsContent value="class">
        <Card>
          <CardHeader>
            <CardTitle>Class Assignment History</CardTitle>
            <CardDescription>
              Record of all class assignments for this student
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Class</TableHead>
                  <TableHead>From Date</TableHead>
                  <TableHead>To Date</TableHead>
                  <TableHead>Duration</TableHead>
                  <TableHead>Reason</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {classHistory.length > 0 ? (
                  [...classHistory].reverse().map((assignment) => {
                    const fromDate = new Date(assignment.fromDate);
                    const toDate = assignment.toDate ? new Date(assignment.toDate) : null;
                    
                    // Calculate duration in days, months
                    let duration = "Current";
                    if (toDate) {
                      const diffTime = Math.abs(toDate.getTime() - fromDate.getTime());
                      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                      
                      if (diffDays < 30) {
                        duration = `${diffDays} day${diffDays !== 1 ? 's' : ''}`;
                      } else {
                        const diffMonths = Math.floor(diffDays / 30);
                        duration = `${diffMonths} month${diffMonths !== 1 ? 's' : ''}`;
                      }
                    }
                    
                    return (
                      <TableRow key={assignment.id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            <Users size={16} className="text-muted-foreground" />
                            {assignment.className}
                          </div>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Calendar size={16} className="text-muted-foreground" />
                            {formatDate(assignment.fromDate)}
                          </div>
                        </TableCell>
                        <TableCell>
                          {assignment.toDate ? (
                            <div className="flex items-center gap-2">
                              <Calendar size={16} className="text-muted-foreground" />
                              {formatDate(assignment.toDate)}
                            </div>
                          ) : (
                            "Current"
                          )}
                        </TableCell>
                        <TableCell>
                          {duration}
                        </TableCell>
                        <TableCell>
                          {assignment.reason || "—"}
                        </TableCell>
                      </TableRow>
                    );
                  })
                ) : (
                  <TableRow>
                    <TableCell colSpan={5} className="h-24 text-center">
                      No class assignment history found.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  );
};

export default StudentHistoryTab;
