
import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { TeacherScheduleData, ScheduleOperation } from "@/types/teacherScheduling";
import { TeacherScheduleItem } from "@/types/class";
import { capitalizeFirstLetter } from "@/lib/utils";
import { ArrowLeft, ArrowRight, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { getTeacherAvailability } from "@/services/teacherSchedulingService";

interface ScheduleCalendarViewProps {
  teacherSchedules: TeacherScheduleData[];
  onSlotClick: (teacherId: string, slot: TeacherScheduleItem, operation: ScheduleOperation) => void;
  conflicts?: Map<string, TeacherScheduleItem[]>;
  readOnly?: boolean;
}

const ScheduleCalendarView = ({
  teacherSchedules,
  onSlotClick,
  conflicts,
  readOnly = false
}: ScheduleCalendarViewProps) => {
  const [currentWeek, setCurrentWeek] = useState<number>(0);
  const [availabilityMap, setAvailabilityMap] = useState<Map<string, TeacherScheduleItem[]>>(new Map());
  
  // Days of the week
  const days = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"];
  
  // Time slots (30-minute intervals from 8:00 to 20:00)
  const startHour = 8;
  const endHour = 20;
  const timeSlots: string[] = [];
  
  for (let hour = startHour; hour < endHour; hour++) {
    timeSlots.push(`${hour.toString().padStart(2, '0')}:00`);
    timeSlots.push(`${hour.toString().padStart(2, '0')}:30`);
  }
  
  // Load teacher availability
  useEffect(() => {
    const loadAvailability = async () => {
      const newAvailabilityMap = new Map<string, TeacherScheduleItem[]>();
      
      for (const teacher of teacherSchedules) {
        try {
          const availability = await getTeacherAvailability(teacher.teacherId);
          newAvailabilityMap.set(teacher.teacherId, availability);
        } catch (error) {
          console.error(`Error loading availability for teacher ${teacher.teacherId}:`, error);
        }
      }
      
      setAvailabilityMap(newAvailabilityMap);
    };
    
    loadAvailability();
  }, [teacherSchedules]);

  // Check if a time is within a schedule slot
  const isWithinScheduleSlot = (day: string, time: string, teacherId: string): TeacherScheduleItem | null => {
    const teacher = teacherSchedules.find(t => t.teacherId === teacherId);
    if (!teacher) return null;
    
    return teacher.schedule.find(slot => {
      if (slot.day !== day) return false;
      
      const slotStartParts = slot.timeStart.split(':');
      const slotEndParts = slot.timeEnd.split(':');
      const timeParts = time.split(':');
      
      const slotStartMinutes = parseInt(slotStartParts[0]) * 60 + parseInt(slotStartParts[1]);
      const slotEndMinutes = parseInt(slotEndParts[0]) * 60 + parseInt(slotEndParts[1]);
      const timeMinutes = parseInt(timeParts[0]) * 60 + parseInt(timeParts[1]);
      
      return timeMinutes >= slotStartMinutes && timeMinutes < slotEndMinutes;
    }) || null;
  };
  
  // Check if a teacher is available at a given time
  const isTeacherAvailable = (day: string, time: string, teacherId: string): boolean => {
    const availability = availabilityMap.get(teacherId);
    if (!availability) return false;
    
    return availability.some(slot => {
      if (slot.day !== day) return false;
      
      const slotStartParts = slot.timeStart.split(':');
      const slotEndParts = slot.timeEnd.split(':');
      const timeParts = time.split(':');
      
      const slotStartMinutes = parseInt(slotStartParts[0]) * 60 + parseInt(slotStartParts[1]);
      const slotEndMinutes = parseInt(slotEndParts[0]) * 60 + parseInt(slotEndParts[1]);
      const timeMinutes = parseInt(timeParts[0]) * 60 + parseInt(timeParts[1]);
      
      return timeMinutes >= slotStartMinutes && timeMinutes < slotEndMinutes;
    });
  };
  
  // Check if a slot has a conflict
  const hasConflict = (teacherId: string, slot: TeacherScheduleItem): boolean => {
    if (!conflicts) return false;
    
    const teacherConflicts = conflicts.get(teacherId);
    if (!teacherConflicts) return false;
    
    return teacherConflicts.some(
      conflictSlot => 
        conflictSlot.day === slot.day && 
        conflictSlot.timeStart === slot.timeStart && 
        conflictSlot.timeEnd === slot.timeEnd
    );
  };
  
  // Generate a color for a teacher (based on teacherId for consistency)
  const getTeacherColor = (teacherId: string): string => {
    const colors = [
      "bg-blue-100 text-blue-800 border-blue-200",
      "bg-green-100 text-green-800 border-green-200",
      "bg-purple-100 text-purple-800 border-purple-200",
      "bg-amber-100 text-amber-800 border-amber-200",
      "bg-pink-100 text-pink-800 border-pink-200",
      "bg-cyan-100 text-cyan-800 border-cyan-200",
      "bg-indigo-100 text-indigo-800 border-indigo-200"
    ];
    
    const index = teacherId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) % colors.length;
    return colors[index];
  };

  // Handle slot click
  const handleSlotClick = (day: string, time: string, teacherId: string) => {
    const slot = isWithinScheduleSlot(day, time, teacherId);
    
    if (slot) {
      onSlotClick(teacherId, slot, "edit");
    } else if (!readOnly) {
      // Create a new slot starting at this time and ending 1 hour later
      const timeParts = time.split(':');
      const hours = parseInt(timeParts[0]);
      const minutes = parseInt(timeParts[1]);
      
      const endHours = minutes === 30 ? hours + 1 : hours;
      const endMinutes = minutes === 30 ? "30" : "00";
      
      const newSlot: TeacherScheduleItem = {
        day,
        timeStart: time,
        timeEnd: `${endHours.toString().padStart(2, '0')}:${endMinutes}`
      };
      
      onSlotClick(teacherId, newSlot, "create");
    }
  };

  return (
    <Card className="col-span-2">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-center">
          <CardTitle className="text-xl">Schedule Calendar</CardTitle>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="icon"
              onClick={() => setCurrentWeek(prev => prev - 1)}
              disabled={currentWeek <= 0}
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <span className="text-sm">Week {currentWeek + 1}</span>
            <Button
              variant="outline"
              size="icon"
              onClick={() => setCurrentWeek(prev => prev + 1)}
            >
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-[auto,repeat(7,1fr)] gap-1 text-xs">
          {/* Header row with days */}
          <div className="sticky top-0 bg-background z-10 h-10 flex items-end font-medium pb-2">
            Time
          </div>
          {days.map(day => (
            <div 
              key={day} 
              className="sticky top-0 bg-background z-10 h-10 flex items-end justify-center font-medium pb-2 border-b"
            >
              {capitalizeFirstLetter(day)}
            </div>
          ))}
          
          {/* Time slots */}
          {timeSlots.map(time => (
            <>
              <div
                key={time}
                className={cn(
                  "pr-2 text-right py-1 h-12 border-r font-medium",
                  time.endsWith(":00") ? "border-b" : ""
                )}
              >
                {time}
              </div>
              {days.map(day => (
                <div
                  key={`${day}-${time}`}
                  className={cn(
                    "relative border border-dashed border-gray-200 h-12 group",
                    time.endsWith(":00") ? "border-b-solid" : ""
                  )}
                >
                  {/* Render schedule slots for each teacher */}
                  <div className="absolute inset-0 flex flex-col gap-0.5 p-0.5">
                    {teacherSchedules.map(teacher => {
                      const slot = isWithinScheduleSlot(day, time, teacher.teacherId);
                      const isStartOfSlot = slot && slot.timeStart === time;
                      
                      if (isStartOfSlot) {
                        // Calculate height based on duration
                        const startParts = slot.timeStart.split(':');
                        const endParts = slot.timeEnd.split(':');
                        const startMinutes = parseInt(startParts[0]) * 60 + parseInt(startParts[1]);
                        const endMinutes = parseInt(endParts[0]) * 60 + parseInt(endParts[1]);
                        const durationMinutes = endMinutes - startMinutes;
                        const durationSlots = durationMinutes / 30; // Each slot is 30 minutes
                        const heightClass = `h-[${durationSlots * 3}rem]`;
                        
                        const hasScheduleConflict = hasConflict(teacher.teacherId, slot);
                        
                        return (
                          <div
                            key={`${teacher.teacherId}-${day}-${time}`}
                            className={cn(
                              "w-full rounded flex flex-col justify-between px-1 py-0.5 cursor-pointer text-xs transition-colors",
                              getTeacherColor(teacher.teacherId),
                              hasScheduleConflict ? "ring-2 ring-red-500" : "",
                              readOnly ? "opacity-80" : "hover:opacity-90"
                            )}
                            style={{ height: `${durationSlots * 3}rem` }}
                            onClick={() => {
                              if (!readOnly) {
                                onSlotClick(teacher.teacherId, slot, "edit");
                              }
                            }}
                          >
                            <div className="font-medium truncate">{teacher.name}</div>
                            <div className="truncate">{`${slot.timeStart} - ${slot.timeEnd}`}</div>
                            {hasScheduleConflict && (
                              <div className="text-red-600 flex items-center mt-1">
                                <AlertCircle className="h-3 w-3 mr-1" />
                                <span>Conflict</span>
                              </div>
                            )}
                          </div>
                        );
                      }
                      return null;
                    })}
                  </div>
                  
                  {/* For creating new slots on empty cells */}
                  {!readOnly && (
                    <div
                      className="absolute inset-0 opacity-0 hover:opacity-20 bg-primary cursor-pointer"
                      onClick={() => {
                        // Find first available teacher to assign this slot to
                        if (teacherSchedules.length > 0) {
                          const availableTeacher = teacherSchedules.find(
                            teacher => isTeacherAvailable(day, time, teacher.teacherId)
                          );
                          
                          if (availableTeacher) {
                            handleSlotClick(day, time, availableTeacher.teacherId);
                          }
                        }
                      }}
                    />
                  )}
                </div>
              ))}
            </>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default ScheduleCalendarView;
