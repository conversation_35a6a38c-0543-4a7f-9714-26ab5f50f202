
import InteractiveChart from "../InteractiveChart";
import DashboardSection from "../DashboardSection";
import MetricCard from "../MetricCard";
import QuickActionButton from "../QuickActionButton";
import { 
  BookOpen, 
  Users, 
  CheckCircle, 
  Calendar, 
  CheckSquare, 
  Edit, 
  FileText,
} from "lucide-react";
import { DashboardData } from "@/services/dashboardService";

interface TeacherDashboardProps {
  dashboardData: Partial<DashboardData>;
  isLoadingData: boolean;
}

const TeacherDashboard = ({ dashboardData, isLoadingData }: TeacherDashboardProps) => {
  return (
    <div className="space-y-6">
      {/* Metric Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <MetricCard 
          title="My Classes" 
          value={dashboardData?.classStats?.activeClasses || 0} // In a real app, this would be filtered to teacher's classes
          icon={BookOpen}
          isLoading={isLoadingData}
        />
        <MetricCard 
          title="Total Students" 
          value={dashboardData?.studentStats?.totalStudents || 0} // In a real app, this would be filtered to teacher's students
          icon={Users}
          isLoading={isLoadingData}
        />
        <MetricCard 
          title="Attendance Rate" 
          value={dashboardData?.attendanceStats?.overallAttendanceRate || 0}
          suffix="%"
          icon={CheckCircle}
          isLoading={isLoadingData}
        />
        <MetricCard 
          title="Lessons This Week" 
          value={5} // Placeholder, would be calculated from actual schedule
          icon={Calendar}
          isLoading={isLoadingData}
        />
      </div>

      {/* Quick Actions */}
      <DashboardSection
        title="Quick Actions"
        description="Common tasks"
        isLoading={isLoadingData}
      >
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <QuickActionButton
            label="Mark Attendance"
            icon={CheckSquare}
            href="/attendance"
          />
          <QuickActionButton
            label="Create Note"
            icon={Edit}
            href="/notes"
          />
          <QuickActionButton
            label="View Schedule"
            icon={Calendar}
            href="/schedule"
          />
          <QuickActionButton
            label="Student Reports"
            icon={FileText}
            href="/reports"
          />
        </div>
      </DashboardSection>

      {/* Attendance Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        <InteractiveChart
          title="Attendance Trend"
          description="Recent attendance statistics"
          data={dashboardData?.attendanceStats?.attendanceTrend || []}
          xKey="date"
          yKeys={[
            { key: "rate", name: "Attendance Rate", color: "#4f46e5" },
          ]}
          type="area"
          isLoading={isLoadingData}
          actions={true}
        />
        <DashboardSection
          title="Low Attendance Students"
          description="Students requiring attention"
          isLoading={isLoadingData}
        >
          <div className="space-y-2">
            {dashboardData?.attendanceStats?.lowAttendingStudents?.map((student, index) => (
              <div key={index} className="flex justify-between items-center p-2 text-sm rounded hover:bg-muted">
                <span>{student.studentName}</span>
                <span className={`font-semibold ${student.attendanceRate < 70 ? 'text-red-500' : 'text-amber-500'}`}>
                  {student.attendanceRate.toFixed(1)}%
                </span>
              </div>
            ))}
          </div>
        </DashboardSection>
      </div>
    </div>
  );
};

export default TeacherDashboard;
