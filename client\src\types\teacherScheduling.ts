
import { TeacherScheduleItem } from "./class";

export interface TeacherScheduleData {
  teacherId: string;
  name: string;
  schedule: TeacherScheduleItem[];
}

export interface TeacherReplacement {
  originalTeacherId: string;
  newTeacherId: string;
  affectedDays: string[];
  reason: string;
  scheduleToReplace?: TeacherScheduleItem[]; // Add missing property
  effectiveDate?: string; // Add missing property
}

export interface TeacherTransition {
  originalTeacherId: string; // Renamed from oldTeacherId for interface but keeping oldTeacherId in implementation
  newTeacherId: string;
  transitionStartDate: string;
  transitionEndDate: string;
  reason: string;
  schedulesToTransfer?: TeacherScheduleItem[]; // Add missing property
  notes?: string; // Add missing property
  notifyStudents?: boolean; // Add missing property
  materialTransfer?: {
    notes: string;
  }; // Add missing property
}

export type ClassOperationStatus = 'idle' | 'loading' | 'success' | 'error';

export interface ScheduleConflict {
  day: string;
  time: string;
  className: string;
}

// Add missing ScheduleOperation type
export type ScheduleOperation = 'create' | 'edit' | 'view';
