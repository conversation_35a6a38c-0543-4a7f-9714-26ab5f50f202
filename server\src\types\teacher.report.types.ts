// server/src/types/teacher.report.types.ts
import { AttendanceStatus } from './attendance.types';
import { NoteCategory } from './notes.types';

// Basic types
export type ReportFormat = 'pdf' | 'excel' | 'csv' | 'json';

export type TeacherReportType = 
    | 'class_attendance'
    | 'student_progress'
    | 'class_behavior'
    | 'makeup_classes';

// Request structure
export interface ReportDateRange {
    startDate: Date;
    endDate: Date;
}

export interface TeacherReportRequestDTO {
    type: TeacherReportType;
    format: ReportFormat;
    dateRange: ReportDateRange;
    classId?: string;
    studentId?: string;
    templateId?: string;
    includeNotes?: boolean;
    includeMakeupClasses?: boolean;
    groupBy?: 'student' | 'date' | 'status';
    filters?: {
        attendanceStatus?: AttendanceStatus[];
        noteTypes?: NoteCategory[];
    };
}

// Report Data Structures
export interface AttendanceReportData {
    className: string;
    period: ReportDateRange;
    totalClasses: number;
    attendanceRecords: Array<{
        date: Date;
        status: AttendanceStatus;
        studentName?: string;
        excuse?: string;
        notes?: string;
    }>;
    summary: {
        present: number;
        absent: number;
        late: number;
        excused: number;
        attendanceRate: number;
    };
}

export interface StudentProgressReportData {
    studentName: string;
    period: ReportDateRange;
    attendanceSummary: {
        totalClasses: number;
        attended: number;
        attendanceRate: number;
    };
    behaviorNotes: Array<{
        date: Date;
        type: NoteCategory;
        content: string;
    }>;
    makeupClasses?: Array<{
        date: Date;
        status: AttendanceStatus;
        completed: boolean;
    }>;
}

export interface ClassBehaviorReportData {
    classId: string;
    className: string;
    period: ReportDateRange;
    behaviorNotes: Array<{
        studentName: string;
        notes: Array<{
            date: Date;
            content: string;
            visibility: string;
        }>;
    }>;
}

export interface MakeupClassesReportData {
    period: ReportDateRange;
    makeupClasses: Array<{
        date: Date;
        className: string;
        students: Array<{
            studentName: string;
            status: AttendanceStatus;
            notes?: string;
        }>;
    }>;
}

// Template Configuration
export interface ReportTemplateConfig {
    name: string;
    type: TeacherReportType;
    config: {
        header: {
            logo?: boolean;
            title: string;
            subtitle?: string;
            date: boolean;
        };
        content: {
            sections: Array<{
                type: 'table' | 'chart' | 'summary' | 'notes';
                title?: string;
                data: string[];
            }>;
        };
        footer: {
            pageNumbers: boolean;
            signature?: boolean;
            notes?: string;
        };
    };
}