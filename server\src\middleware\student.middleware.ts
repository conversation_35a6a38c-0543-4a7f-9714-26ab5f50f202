// server/src/middleware/student.middleware.ts
import { Request, Response, NextFunction } from 'express';
import rateLimit from 'express-rate-limit';
import { AppError } from '../types/error.types';
import { SystemLogger } from '../services/logger.service';

// Rate limiting for student registration
export const studentRegistrationLimiter = rateLimit({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 50, // max 50 registrations per hour
    message: 'Too many student registrations from this IP, please try again after an hour'
});

// Rate limiting for student operations
export const studentOperationsLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // max 100 operations per 15 minutes
    message: 'Too many operations, please try again after 15 minutes'
});

// Middleware to check if student exists
export const checkStudentExists = async (req: Request, res: Response, next: NextFunction) => {
    try {
        const studentId = req.params.id;
        if (!studentId.match(/^[0-9a-fA-F]{24}$/)) {
            throw new AppError(400, 'Invalid student ID format');
        }

        // Log student access
        await SystemLogger.log({
            severity: 'info',
            category: 'student_management',
            action: 'access_check',
            performedBy: req.user?._id?.toString() || 'system',
            targetId: studentId,
            details: {
                method: req.method,
                path: req.path
            },
            status: 'success',
            timestamp: new Date()
        });

        next();
    } catch (error) {
        next(error);
    }
};

// Middleware to check class capacity before adding student
export const checkClassCapacity = async (req: Request, res: Response, next: NextFunction) => {
    try {
        if (req.body.currentClass) {
            const classId = req.body.currentClass;
            const Class = req.app.locals.models.Class; // Assuming you store models in app.locals
            const targetClass = await Class.findById(classId);
            
            if (!targetClass) {
                throw new AppError(404, 'Class not found');
            }

            if (targetClass.currentStudentCount >= targetClass.capacity) {
                throw new AppError(400, 'Class has reached maximum capacity');
            }
        }
        next();
    } catch (error) {
        next(error);
    }
};