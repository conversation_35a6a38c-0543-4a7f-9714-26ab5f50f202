// server/src/scripts/seedDatabase.ts
import mongoose from 'mongoose';
import { User } from '../models/user.model';
import { AuthService } from '../services/auth.service';
import { DB_CONFIG } from '../config/database.config';

async function seedInitialUsers() {
    try {
        // Connect to database
        await mongoose.connect(DB_CONFIG.URI);
        console.log('Connected to database');

        // Create system ID that will be used as both createdBy and changedBy
        const systemId = new mongoose.Types.ObjectId();

        // Define initial users for local network deployment
        const initialUsers = [
            {
                username: 'admin@local',
                password: 'admin',
                role: 'superAdmin',
                name: 'Admin User'
            },
            {
                username: 'manager@local',
                password: 'manager',
                role: 'manager',
                name: 'Manager User'
            },
            {
                username: 'secretary@local',
                password: 'secretary',
                role: 'secretary',
                name: 'Secretary User'
            },
            {
                username: 'teacher@local',
                password: 'teacher',
                role: 'teacher',
                name: 'Teacher User'
            }
        ];

        console.log('Creating initial users for local deployment...');

        for (const userData of initialUsers) {
            // Check if user already exists
            const existingUser = await User.findOne({ username: userData.username });
            if (existingUser) {
                console.log(`User ${userData.username} already exists, skipping...`);
                continue;
            }

            // Hash password
            const hashedPassword = await AuthService.hashPassword(userData.password);

            // Create user
            const user = new User({
                username: userData.username,
                password: hashedPassword,
                role: userData.role,
                status: 'active',
                createdBy: systemId,
                createdAt: new Date(),
                modifiedAt: new Date(),
                roleHistory: [{
                    role: userData.role,
                    changedAt: new Date(),
                    changedBy: systemId,
                    reason: 'Initial system setup'
                }],
                loginAttempts: []
            });

            await user.save();
            console.log(`✅ Created ${userData.role}: ${userData.username}/${userData.password}`);
        }

        console.log('\n🎉 Initial users created successfully!');
        console.log('\n📋 Login Credentials (EMAIL/PASSWORD):');
        console.log('Admin: admin@local / admin');
        console.log('Manager: manager@local / manager');
        console.log('Secretary: secretary@local / secretary');
        console.log('Teacher: teacher@local / teacher');

    } catch (error) {
        console.error('Error seeding database:', error);
        if (error instanceof Error) {
            console.error('Error details:', error.message);
        }
    } finally {
        // Always disconnect
        await mongoose.disconnect();
        console.log('Disconnected from database');
    }
}

// Run the seeding function
seedInitialUsers();