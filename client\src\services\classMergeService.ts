import { Class, ClassApiResponse } from "@/types/class";
import { ClassMergeRequest, ClassSplitRequest, MergeCompatibilityResult } from "@/types/classMerge";
import { toast } from "@/hooks/use-toast";

// Merge two classes
export const mergeClasses = async (
  sourceClassId: string,
  mergeData: ClassMergeRequest
): Promise<ClassApiResponse> => {
  try {
    console.log(`Merging class ${sourceClassId} with ${mergeData.targetClassId}`);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // In a real app, this would be an API call
    // POST /api/classes/:id/merge
    
    // Mock response for now
    const mergedClass: Class = {
      id: mergeData.targetClassId,
      name: "Merged Class Example",
      level: "Intermediate",
      teachers: [
        {
          id: "teacher1",
          name: "<PERSON>",
          schedule: []
        },
        {
          id: "teacher2",
          name: "<PERSON>",
          schedule: []
        }
      ],
      room: "Room B202",
      capacity: {
        total: 40,
        current: 35,
        available: 5
      },
      schedule: {
        startDate: "2023-09-01",
        endDate: "2023-12-15"
      },
      status: "active"
    };
    
    return {
      success: true,
      data: mergedClass
    };
  } catch (error) {
    console.error("Error merging classes:", error);
    throw error;
  }
};

// Split a class into two
export const splitClass = async (
  sourceClassId: string,
  splitData: ClassSplitRequest
): Promise<ClassApiResponse> => {
  try {
    console.log(`Splitting class ${sourceClassId}`, splitData);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // In a real app, this would be an API call
    // POST /api/classes/:id/split
    
    // Mock response for now - return the newly created class
    const newClass: Class = {
      id: `new-class-${Date.now()}`,
      name: splitData.name,
      level: splitData.level,
      teachers: splitData.teacherAssignments.map(ta => ({
        id: ta.teacherId,
        name: "New Teacher", // This would come from the API
        schedule: ta.schedule
      })),
      room: splitData.room,
      capacity: {
        total: splitData.capacity,
        current: splitData.studentIds.length,
        available: splitData.capacity - splitData.studentIds.length
      },
      schedule: {
        startDate: "2023-09-01", // Would be inherited from source class
        endDate: "2023-12-15"    // Would be inherited from source class
      },
      status: "active"
    };
    
    return {
      success: true,
      data: newClass
    };
  } catch (error) {
    console.error("Error splitting class:", error);
    throw error;
  }
};

// Check if two classes are compatible for merging
export const checkMergeCompatibility = async (
  sourceClassId: string,
  targetClassId: string
): Promise<MergeCompatibilityResult> => {
  try {
    console.log(`Checking merge compatibility between ${sourceClassId} and ${targetClassId}`);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Mock response
    // In a real app, this would check level, capacity, schedules, etc.
    
    // For demo purposes, return compatibility issues for specific class combinations
    if (sourceClassId === "class1" && targetClassId === "class3") {
      return {
        compatible: false,
        issues: [
          "Target class is not active",
          "Class levels do not match"
        ]
      };
    } else if (sourceClassId === "class1" && targetClassId === "class2") {
      return {
        compatible: false,
        issues: [
          "Combined student count exceeds capacity"
        ],
        mergedCapacity: {
          total: 25,
          current: 30,
          available: -5
        }
      };
    }
    
    // Default to compatible
    return {
      compatible: true,
      issues: [],
      mergedCapacity: {
        total: 40,
        current: 30,
        available: 10
      }
    };
  } catch (error) {
    console.error("Error checking merge compatibility:", error);
    throw error;
  }
};
