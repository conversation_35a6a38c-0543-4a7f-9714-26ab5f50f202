
import { useState } from "react";
import { toast } from "sonner";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Separator } from "@/components/ui/separator";
import { 
  Calendar, 
  User, 
  BookOpen, 
  Eye, 
  Tag, 
  Pencil, 
  Trash, 
  AlertCircle,
  Download,
  Paperclip
} from "lucide-react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { getCurrentUser } from "@/lib/auth";
import { canEditNote, canDeleteNote } from "@/lib/noteUtils";

interface NoteDetailDialogProps {
  note: any; // The note object from API
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onNoteUpdated: () => void;
}

const NoteDetailDialog = ({ note, open, onOpenChange, onNoteUpdated }: NoteDetailDialogProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editContent, setEditContent] = useState(note.content);
  const [isDeleting, setIsDeleting] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const currentUser = getCurrentUser();
  
  // Check if current user can edit or delete the note
  const canEdit = currentUser && canEditNote(
    currentUser.role, 
    note.createdBy.id, 
    currentUser.id
  );
  
  const canDelete = currentUser && canDeleteNote(
    currentUser.role, 
    note.createdBy.id, 
    currentUser.id
  );
  
  const handleSaveEdit = async () => {
    if (!editContent.trim()) {
      toast.error("Note content cannot be empty");
      return;
    }
    
    try {
      setIsSubmitting(true);
      
      // Mock API call to update note
      await new Promise(resolve => setTimeout(resolve, 800));
      
      toast.success("Note updated successfully");
      setIsEditing(false);
      onNoteUpdated();
    } catch (error) {
      toast.error("Failed to update note");
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  };
  
  const handleDelete = async () => {
    try {
      setIsSubmitting(true);
      
      // Mock API call to delete note
      await new Promise(resolve => setTimeout(resolve, 800));
      
      toast.success("Note deleted successfully");
      onOpenChange(false);
      onNoteUpdated();
    } catch (error) {
      toast.error("Failed to delete note");
      console.error(error);
    } finally {
      setIsSubmitting(false);
      setIsDeleting(false);
    }
  };
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl overflow-hidden">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between">
            <span>{note.title}</span>
            <div className="flex space-x-2">
              {canEdit && !isEditing && (
                <Button variant="outline" size="sm" onClick={() => setIsEditing(true)}>
                  <Pencil className="h-4 w-4 mr-1" />
                  Edit
                </Button>
              )}
              {canDelete && (
                <AlertDialog open={isDeleting} onOpenChange={setIsDeleting}>
                  <AlertDialogTrigger asChild>
                    <Button variant="destructive" size="sm">
                      <Trash className="h-4 w-4 mr-1" />
                      Delete
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent>
                    <AlertDialogHeader>
                      <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                      <AlertDialogDescription>
                        This action cannot be undone. This will permanently delete this note.
                      </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                      <AlertDialogCancel>Cancel</AlertDialogCancel>
                      <AlertDialogAction onClick={handleDelete} disabled={isSubmitting}>
                        {isSubmitting ? "Deleting..." : "Delete"}
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              )}
            </div>
          </DialogTitle>
          <DialogDescription>
            Created by {note.createdBy.name} on {new Date(note.createdAt).toLocaleDateString()}
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <Tabs defaultValue="content">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="content">Content</TabsTrigger>
              <TabsTrigger value="details">Details</TabsTrigger>
            </TabsList>
            
            <TabsContent value="content" className="space-y-4 pt-4">
              {isEditing ? (
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="content">Edit Note</Label>
                    <Textarea
                      id="content"
                      value={editContent}
                      onChange={(e) => setEditContent(e.target.value)}
                      className="min-h-[200px]"
                    />
                  </div>
                  <div className="flex justify-end space-x-2">
                    <Button variant="outline" onClick={() => {
                      setIsEditing(false);
                      setEditContent(note.content);
                    }}>
                      Cancel
                    </Button>
                    <Button onClick={handleSaveEdit} disabled={isSubmitting}>
                      {isSubmitting ? "Saving..." : "Save Changes"}
                    </Button>
                  </div>
                </div>
              ) : (
                <div className="prose prose-sm dark:prose-invert max-w-none">
                  <p>{note.content}</p>
                  
                  {note.attachments && note.attachments.length > 0 && (
                    <div className="mt-6">
                      <h4 className="text-sm font-medium flex items-center mb-2">
                        <Paperclip className="h-4 w-4 mr-1" />
                        Attachments
                      </h4>
                      <div className="space-y-2">
                        {note.attachments.map((attachment: any, index: number) => (
                          <div key={index} className="flex items-center p-2 border rounded">
                            <span className="truncate">{attachment.name}</span>
                            <Button variant="ghost" size="sm" className="ml-auto">
                              <Download className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="details" className="space-y-4 pt-4">
              <div className="space-y-3">
                <div className="flex items-start">
                  <User className="h-4 w-4 mr-2 mt-0.5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Created By</p>
                    <p className="text-sm text-muted-foreground">
                      {note.createdBy.name} ({note.createdBy.role})
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <Calendar className="h-4 w-4 mr-2 mt-0.5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Date Created</p>
                    <p className="text-sm text-muted-foreground">
                      {new Date(note.createdAt).toLocaleString()}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <Tag className="h-4 w-4 mr-2 mt-0.5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Type</p>
                    <p className="text-sm text-muted-foreground capitalize">
                      {note.type}
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <Eye className="h-4 w-4 mr-2 mt-0.5 text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Visibility</p>
                    <p className="text-sm text-muted-foreground capitalize">
                      {note.visibility}
                    </p>
                  </div>
                </div>
                
                {note.student && (
                  <div className="flex items-start">
                    <User className="h-4 w-4 mr-2 mt-0.5 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">Student</p>
                      <p className="text-sm text-muted-foreground">
                        {note.student.name}
                      </p>
                    </div>
                  </div>
                )}
                
                {note.relatedClass && (
                  <div className="flex items-start">
                    <BookOpen className="h-4 w-4 mr-2 mt-0.5 text-muted-foreground" />
                    <div>
                      <p className="text-sm font-medium">Class</p>
                      <p className="text-sm text-muted-foreground">
                        {note.relatedClass.name}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </TabsContent>
          </Tabs>
        </div>
        
        <DialogFooter className="gap-2 sm:gap-0">
          {!isEditing && (
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Close
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default NoteDetailDialog;
