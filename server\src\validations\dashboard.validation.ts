// server/src/validations/dashboard.validation.ts
import Joi from 'joi';

export const dashboardValidation = {
    getDashboardData: Joi.object({
        fromDate: Joi.date().iso(),
        toDate: Joi.date().iso().min(Joi.ref('fromDate'))
    }),

    getStatistics: Joi.object({
        fromDate: Joi.date().iso(),
        toDate: Joi.date().iso().min(Joi.ref('fromDate')),
        level: Joi.string().min(1).max(50)
    }),

    getPaymentStatistics: Joi.object({
        startDate: Joi.date().iso(),
        endDate: Joi.date().iso().min(Joi.ref('startDate'))
    }),

    getRoomUtilization: Joi.object({
        startDate: Joi.date().iso(),
        endDate: Joi.date().iso().min(Joi.ref('startDate'))
    }),

    getSystemActivity: Joi.object({
        fromDate: Joi.date().iso(),
        toDate: Joi.date().iso().min(Joi.ref('fromDate')),
        limit: Joi.number().integer().min(1).max(100)
    })
};
