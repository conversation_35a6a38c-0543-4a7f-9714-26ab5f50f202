// server/src/routes/secretary.report.routes.ts
import express from 'express';
import { Secretary<PERSON>eportController } from '../controllers/secretary.report.controller';
import { authenticateToken, authorizeRoles } from '../middleware/auth.middleware';
import { validate } from '../middleware/validate.middleware';
import { secretaryReportValidation } from '../validations/secretary.report.validation';
import { catchAsync } from '../middleware/error.middleware';
import { apiLimiter } from '../middleware/rateLimiter.middleware';

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Apply secretary role authorization
router.use(authorizeRoles('secretary'));

// Get report options
router.get(
    '/options',
    catchAsync(SecretaryReportController.getReportOptions)
);

// Validate report request
router.post(
    '/validate',
    validate(secretaryReportValidation.generateReport),
    catchAsync(SecretaryReportController.validateReportRequest)
);

// Preview report
router.post(
    '/preview',
    validate(secretaryReportValidation.generateReport),
    apiLimiter,
    catchAsync(SecretaryReportController.previewReport)
);

// Generate and download report
router.post(
    '/generate',
    validate(secretaryReportValidation.generateReport),
    apiLimiter,
    catchAsync(SecretaryReportController.generateReport)
);

export default router;