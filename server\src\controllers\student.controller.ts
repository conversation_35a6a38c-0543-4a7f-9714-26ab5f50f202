// server/src/controllers/student.controller.ts
import { Request, Response } from 'express';
import { StudentService } from '../services/student.service';
import { AppError } from '../types/error.types';
import { StudentUpdateDTO, PaymentRecordDTO, StudentTransferDTO, StudentBulkOperationDTO, StudentExportOptions } from '../types/student.types';

export class StudentController {
    static async getStudents(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const {
            page,
            limit,
            sortBy,
            sortOrder,
            status,
            level,
            search,
            classId,
            fromDate,
            toDate,
            paymentStatus
        } = req.query;

        const options = {
            page: page ? parseInt(page as string) : undefined,
            limit: limit ? parseInt(limit as string) : undefined,
            sortBy: sortBy as string,
            sortOrder: sortOrder as 'asc' | 'desc',
            status: status as 'active' | 'inactive',
            level: level as string,
            search: search as string,
            classId: classId as string,
            fromDate: fromDate ? new Date(fromDate as string) : undefined,
            toDate: toDate ? new Date(toDate as string) : undefined,
            paymentStatus: paymentStatus as 'paid' | 'pending' | 'overdue'
        };

        const result = await StudentService.getStudents(options, currentUser._id.toString());

        res.json({
            success: true,
            data: result.students,
            pagination: result.pagination
        });
    }

    static async getStudentById(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { id } = req.params;
        const student = await StudentService.getStudentById(id, currentUser._id.toString());

        res.json({
            success: true,
            data: student
        });
    }

    static async createStudent(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const studentData = req.body;
        const student = await StudentService.createStudent(
            studentData,
            currentUser._id.toString()
        );

        res.status(201).json({
            success: true,
            message: 'Student created successfully',
            data: student
        });
    }

    static async updateStudent(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { id } = req.params;
        const updateData: StudentUpdateDTO = req.body;

        // Add version check
        const versionCheck = req.versionControl?.enabled && req.versionControl.currentVersion !== undefined
            ? { version: req.versionControl.currentVersion }
            : undefined;

        const student = await StudentService.updateStudent(
            id,
            updateData,
            currentUser._id.toString(),
            versionCheck
        );

        res.json({
            success: true,
            message: 'Student updated successfully',
            data: student
        });
    }

    static async getLevelMismatches(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const mismatches = await StudentService.identifyLevelMismatches();

        res.json({
            success: true,
            data: mismatches
        });
    }

    static async getSuggestedClasses(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { id } = req.params;
        const suggestedClasses = await StudentService.getSuggestedClasses(id);

        res.json({
            success: true,
            data: suggestedClasses
        });
    }


    static async recordPayment(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { id } = req.params;
        const paymentData: PaymentRecordDTO = req.body;

        const student = await StudentService.recordPayment(
            id,
            paymentData,
            currentUser._id.toString()
        );

        res.json({
            success: true,
            message: 'Payment recorded successfully',
            data: student
        });
    }

    static async transferClass(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { id } = req.params;
        const transferData: StudentTransferDTO = req.body;

        const student = await StudentService.transferClass(
            id,
            transferData,
            currentUser._id.toString()
        );

        res.json({
            success: true,
            message: 'Student transferred successfully',
            data: student
        });
    }

    static async bulkOperation(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const operationData: StudentBulkOperationDTO = req.body;

        const result = await StudentService.bulkOperation(
            operationData,
            currentUser._id.toString()
        );

        res.json({
            success: true,
            message: 'Bulk operation completed',
            data: result
        });
    }

    static async exportStudents(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const {
            format = 'csv',
            fields,
            includePaymentHistory,
            includeClassHistory,
            fromDate,
            toDate
        } = req.query;

        const options: StudentExportOptions = {
            format: format as 'csv' | 'json',
            fields: fields ? (fields as string).split(',') : undefined,
            includePaymentHistory: includePaymentHistory === 'true',
            includeClassHistory: includeClassHistory === 'true',
            dateRange: fromDate && toDate ? {
                start: new Date(fromDate as string),
                end: new Date(toDate as string)
            } : undefined
        };

        const exportData = await StudentService.exportStudents(
            options,
            currentUser._id.toString()
        );

        const filename = `students_export_${new Date().toISOString()}.${format}`;
        res.setHeader('Content-Disposition', `attachment; filename=${filename}`);
        res.setHeader('Content-Type', format === 'csv' ? 'text/csv' : 'application/json');
        res.send(exportData);
    }

    static async archiveStudent(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { id } = req.params;
        const archiveData = await StudentService.archiveStudent(
            id,
            currentUser._id.toString()
        );

        res.json({
            success: true,
            message: 'Student archived successfully',
            data: archiveData
        });
    }

    static async restoreStudent(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { id } = req.params;
        const restoredStudent = await StudentService.restoreStudent(
            id,
            currentUser._id.toString()
        );

        res.json({
            success: true,
            message: 'Student restored successfully',
            data: restoredStudent
        });
    }

    static async validateStudentEmail(req: Request, res: Response) {
        const { email } = req.query;

        if (!email) {
            throw new AppError(400, 'Email is required');
        }

        const exists = await StudentService.checkEmailExists(email as string);

        res.json({
            success: true,
            data: { exists }
        });
    }

    static async getStudentPaymentHistory(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { id } = req.params;
        const {
            fromDate,
            toDate,
            page = '1',
            limit = '10'
        } = req.query;

        const options = {
            fromDate: fromDate ? new Date(fromDate as string) : undefined,
            toDate: toDate ? new Date(toDate as string) : undefined,
            page: parseInt(page as string),
            limit: parseInt(limit as string)
        };

        const paymentHistory = await StudentService.getPaymentHistory(
            id,
            options,
            currentUser._id.toString()
        );

        res.json({
            success: true,
            data: paymentHistory
        });
    }

    static async getStudentClassHistory(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { id } = req.params;
        const {
            fromDate,
            toDate,
            page = '1',
            limit = '10'
        } = req.query;

        const options = {
            fromDate: fromDate ? new Date(fromDate as string) : undefined,
            toDate: toDate ? new Date(toDate as string) : undefined,
            page: parseInt(page as string),
            limit: parseInt(limit as string)
        };

        const classHistory = await StudentService.getClassHistory(
            id,
            options,
            currentUser._id.toString()
        );

        res.json({
            success: true,
            data: classHistory
        });
    }
}