
import { Card, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { LucideIcon } from "lucide-react";

interface MetricCardProps {
  title: string;
  value: number | string;
  icon: LucideIcon;
  prefix?: string;
  suffix?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  isLoading?: boolean;
  className?: string;
}

const MetricCard = ({
  title,
  value,
  icon: Icon,
  prefix = "",
  suffix = "",
  trend,
  isLoading = false,
  className,
}: MetricCardProps) => {
  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
        <Icon className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <Skeleton className="h-7 w-3/4" />
        ) : (
          <div className="text-2xl font-bold">
            {prefix}
            {typeof value === 'number' ? value.toLocaleString() : value}
            {suffix}
          </div>
        )}
        
        {trend && !isLoading && (
          <p className="text-xs text-muted-foreground flex items-center mt-1">
            <span
              className={trend.isPositive ? "text-green-500" : "text-red-500"}
            >
              {trend.isPositive ? "↑" : "↓"} {trend.value.toFixed(1)}%
            </span>
            <span className="ml-1">from previous period</span>
          </p>
        )}
      </CardContent>
    </Card>
  );
};

export default MetricCard;
