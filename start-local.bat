@echo off
echo Starting Vertex Education Management System for Local Network...
echo.

echo Starting Backend Server...
cd server
start "Backend Server" cmd /k "npm run dev"
cd ..

echo Waiting for backend to start...
timeout /t 5 /nobreak > nul

echo Starting Frontend Application...
cd client
start "Frontend App" cmd /k "npm run dev"
cd ..

echo.
echo ========================================
echo Vertex Education Management System
echo ========================================
echo Backend: http://localhost:3000
echo Frontend: http://localhost:5173
echo.
echo Access from other devices on your network:
echo Replace 'localhost' with your computer's IP address
echo Example: http://*************:5173
echo.
echo Simple Login Credentials:
echo Admin: admin@local / admin
echo Manager: manager@local / manager  
echo Secretary: secretary@local / secretary
echo Teacher: teacher@local / teacher
echo.
echo Press any key to close this window...
pause > nul
