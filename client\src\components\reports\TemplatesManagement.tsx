
import { useState } from "react";
import { 
  <PERSON>, 
  CardContent, 
  CardDescription, 
  <PERSON><PERSON><PERSON>er, 
  Card<PERSON><PERSON>er, 
  CardTitle 
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ReportTemplate } from "@/types/reports";
import { Badge } from "@/components/ui/badge";
import { 
  FileText, 
  Copy, 
  Edit, 
  Trash2, 
  Share2,
  Clock,
  User,
  FilePlus,
  LayoutTemplate,
  FileSpreadsheet,
  ListChecks,
  FileType
} from "lucide-react";
import { formatDistance } from "date-fns";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { useToast } from "@/components/ui/use-toast";
import {
  Select,
  SelectContent,
  <PERSON>I<PERSON>,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";

interface TemplatesManagementProps {
  templates: ReportTemplate[];
  onUseTemplate: (templateId: string) => void;
  onUpdateTemplate?: (template: ReportTemplate) => void;
  onDeleteTemplate?: (templateId: string) => void;
}

export default function TemplatesManagement({
  templates,
  onUseTemplate,
  onUpdateTemplate,
  onDeleteTemplate
}: TemplatesManagementProps) {
  const { toast } = useToast();
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isShareDialogOpen, setIsShareDialogOpen] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<ReportTemplate | null>(null);
  const [deletingTemplateId, setDeletingTemplateId] = useState<string | null>(null);
  const [sharingTemplateId, setSharingTemplateId] = useState<string | null>(null);
  const [activeView, setActiveView] = useState<"grid" | "list">("grid");
  
  const [templateForm, setTemplateForm] = useState({
    name: "",
    description: "",
    isShared: false
  });

  const handleEditTemplate = (template: ReportTemplate) => {
    setEditingTemplate(template);
    setTemplateForm({
      name: template.name,
      description: template.description || "",
      isShared: template.isShared
    });
    setIsEditDialogOpen(true);
  };

  const handleDeleteTemplate = (templateId: string) => {
    setDeletingTemplateId(templateId);
    setIsDeleteDialogOpen(true);
  };

  const handleShareTemplate = (templateId: string) => {
    setSharingTemplateId(templateId);
    setIsShareDialogOpen(true);
  };

  const confirmDeleteTemplate = () => {
    if (!deletingTemplateId || !onDeleteTemplate) return;
    
    onDeleteTemplate(deletingTemplateId);
    setIsDeleteDialogOpen(false);
    setDeletingTemplateId(null);
    
    toast({
      title: "Template deleted",
      description: "The report template has been deleted."
    });
  };

  const handleSaveEdit = () => {
    if (!editingTemplate || !onUpdateTemplate) return;

    onUpdateTemplate({
      ...editingTemplate,
      name: templateForm.name,
      description: templateForm.description,
      isShared: templateForm.isShared
    });
    
    setIsEditDialogOpen(false);
    setEditingTemplate(null);
    
    toast({
      title: "Template updated",
      description: "The report template has been updated."
    });
  };

  const handleDuplicateTemplate = (template: ReportTemplate) => {
    // In a real implementation, this would call the API to duplicate the template
    toast({
      title: "Template duplicated",
      description: "Template duplication functionality will be implemented here."
    });
  };

  const handleShareConfirm = () => {
    // In a real implementation, this would handle template sharing
    setIsShareDialogOpen(false);
    setSharingTemplateId(null);
    
    toast({
      title: "Template shared",
      description: "The template has been shared successfully."
    });
  };

  // Get the appropriate icon for a report type
  const getTemplateIcon = (template: ReportTemplate) => {
    const type = template.reportType;
    
    if (type.includes('attendance')) return <FileSpreadsheet className="h-5 w-5 text-primary" />;
    if (type.includes('progress')) return <ListChecks className="h-5 w-5 text-primary" />;
    if (type.includes('payment')) return <FileType className="h-5 w-5 text-primary" />;
    return <LayoutTemplate className="h-5 w-5 text-primary" />;
  };

  const formatReportType = (type: string) => {
    return type.split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ');
  };

  // Filter templates by category
  const teacherTemplates = templates.filter(t => 
    ['class_attendance', 'student_progress', 'class_behavior', 'makeup_classes'].includes(t.reportType)
  );
  
  const secretaryTemplates = templates.filter(t => 
    ['student_registration', 'payment_collection', 'class_capacity', 'student_attendance', 'payment_due'].includes(t.reportType)
  );
  
  const managerTemplates = templates.filter(t => 
    ['financial_summary', 'performance_metrics', 'enrollment_trends', 'teacher_statistics'].includes(t.reportType)
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Tabs defaultValue="all" className="w-[400px]">
            <TabsList>
              <TabsTrigger value="all">All Templates</TabsTrigger>
              <TabsTrigger value="teacher">Teacher</TabsTrigger>
              <TabsTrigger value="secretary">Secretary</TabsTrigger>
              <TabsTrigger value="manager">Manager</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" onClick={() => setActiveView("grid")}>
            Grid
          </Button>
          <Button variant="outline" size="sm" onClick={() => setActiveView("list")}>
            List
          </Button>
          <Button variant="outline" size="sm">
            <FilePlus className="h-4 w-4 mr-2" />
            Create Template
          </Button>
        </div>
      </div>
      
      {activeView === "grid" ? (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {templates.map(template => (
            <Card key={template.id} className="group">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div className="flex items-center gap-2">
                    {getTemplateIcon(template)}
                    <CardTitle className="text-base">{template.name}</CardTitle>
                  </div>
                  
                  <Badge variant="outline" className="capitalize">
                    {formatReportType(template.reportType)}
                  </Badge>
                </div>
                
                <CardDescription className="line-clamp-2">
                  {template.description || "No description provided."}
                </CardDescription>
              </CardHeader>
              
              <CardContent className="pb-2">
                <div className="flex flex-col space-y-1 text-sm">
                  <div className="flex items-center gap-1 text-muted-foreground">
                    <Clock className="h-3.5 w-3.5" />
                    <span>
                      Created {formatDistance(new Date(template.createdAt), new Date(), { addSuffix: true })}
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-1 text-muted-foreground">
                    <User className="h-3.5 w-3.5" />
                    <span>By {template.createdBy}</span>
                  </div>
                </div>
              </CardContent>
              
              <CardFooter className="pt-1 flex justify-between">
                <Button 
                  variant="default" 
                  size="sm"
                  onClick={() => onUseTemplate(template.id)}
                >
                  Use Template
                </Button>
                
                <div className="flex opacity-0 group-hover:opacity-100 transition-opacity">
                  <Button 
                    variant="ghost" 
                    size="icon"
                    onClick={() => handleDuplicateTemplate(template)}
                    title="Duplicate template"
                  >
                    <Copy className="h-4 w-4" />
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="icon"
                    onClick={() => handleEditTemplate(template)}
                    title="Edit template"
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="icon"
                    onClick={() => handleShareTemplate(template.id)}
                    title="Share template"
                  >
                    <Share2 className="h-4 w-4" />
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="icon"
                    onClick={() => handleDeleteTemplate(template.id)}
                    title="Delete template"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <div className="border rounded-md">
          <table className="w-full">
            <thead>
              <tr className="border-b bg-muted/50">
                <th className="text-left p-3 font-medium">Name</th>
                <th className="text-left p-3 font-medium">Type</th>
                <th className="text-left p-3 font-medium">Created</th>
                <th className="text-left p-3 font-medium">Created By</th>
                <th className="text-left p-3 font-medium">Format</th>
                <th className="text-left p-3 font-medium">Actions</th>
              </tr>
            </thead>
            <tbody>
              {templates.map(template => (
                <tr key={template.id} className="border-b hover:bg-muted/50">
                  <td className="p-3">
                    <div className="flex items-center gap-2">
                      {getTemplateIcon(template)}
                      <span>{template.name}</span>
                    </div>
                  </td>
                  <td className="p-3">
                    <Badge variant="outline" className="capitalize">
                      {formatReportType(template.reportType)}
                    </Badge>
                  </td>
                  <td className="p-3 text-sm text-muted-foreground">
                    {formatDistance(new Date(template.createdAt), new Date(), { addSuffix: true })}
                  </td>
                  <td className="p-3 text-sm">{template.createdBy}</td>
                  <td className="p-3">
                    <Badge>{template.format.toUpperCase()}</Badge>
                  </td>
                  <td className="p-3">
                    <div className="flex space-x-1">
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => onUseTemplate(template.id)}
                      >
                        Use
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="icon"
                        onClick={() => handleEditTemplate(template)}
                      >
                        <Edit className="h-4 w-4" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="icon"
                        onClick={() => handleDeleteTemplate(template.id)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
      
      {templates.length === 0 && (
        <div className="flex flex-col items-center justify-center py-8 bg-muted/20 rounded-md">
          <LayoutTemplate className="h-10 w-10 text-muted-foreground mb-3" />
          <p className="text-muted-foreground mb-4">No templates found</p>
          <Button variant="outline">Create Your First Template</Button>
        </div>
      )}

      {/* Edit Template Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Template</DialogTitle>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="templateName">Name</Label>
              <Input
                id="templateName"
                value={templateForm.name}
                onChange={(e) => setTemplateForm({ ...templateForm, name: e.target.value })}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="templateDescription">Description</Label>
              <Textarea
                id="templateDescription"
                value={templateForm.description}
                onChange={(e) => setTemplateForm({ ...templateForm, description: e.target.value })}
                rows={3}
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch
                id="templateShared"
                checked={templateForm.isShared}
                onCheckedChange={(checked) => setTemplateForm({ ...templateForm, isShared: checked })}
              />
              <Label htmlFor="templateShared">Share with other users</Label>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleSaveEdit}>Save Changes</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Template Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this template? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>Cancel</Button>
            <Button variant="destructive" onClick={confirmDeleteTemplate}>Delete Template</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Share Template Dialog */}
      <Dialog open={isShareDialogOpen} onOpenChange={setIsShareDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Share Template</DialogTitle>
            <DialogDescription>
              Choose users or roles to share this template with.
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="shareWithRole">Share with role</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Select role" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="teacher">All Teachers</SelectItem>
                  <SelectItem value="secretary">All Secretaries</SelectItem>
                  <SelectItem value="manager">All Managers</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="shareWithUsers">Or share with specific users</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Select users" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="user1">John Doe</SelectItem>
                  <SelectItem value="user2">Jane Smith</SelectItem>
                  <SelectItem value="user3">Robert Johnson</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="shareMessage">Message (optional)</Label>
              <Textarea
                id="shareMessage"
                placeholder="Add a note about this template..."
                rows={2}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsShareDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleShareConfirm}>Share Template</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
