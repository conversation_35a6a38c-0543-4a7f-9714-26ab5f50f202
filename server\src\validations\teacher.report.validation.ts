// server/src/validations/teacher.report.validation.ts
import <PERSON><PERSON> from 'joi';

export const reportValidation = {
    // Report generation and preview
    generateTeacherReport: Joi.object({
        type: Joi.string()
            .valid('class_attendance', 'student_progress', 'class_behavior', 'makeup_classes')
            .required()
            .messages({
                'any.only': 'Invalid report type',
                'any.required': 'Report type is required'
            }),
        format: Joi.string()
            .valid('pdf', 'excel', 'csv', 'json')
            .required()
            .messages({
                'any.only': 'Invalid format',
                'any.required': 'Format is required'
            }),
        dateRange: Joi.object({
            startDate: Joi.date().iso().required(),
            endDate: Joi.date().iso().min(Joi.ref('startDate')).required()
        }).required(),
        classId: Joi.string()
            .pattern(/^[0-9a-fA-F]{24}$/)
            .messages({
                'string.pattern.base': 'Invalid class ID format'
            }),
        studentId: Joi.string()
            .pattern(/^[0-9a-fA-F]{24}$/)
            .messages({
                'string.pattern.base': 'Invalid student ID format'
            }),
        filters: Joi.object({
            attendanceStatus: Joi.array().items(
                Joi.string().valid('present', 'absent', 'late', 'excused')
            ),
            noteTypes: Joi.array().items(
                Joi.string().valid('academic', 'behavioral', 'attendance', 'general')
            )
        }),
        includeNotes: Joi.boolean(),
        includeMakeupClasses: Joi.boolean(),
        groupBy: Joi.string()
            .valid('student', 'date', 'status')
            .when('type', {
                is: 'class_attendance',
                then: Joi.optional(),
                otherwise: Joi.forbidden()
            })
    }).custom((value, helpers) => {
        // Validate required fields based on report type
        switch (value.type) {
            case 'student_progress':
                if (!value.studentId) {
                    return helpers.error('any.custom', { 
                        message: 'Student ID is required for student progress reports' 
                    });
                }
                break;
            case 'class_attendance':
            case 'class_behavior':
                if (!value.classId) {
                    return helpers.error('any.custom', { 
                        message: `Class ID is required for ${value.type.replace('_', ' ')} reports` 
                    });
                }
                break;
            case 'makeup_classes':
                if (!value.classId && !value.studentId) {
                    return helpers.error('any.custom', { 
                        message: 'Either Class ID or Student ID is required for makeup class reports' 
                    });
                }
                break;
        }
        return value;
    })
};