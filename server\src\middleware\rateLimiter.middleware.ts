// server/src/middleware/rateLimiter.middleware.ts
import rateLimit from 'express-rate-limit';
import { AppError } from '../types/error.types';
import { Request, Response } from 'express';

export const loginLimiter = rateLimit({
    windowMs: 1 * 60 * 1000, // 15 minutes
    max: 5, // 5 requests per windowMs
    handler: (req: Request, res: Response) => {
        res.status(429).json({
            status: 'failed',
            message: 'Too many login attempts, please try again after 15 minutes'
        });
    },
    skipFailedRequests: false,
    standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
    legacyHeaders: false  // Disable the `X-RateLimit-*` headers
});

export const apiLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // 100 requests per windowMs
    handler: (req: Request, res: Response) => {
        res.status(429).json({
            status: 'failed',
            message: 'Too many requests from this IP, please try again after 15 minutes'
        });
    },
    skipFailedRequests: false,
    standardHeaders: true,
    legacyHeaders: false
});