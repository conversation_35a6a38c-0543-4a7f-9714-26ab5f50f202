
import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useToast } from "@/components/ui/use-toast";
import { fetchRoom } from "@/services/roomService";

interface RoomCreateFormProps {
  isEditMode?: boolean;
  roomId?: string;
}

const RoomCreateForm = ({ isEditMode = false, roomId }: RoomCreateFormProps) => {
  const navigate = useNavigate();
  const { toast } = useToast();
  
  const [name, setName] = useState<string>("");
  const [number, setNumber] = useState<string>("");
  const [type, setType] = useState<string>("classroom");
  const [capacity, setCapacity] = useState<string>("");
  const [floor, setFloor] = useState<string>("");
  const [building, setBuilding] = useState<string>("");
  const [description, setDescription] = useState<string>("");
  const [features, setFeatures] = useState<string[]>([]);
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // Load room data if in edit mode
  useEffect(() => {
    if (isEditMode && roomId) {
      setIsLoading(true);
      fetchRoom(roomId)
        .then(room => {
          setName(room.name);
          setNumber(room.id);
          setType(""); // Type is not available in backend, set to empty
          setCapacity(room.capacity.toString());
          setFloor(room.floor.toString()); // Convert number to string
          setBuilding(room.building);
          setDescription(""); // Notes not available in backend, set to empty
          setFeatures(room.features || []);
          setIsLoading(false);
        })
        .catch(err => {
          toast({
            title: "Error",
            description: "Failed to load room data",
            variant: "destructive",
          });
          setIsLoading(false);
        });
    }
  }, [isEditMode, roomId, toast]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Simulating API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: isEditMode ? "Room Updated" : "Room Created",
        description: `Room ${name} has been successfully ${isEditMode ? "updated" : "created"}.`,
      });
      
      navigate("/rooms");
    } catch (error) {
      toast({
        title: "Error",
        description: `Failed to ${isEditMode ? "update" : "create"} room. Please try again.`,
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="pt-6">
          <div className="flex justify-center py-8">
            <p>Loading room data...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <form onSubmit={handleSubmit}>
      <Card>
        <CardHeader>
          <CardTitle>{isEditMode ? "Edit Room" : "Create New Room"}</CardTitle>
          <CardDescription>
            {isEditMode ? "Update room details" : "Add a new room to the system with all necessary details."}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">Room Name</Label>
              <Input
                id="name"
                placeholder="Room Name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="number">Room Number</Label>
              <Input
                id="number"
                placeholder="Room Number"
                value={number}
                onChange={(e) => setNumber(e.target.value)}
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="type">Room Type</Label>
              <Select 
                value={type} 
                onValueChange={setType}
                required
              >
                <SelectTrigger id="type">
                  <SelectValue placeholder="Select room type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectItem value="classroom">Classroom</SelectItem>
                    <SelectItem value="lecture">Lecture Hall</SelectItem>
                    <SelectItem value="lab">Laboratory</SelectItem>
                    <SelectItem value="studio">Studio</SelectItem>
                    <SelectItem value="office">Office</SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="capacity">Capacity</Label>
              <Input
                id="capacity"
                type="number"
                min="1"
                placeholder="Room Capacity"
                value={capacity}
                onChange={(e) => setCapacity(e.target.value)}
                required
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="floor">Floor</Label>
              <Input
                id="floor"
                placeholder="Floor"
                value={floor}
                onChange={(e) => setFloor(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="building">Building</Label>
              <Input
                id="building"
                placeholder="Building"
                value={building}
                onChange={(e) => setBuilding(e.target.value)}
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              placeholder="Room description"
              rows={4}
              value={description}
              onChange={(e) => setDescription(e.target.value)}
            />
          </div>
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button 
            type="button" 
            variant="outline" 
            onClick={() => navigate("/rooms")}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? 
              (isEditMode ? "Updating..." : "Creating...") : 
              (isEditMode ? "Update Room" : "Create Room")}
          </Button>
        </CardFooter>
      </Card>
    </form>
  );
};

export default RoomCreateForm;
