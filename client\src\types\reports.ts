
// Add to the existing types file or create it if it doesn't exist
export interface ReportFilterOption {
  id: string;
  label: string;
  type: 'select' | 'text' | 'number' | 'checkbox';
  placeholder?: string;
  description?: string;
  options?: { label: string; value: string }[];
}

export type ReportFormat = 'pdf' | 'excel' | 'csv' | 'json';

export interface ReportOption {
  id: string;
  name: string;
  description: string;
  category: 'students' | 'classes' | 'attendance' | 'payments' | 'system';
  formats: ReportFormat[];
  filterOptions?: ReportFilterOption[];
  defaultFormat?: ReportFormat;
  // Additional properties needed by components
  type?: string;
  icon?: string;
  role?: string[];
}

export interface ReportTemplate {
  id: string;
  name: string;
  description: string;
  reportType: string;
  format: ReportFormat;
  parameters: Record<string, any>;
  createdBy: string;
  createdAt: string;
  lastUsed?: string;
  isShared?: boolean;
}

export interface ReportGenerationRequest {
  reportType: string;
  format: ReportFormat;
  dateRange: {
    startDate: string;
    endDate: string;
  };
  filters: Record<string, any>;
  templateId?: string;
  saveasTemplate?: boolean;
  templateName?: string;
  type?: string;
  classId?: string;
  studentId?: string;
}

export interface TeacherScheduleItem {
  day: string;
  timeStart: string;
  timeEnd: string;
}

export interface PreviewData {
  columns: {
    header: string;
    accessor: string;
  }[];
  data: any[];
  summary?: {
    title: string;
    value: string | number;
  }[];
}

export interface ReportPreviewData {
  data: any;
  columns: any[];
  summary?: any[];
  charts?: any[];
}

export interface ReportResponse {
  id: string;
  name: string;
  url: string;
  createdAt: string;
  format: ReportFormat;
  type?: string;
  reportType?: string;
  generatedAt?: string;
  generatedBy?: string;
  parameters?: Record<string, any>;
  status?: string;
}

export interface SavedReport {
  id: string;
  name: string;
  type: string;
  createdAt: string;
  createdBy: string;
  lastViewed?: string;
  format: ReportFormat;
  url?: string;
  generatedAt?: string;
  size?: string;
  downloadCount?: number;
}

export type ReportType = string;
export type TeacherReportType = string;
export type SecretaryReportType = string;
