
import { useState } from "react";
import { toast } from "sonner";
import { useQuery } from "@tanstack/react-query";
import { 
  Save, 
  Lock, 
  Shield, 
  Clock, 
  KeyRound, 
  UserCog,
  AlertTriangle,
  RefreshCw
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import CustomCard from "@/components/ui/CustomCard";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { getSystemSettings, updateSystemSettings } from "@/services/systemService";

const AuthConfig = () => {
  const [passwordPolicy, setPasswordPolicy] = useState({
    minLength: 8,
    requireUppercase: true,
    requireLowercase: true,
    requireNumbers: true,
    requireSpecial: true,
    expiryDays: 90,
    preventReuse: true,
    previousPasswordsCount: 5
  });

  const [sessionSettings, setSessionSettings] = useState({
    jwtExpiryHours: 24,
    refreshTokenDays: 7,
    inactivityTimeoutMinutes: 30,
    singleSessionPerUser: false,
    forceLogoutOnPasswordChange: true
  });

  const [loginSecurity, setLoginSecurity] = useState({
    maxAttempts: 5,
    lockoutMinutes: 15,
    captchaThreshold: 3,
    twoFactorAuth: {
      enabled: false,
      required: false,
      requiredForAdmins: true,
      methods: ["app", "email"]
    }
  });

  const { data: systemSettings, isLoading, refetch } = useQuery({
    queryKey: ["systemSettings"],
    queryFn: getSystemSettings,
  });

  // Check if we have settings loaded from the API
  useState(() => {
    if (systemSettings?.security) {
      if (systemSettings.security.passwordPolicy) {
        setPasswordPolicy(systemSettings.security.passwordPolicy);
      }
      if (systemSettings.security.session) {
        setSessionSettings(systemSettings.security.session);
      }
      if (systemSettings.security.loginSecurity) {
        setLoginSecurity(systemSettings.security.loginSecurity);
      }
    }
  });

  const handleSavePasswordPolicy = async () => {
    try {
      await updateSystemSettings({ 
        security: { 
          ...systemSettings?.security,
          passwordPolicy 
        } 
      });
      
      // Log the configuration change
      console.log("Password policy updated:", passwordPolicy);
      
      toast.success("Password policy updated successfully");
    } catch (error) {
      toast.error("Failed to update password policy");
    }
  };

  const handleSaveSessionSettings = async () => {
    try {
      await updateSystemSettings({ 
        security: { 
          ...systemSettings?.security,
          session: sessionSettings 
        } 
      });
      
      // Log the configuration change
      console.log("Session settings updated:", sessionSettings);
      
      toast.success("Session settings updated successfully");
    } catch (error) {
      toast.error("Failed to update session settings");
    }
  };

  const handleSaveLoginSecurity = async () => {
    try {
      await updateSystemSettings({ 
        security: { 
          ...systemSettings?.security,
          loginSecurity 
        } 
      });
      
      // Log the configuration change
      console.log("Login security settings updated:", loginSecurity);
      
      toast.success("Login security settings updated successfully");
    } catch (error) {
      toast.error("Failed to update login security settings");
    }
  };

  const testPasswordPolicy = () => {
    // Generate a password that meets the policy
    let testPassword = "";
    
    if (passwordPolicy.requireUppercase) testPassword += "A";
    if (passwordPolicy.requireLowercase) testPassword += "a";
    if (passwordPolicy.requireNumbers) testPassword += "1";
    if (passwordPolicy.requireSpecial) testPassword += "!";
    
    // Fill up to the minimum length
    while (testPassword.length < passwordPolicy.minLength) {
      testPassword += "x";
    }
    
    toast.success(`Generated valid test password: ${testPassword}`);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold">Authentication Settings</h2>
          <p className="text-muted-foreground">
            Configure password policies, session management, and login security
          </p>
        </div>
        <Button 
          variant="outline" 
          size="sm"
          onClick={() => refetch()}
        >
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>

      {/* Password Policy */}
      <CustomCard className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium flex items-center">
            <Shield className="mr-2 h-5 w-5 text-primary" />
            Password Policy
          </h3>
          <Button 
            variant="outline" 
            size="sm"
            onClick={testPasswordPolicy}
          >
            Test Policy
          </Button>
        </div>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Label htmlFor="min-length" className="flex-grow">Minimum Length</Label>
            <Input 
              id="min-length" 
              type="number" 
              className="w-20 ml-4" 
              value={passwordPolicy.minLength}
              onChange={(e) => setPasswordPolicy({
                ...passwordPolicy,
                minLength: parseInt(e.target.value)
              })}
              min={6}
              max={30}
            />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center space-x-2">
              <Switch 
                id="require-uppercase" 
                checked={passwordPolicy.requireUppercase}
                onCheckedChange={(checked) => setPasswordPolicy({
                  ...passwordPolicy,
                  requireUppercase: checked
                })}
              />
              <Label htmlFor="require-uppercase">Require Uppercase</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch 
                id="require-lowercase" 
                checked={passwordPolicy.requireLowercase}
                onCheckedChange={(checked) => setPasswordPolicy({
                  ...passwordPolicy,
                  requireLowercase: checked
                })}
              />
              <Label htmlFor="require-lowercase">Require Lowercase</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch 
                id="require-numbers" 
                checked={passwordPolicy.requireNumbers}
                onCheckedChange={(checked) => setPasswordPolicy({
                  ...passwordPolicy,
                  requireNumbers: checked
                })}
              />
              <Label htmlFor="require-numbers">Require Numbers</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch 
                id="require-special" 
                checked={passwordPolicy.requireSpecial}
                onCheckedChange={(checked) => setPasswordPolicy({
                  ...passwordPolicy,
                  requireSpecial: checked
                })}
              />
              <Label htmlFor="require-special">Require Special Characters</Label>
            </div>
          </div>
          
          <Separator />
          
          <div className="flex items-center justify-between">
            <Label htmlFor="expiry-days" className="flex-grow">Password Expiry (days)</Label>
            <Input 
              id="expiry-days" 
              type="number" 
              className="w-20 ml-4" 
              value={passwordPolicy.expiryDays}
              onChange={(e) => setPasswordPolicy({
                ...passwordPolicy,
                expiryDays: parseInt(e.target.value)
              })}
              min={0}
              max={365}
            />
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            Set to 0 to disable password expiration
          </p>
          
          <div className="flex items-center space-x-2">
            <Switch 
              id="prevent-reuse" 
              checked={passwordPolicy.preventReuse}
              onCheckedChange={(checked) => setPasswordPolicy({
                ...passwordPolicy,
                preventReuse: checked
              })}
            />
            <Label htmlFor="prevent-reuse">Prevent Password Reuse</Label>
          </div>
          
          <div className="flex items-center justify-between">
            <Label htmlFor="previous-passwords" className="flex-grow">
              Remember Previous Passwords
            </Label>
            <Input 
              id="previous-passwords" 
              type="number" 
              className="w-20 ml-4" 
              value={passwordPolicy.previousPasswordsCount}
              onChange={(e) => setPasswordPolicy({
                ...passwordPolicy,
                previousPasswordsCount: parseInt(e.target.value)
              })}
              min={1}
              max={20}
              disabled={!passwordPolicy.preventReuse}
            />
          </div>
          
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button>
                <Save className="h-4 w-4 mr-2" />
                Save Password Policy
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Update Password Policy</AlertDialogTitle>
                <AlertDialogDescription>
                  You are about to update the system-wide password policy. This may require users to update their passwords on next login if they no longer meet the requirements.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction onClick={handleSavePasswordPolicy}>
                  Confirm Update
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </CustomCard>

      {/* Session Management */}
      <CustomCard className="p-6">
        <h3 className="text-lg font-medium flex items-center mb-4">
          <Clock className="mr-2 h-5 w-5 text-primary" />
          Session Management
        </h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Label htmlFor="jwt-expiry" className="flex-grow">JWT Token Expiry (hours)</Label>
            <Input 
              id="jwt-expiry" 
              type="number" 
              className="w-20 ml-4" 
              value={sessionSettings.jwtExpiryHours}
              onChange={(e) => setSessionSettings({
                ...sessionSettings,
                jwtExpiryHours: parseInt(e.target.value)
              })}
              min={1}
              max={168}
            />
          </div>
          
          <div className="flex items-center justify-between">
            <Label htmlFor="refresh-token" className="flex-grow">Refresh Token Duration (days)</Label>
            <Input 
              id="refresh-token" 
              type="number" 
              className="w-20 ml-4" 
              value={sessionSettings.refreshTokenDays}
              onChange={(e) => setSessionSettings({
                ...sessionSettings,
                refreshTokenDays: parseInt(e.target.value)
              })}
              min={1}
              max={90}
            />
          </div>
          
          <div className="flex items-center justify-between">
            <Label htmlFor="inactivity-timeout" className="flex-grow">Inactivity Timeout (minutes)</Label>
            <Input 
              id="inactivity-timeout" 
              type="number" 
              className="w-20 ml-4" 
              value={sessionSettings.inactivityTimeoutMinutes}
              onChange={(e) => setSessionSettings({
                ...sessionSettings,
                inactivityTimeoutMinutes: parseInt(e.target.value)
              })}
              min={0}
              max={1440}
            />
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            Set to 0 to disable inactivity timeout
          </p>
          
          <div className="flex items-center space-x-2">
            <Switch 
              id="single-session" 
              checked={sessionSettings.singleSessionPerUser}
              onCheckedChange={(checked) => setSessionSettings({
                ...sessionSettings,
                singleSessionPerUser: checked
              })}
            />
            <Label htmlFor="single-session">
              Single Session Per User (invalidate previous sessions on login)
            </Label>
          </div>
          
          <div className="flex items-center space-x-2">
            <Switch 
              id="force-logout" 
              checked={sessionSettings.forceLogoutOnPasswordChange}
              onCheckedChange={(checked) => setSessionSettings({
                ...sessionSettings,
                forceLogoutOnPasswordChange: checked
              })}
            />
            <Label htmlFor="force-logout">Force Logout on Password Change</Label>
          </div>
          
          <Button onClick={handleSaveSessionSettings}>
            <Save className="h-4 w-4 mr-2" />
            Save Session Settings
          </Button>
        </div>
      </CustomCard>

      {/* Login Security */}
      <CustomCard className="p-6">
        <h3 className="text-lg font-medium flex items-center mb-4">
          <Lock className="mr-2 h-5 w-5 text-primary" />
          Login Security
        </h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Label htmlFor="max-attempts" className="flex-grow">Max Failed Login Attempts</Label>
            <Input 
              id="max-attempts" 
              type="number" 
              className="w-20 ml-4" 
              value={loginSecurity.maxAttempts}
              onChange={(e) => setLoginSecurity({
                ...loginSecurity,
                maxAttempts: parseInt(e.target.value)
              })}
              min={1}
              max={20}
            />
          </div>
          
          <div className="flex items-center justify-between">
            <Label htmlFor="lockout-minutes" className="flex-grow">Account Lockout Duration (minutes)</Label>
            <Input 
              id="lockout-minutes" 
              type="number" 
              className="w-20 ml-4" 
              value={loginSecurity.lockoutMinutes}
              onChange={(e) => setLoginSecurity({
                ...loginSecurity,
                lockoutMinutes: parseInt(e.target.value)
              })}
              min={1}
              max={1440}
            />
          </div>
          
          <div className="flex items-center justify-between">
            <Label htmlFor="captcha-threshold" className="flex-grow">CAPTCHA Threshold (attempts)</Label>
            <Input 
              id="captcha-threshold" 
              type="number" 
              className="w-20 ml-4" 
              value={loginSecurity.captchaThreshold}
              onChange={(e) => setLoginSecurity({
                ...loginSecurity,
                captchaThreshold: parseInt(e.target.value)
              })}
              min={0}
              max={10}
            />
          </div>
          <p className="text-xs text-muted-foreground mt-1">
            Number of failed attempts before CAPTCHA is required. Set to 0 to always require CAPTCHA.
          </p>
          
          <Separator />
          
          <h4 className="font-medium">Two-Factor Authentication</h4>
          
          <div className="flex items-center space-x-2">
            <Switch 
              id="2fa-enabled" 
              checked={loginSecurity.twoFactorAuth.enabled}
              onCheckedChange={(checked) => setLoginSecurity({
                ...loginSecurity,
                twoFactorAuth: {
                  ...loginSecurity.twoFactorAuth,
                  enabled: checked
                }
              })}
            />
            <Label htmlFor="2fa-enabled">Enable Two-Factor Authentication</Label>
          </div>
          
          <div className="flex items-center space-x-2">
            <Switch 
              id="2fa-required" 
              checked={loginSecurity.twoFactorAuth.required}
              onCheckedChange={(checked) => setLoginSecurity({
                ...loginSecurity,
                twoFactorAuth: {
                  ...loginSecurity.twoFactorAuth,
                  required: checked
                }
              })}
              disabled={!loginSecurity.twoFactorAuth.enabled}
            />
            <Label htmlFor="2fa-required">Required for All Users</Label>
          </div>
          
          <div className="flex items-center space-x-2">
            <Switch 
              id="2fa-required-admins" 
              checked={loginSecurity.twoFactorAuth.requiredForAdmins}
              onCheckedChange={(checked) => setLoginSecurity({
                ...loginSecurity,
                twoFactorAuth: {
                  ...loginSecurity.twoFactorAuth,
                  requiredForAdmins: checked
                }
              })}
              disabled={!loginSecurity.twoFactorAuth.enabled || loginSecurity.twoFactorAuth.required}
            />
            <Label htmlFor="2fa-required-admins">Required for Admins and Managers</Label>
          </div>
          
          <div className="space-y-2">
            <Label>2FA Methods</Label>
            <div className="space-x-2">
              <Button 
                variant={loginSecurity.twoFactorAuth.methods.includes("app") ? "default" : "outline"} 
                size="sm"
                onClick={() => {
                  const newMethods = loginSecurity.twoFactorAuth.methods.includes("app")
                    ? loginSecurity.twoFactorAuth.methods.filter(m => m !== "app")
                    : [...loginSecurity.twoFactorAuth.methods, "app"];
                  
                  setLoginSecurity({
                    ...loginSecurity,
                    twoFactorAuth: {
                      ...loginSecurity.twoFactorAuth,
                      methods: newMethods
                    }
                  });
                }}
                disabled={!loginSecurity.twoFactorAuth.enabled}
              >
                Authenticator App
              </Button>
              
              <Button 
                variant={loginSecurity.twoFactorAuth.methods.includes("email") ? "default" : "outline"} 
                size="sm"
                onClick={() => {
                  const newMethods = loginSecurity.twoFactorAuth.methods.includes("email")
                    ? loginSecurity.twoFactorAuth.methods.filter(m => m !== "email")
                    : [...loginSecurity.twoFactorAuth.methods, "email"];
                  
                  setLoginSecurity({
                    ...loginSecurity,
                    twoFactorAuth: {
                      ...loginSecurity.twoFactorAuth,
                      methods: newMethods
                    }
                  });
                }}
                disabled={!loginSecurity.twoFactorAuth.enabled}
              >
                Email
              </Button>
              
              <Button 
                variant={loginSecurity.twoFactorAuth.methods.includes("sms") ? "default" : "outline"} 
                size="sm"
                onClick={() => {
                  const newMethods = loginSecurity.twoFactorAuth.methods.includes("sms")
                    ? loginSecurity.twoFactorAuth.methods.filter(m => m !== "sms")
                    : [...loginSecurity.twoFactorAuth.methods, "sms"];
                  
                  setLoginSecurity({
                    ...loginSecurity,
                    twoFactorAuth: {
                      ...loginSecurity.twoFactorAuth,
                      methods: newMethods
                    }
                  });
                }}
                disabled={!loginSecurity.twoFactorAuth.enabled}
              >
                SMS
              </Button>
            </div>
          </div>
          
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Enabling mandatory two-factor authentication will require users to set up 2FA on their next login. 
              Ensure your users are properly notified before enabling this setting.
            </AlertDescription>
          </Alert>
          
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button>
                <Save className="h-4 w-4 mr-2" />
                Save Login Security Settings
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Update Security Settings</AlertDialogTitle>
                <AlertDialogDescription>
                  You are about to update critical security settings. These changes will affect how users authenticate to the system.
                  {loginSecurity.twoFactorAuth.required && (
                    <p className="font-semibold text-destructive mt-2">
                      WARNING: You are enabling mandatory two-factor authentication for all users.
                    </p>
                  )}
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction onClick={handleSaveLoginSecurity}>
                  Confirm Changes
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </CustomCard>
    </div>
  );
};

export default AuthConfig;
