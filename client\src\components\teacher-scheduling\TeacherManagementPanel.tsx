
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription, CardFooter } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { TeacherScheduleData } from "@/types/teacherScheduling";
import { capitalizeFirstLetter } from "@/lib/utils";
import { PlusCircle, X, AlertCircle } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Teacher } from "@/types/class";
import { cn } from "@/lib/utils";

interface TeacherManagementPanelProps {
  teachers: TeacherScheduleData[];
  availableTeachers: Teacher[];
  onAddTeacher: (teacherId: string) => void;
  onRemoveTeacher: (teacherId: string) => void;
  onTeacherSelect: (teacherId: string) => void;
  selectedTeacherId?: string;
  conflicts?: Map<string, string[]>;
  readOnly?: boolean;
}

const TeacherManagementPanel = ({
  teachers,
  availableTeachers,
  onAddTeacher,
  onRemoveTeacher,
  onTeacherSelect,
  selectedTeacherId,
  conflicts,
  readOnly = false
}: TeacherManagementPanelProps) => {
  const [showRemoveDialog, setShowRemoveDialog] = useState(false);
  const [teacherToRemove, setTeacherToRemove] = useState<string | null>(null);

  // Helper function to get schedule summary for a teacher
  const getScheduleSummary = (scheduleData: TeacherScheduleData) => {
    const dayCount = new Set(scheduleData.schedule.map(s => s.day)).size;
    const totalHours = scheduleData.schedule.reduce((total, slot) => {
      const startParts = slot.timeStart.split(':');
      const endParts = slot.timeEnd.split(':');
      
      const startTime = parseInt(startParts[0]) * 60 + parseInt(startParts[1]);
      const endTime = parseInt(endParts[0]) * 60 + parseInt(endParts[1]);
      
      return total + (endTime - startTime) / 60;
    }, 0);
    
    return {
      days: dayCount,
      hours: totalHours.toFixed(1),
      slots: scheduleData.schedule.length
    };
  };

  // Helper to check if a teacher has conflicts
  const hasConflicts = (teacherId: string): boolean => {
    if (!conflicts) return false;
    const teacherConflicts = conflicts.get(teacherId);
    return teacherConflicts !== undefined && teacherConflicts.length > 0;
  };

  // Handle teacher removal with confirmation
  const handleRemoveTeacher = (teacherId: string) => {
    setTeacherToRemove(teacherId);
    setShowRemoveDialog(true);
  };

  const confirmRemoveTeacher = () => {
    if (teacherToRemove) {
      onRemoveTeacher(teacherToRemove);
      setTeacherToRemove(null);
    }
    setShowRemoveDialog(false);
  };
  
  // Filter out teachers that are already added
  const getAvailableToAdd = () => {
    const currentTeacherIds = new Set(teachers.map(t => t.teacherId));
    return availableTeachers.filter(t => !currentTeacherIds.has(t.id));
  };

  return (
    <>
      <Card>
        <CardHeader className="pb-3">
          <CardTitle>Teachers</CardTitle>
          <CardDescription>
            Manage teachers assigned to this class
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {teachers.length === 0 ? (
            <div className="text-center py-4 text-muted-foreground">
              No teachers assigned. Add a teacher to get started.
            </div>
          ) : (
            <div className="space-y-3">
              {teachers.map((teacher) => {
                const summary = getScheduleSummary(teacher);
                const hasTeacherConflicts = hasConflicts(teacher.teacherId);
                
                return (
                  <div 
                    key={teacher.teacherId}
                    className={cn(
                      "p-3 rounded-md border cursor-pointer transition-colors",
                      selectedTeacherId === teacher.teacherId 
                        ? "border-primary bg-primary/5"
                        : "hover:bg-accent",
                      hasTeacherConflicts ? "border-red-300" : ""
                    )}
                    onClick={() => onTeacherSelect(teacher.teacherId)}
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium">{teacher.name}</h3>
                        <div className="text-sm text-muted-foreground mt-1">
                          {summary.days} days · {summary.hours} hours · {summary.slots} slots
                        </div>
                      </div>
                      
                      {!readOnly && (
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-7 w-7 rounded-full hover:bg-destructive/10 hover:text-destructive"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleRemoveTeacher(teacher.teacherId);
                          }}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                    
                    {hasTeacherConflicts && (
                      <div className="mt-2 text-xs text-red-600 flex items-center">
                        <AlertCircle className="h-3.5 w-3.5 mr-1" />
                        <span>Has scheduling conflicts</span>
                      </div>
                    )}
                    
                    <div className="mt-2 space-y-1">
                      {teacher.schedule.map((slot, idx) => (
                        <div key={idx} className="text-xs">
                          <span className="font-medium">{capitalizeFirstLetter(slot.day)}</span>:{' '}
                          {slot.timeStart} - {slot.timeEnd}
                        </div>
                      ))}
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </CardContent>
        <CardFooter>
          {!readOnly && (
            <div className="w-full">
              {getAvailableToAdd().length > 0 ? (
                <Select onValueChange={onAddTeacher}>
                  <SelectTrigger>
                    <div className="flex items-center">
                      <PlusCircle className="h-4 w-4 mr-2" />
                      <span>Add Teacher</span>
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectLabel>Available Teachers</SelectLabel>
                      {getAvailableToAdd().map((teacher) => (
                        <SelectItem key={teacher.id} value={teacher.id}>
                          {teacher.name}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              ) : (
                <Button variant="outline" disabled className="w-full">
                  <PlusCircle className="h-4 w-4 mr-2" />
                  No available teachers
                </Button>
              )}
            </div>
          )}
        </CardFooter>
      </Card>
      
      {/* Confirmation dialog for removing a teacher */}
      <AlertDialog open={showRemoveDialog} onOpenChange={setShowRemoveDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Remove Teacher</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to remove this teacher from the class?
              All of their scheduled time slots will be removed.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmRemoveTeacher}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Remove
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default TeacherManagementPanel;
