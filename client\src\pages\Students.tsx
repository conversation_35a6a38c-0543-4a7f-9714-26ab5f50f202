
import { useNavigate } from "react-router-dom";
import { useEffect } from "react";
import MainLayout from "@/components/layout/MainLayout";
import StudentTable from "@/components/students/StudentTable";

// This is the legacy Students page that will redirect to the new StudentManagement page
export default function Students() {
  const navigate = useNavigate();
  
  useEffect(() => {
    // Redirect to the new student management page
    navigate("/students");
  }, [navigate]);
  
  return (
    <MainLayout>
      <div>
        <h1>Redirecting...</h1>
      </div>
    </MainLayout>
  );
}
