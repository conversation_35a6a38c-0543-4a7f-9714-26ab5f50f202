// server/src/services/note.service.ts
import mongoose, { FilterQuery } from 'mongoose';
import { Note } from '../models/note.model';
import { Student } from '../models/student.model';
import { Class } from '../models/class.model';
import { AppError } from '../types/error.types';
import { SystemLogger } from './logger.service';
import {
    INote,
    NoteQueryOptions,
    CreateNoteDTO,
    UpdateNoteDTO,
    NoteBulkOperationDTO,
    NoteExportOptions,
    NoteSearchOptions,
    NoteResponseDTO,
    NoteCategory,
    NoteVisibility,
    entityTypeToModel
} from '../types/notes.types';
import { getVisibilityQuery, validateUserRole } from '../utils/permissionUtils';

export class NoteService {

    private static handleServiceError(error: unknown, operation: string, details?: any): never {
        console.error(`Note service error during ${operation}:`, {
            error,
            details,
            errorDetails: error instanceof Error ? {
                name: error.name,
                message: error.message,
                stack: error.stack
            } : 'Unknown error type'
        });

        if (error instanceof AppError) {
            throw error;
        }

        if (error instanceof mongoose.Error.ValidationError) {
            throw new AppError(400, `Validation error: ${Object.values(error.errors)
                .map(e => e.message).join(', ')}`);
        }

        if (error instanceof mongoose.Error.CastError) {
            throw new AppError(400, `Invalid ID format: ${error.path}`);
        }

        throw new AppError(500, `Error ${operation}: ${error instanceof Error ?
            error.message : 'Unknown error'}`);
    }

    // Helper method to get visibility query based on user role
    private static getVisibilityQuery(userRole: string, userId: string): FilterQuery<INote> {
        // Validate role first
        validateUserRole(userRole);
        
        return getVisibilityQuery(userRole, userId);
    }

    // Main CRUD operations
    static async getNotes(
        options: NoteQueryOptions,
        requestingUserId: string,
        userRole: string
    ) {
        try {
            // Apply defaults and ensure all parameters have safe values
            const {
                page = 1,
                limit = 10,
                sortBy = 'createdAt',
                sortOrder = 'desc',
                type,
                visibility,
                studentId,
                classId,
                tags,
                search,
                fromDate,
                toDate,
                createdBy
            } = options;

            const query: FilterQuery<INote> = {
                ...this.getVisibilityQuery(userRole, requestingUserId)
            };

            // Apply filters with proper null/undefined checks
            if (type !== undefined && type !== null) {
                query.type = type;
            }
            
            if (studentId) {
                if (mongoose.Types.ObjectId.isValid(studentId)) {
                    query.studentId = new mongoose.Types.ObjectId(studentId);
                } else {
                    throw new AppError(400, 'Invalid student ID format');
                }
            }
            
            if (classId) {
                if (mongoose.Types.ObjectId.isValid(classId)) {
                    query.classId = new mongoose.Types.ObjectId(classId);
                } else {
                    throw new AppError(400, 'Invalid class ID format');
                }
            }
            
            // Handle tags array safely
            if (Array.isArray(tags) && tags.length > 0) {
                query.tags = { $all: tags.filter(tag => tag != null && typeof tag === 'string') };
            }
            
            // Handle search term safely
            if (search && typeof search === 'string' && search.trim().length > 0) {
                const searchTerm = search.trim();
                query.$or = [
                    { content: { $regex: searchTerm, $options: 'i' } },
                    { tags: { $regex: searchTerm, $options: 'i' } }
                ];
            }
            
            // Handle date range safely
            if (fromDate || toDate) {
                query.createdAt = {};
                
                if (fromDate) {
                    const fromDateObj = fromDate instanceof Date ? fromDate : new Date(fromDate);
                    // Check for valid date
                    if (!isNaN(fromDateObj.getTime())) {
                        query.createdAt.$gte = fromDateObj;
                    }
                }
                
                if (toDate) {
                    const toDateObj = toDate instanceof Date ? toDate : new Date(toDate);
                    // Check for valid date
                    if (!isNaN(toDateObj.getTime())) {
                        query.createdAt.$lte = toDateObj;
                    }
                }

                // If createdAt object is empty, remove it to avoid query issues
                if (Object.keys(query.createdAt).length === 0) {
                    delete query.createdAt;
                }
            }
            
            if (createdBy) {
                if (mongoose.Types.ObjectId.isValid(createdBy)) {
                    query.createdBy = new mongoose.Types.ObjectId(createdBy);
                } else {
                    throw new AppError(400, 'Invalid user ID format');
                }
            }

            // Ensure pagination parameters are within safe bounds
            const safeLimit = Math.min(Math.max(1, Number(limit) || 10), 100);
            const safePage = Math.max(1, Number(page) || 1);
            const skip = (safePage - 1) * safeLimit;

            // Ensure sort parameters are valid
            const validSortFields = ['createdAt', 'modifiedAt', 'type', 'visibility'];
            const safeSortBy = validSortFields.includes(String(sortBy)) ? String(sortBy) : 'createdAt';
            const safeSortOrder = ['asc', 'desc'].includes(String(sortOrder)) ? String(sortOrder) : 'desc';
            
            const sort: Record<string, 1 | -1> = {
                [safeSortBy]: safeSortOrder === 'asc' ? 1 : -1
            };

            // Execute the query with all safety measures in place
            const [notes, total] = await Promise.all([
                Note.find(query)
                    .populate({
                        path: 'createdBy',
                        select: 'username'
                    })
                    .populate({
                        path: 'modifiedBy',
                        select: 'username'
                    })
                    .populate({
                        path: 'studentId',
                        select: 'name',
                        options: { lean: true }
                    })
                    .populate({
                        path: 'classId',
                        select: 'name',
                        options: { lean: true }
                    })
                    .sort(sort)
                    .skip(skip)
                    .limit(safeLimit)
                    .lean(),
                Note.countDocuments(query)
            ]);

            await SystemLogger.log({
                severity: 'info',
                category: 'notes',
                action: 'list_notes',
                performedBy: requestingUserId,
                details: {
                    filters: {
                        ...options,
                        // Include sanitized parameters for better debugging
                        safePage,
                        safeLimit,
                        safeSortBy,
                        safeSortOrder
                    }
                },
                status: 'success',
                timestamp: new Date()
            });

            // Ensure we always return a valid array
            return {
                notes: Array.isArray(notes) ? notes.map(note => this.formatNoteResponse(note)) : [],
                pagination: {
                    total: total || 0,
                    page: safePage,
                    limit: safeLimit,
                    pages: Math.ceil((total || 0) / safeLimit)
                }
            };
        } catch (error) {
            if (error instanceof AppError) throw error;
            
            if (error instanceof mongoose.Error.ValidationError) {
                throw new AppError(400, `Validation error: ${Object.values(error.errors)
                    .map(e => e.message).join(', ')}`);
            }
            
            if (error instanceof mongoose.Error.CastError) {
                throw new AppError(400, `Invalid format for field: ${error.path}`);
            }
            
            throw new AppError(500, `Error fetching notes: ${error instanceof Error 
                ? error.message : 'Unknown error'}`);
        }
    }

    static async getNoteById(
        id: string,
        requestingUserId: string,
        userRole: string
    ): Promise<NoteResponseDTO> {
        try {
            if (!id || !mongoose.Types.ObjectId.isValid(id)) {
                throw new AppError(400, 'Invalid note ID format');
            }

            const note = await Note.findById(id)
                .populate({
                    path: 'createdBy',
                    select: 'username'
                })
                .populate({
                    path: 'modifiedBy',
                    select: 'username'
                })
                .populate({
                    path: 'studentId',
                    select: 'name',
                    options: { lean: true }
                })
                .populate({
                    path: 'classId',
                    select: 'name',
                    options: { lean: true }
                })
                .lean();

            if (!note) {
                throw new AppError(404, 'Note not found');
            }

            await SystemLogger.log({
                severity: 'info',
                category: 'notes',
                action: 'view_note',
                performedBy: requestingUserId,
                targetId: id,
                details: { noteId: id },
                status: 'success',
                timestamp: new Date()
            });

            return this.formatNoteResponse(note);
        } catch (error) {
            if (error instanceof mongoose.Error.CastError) {
                throw new AppError(400, 'Invalid note ID format');
            }
            throw error instanceof AppError ? error : new AppError(500, 'Error fetching note');
        }
    }

    static async createNote(
        noteData: CreateNoteDTO,
        createdBy: string
    ): Promise<NoteResponseDTO> {
        try {
            // Validate inputs
            if (!noteData) {
                throw new AppError(400, 'Note data is required');
            }

            if (!createdBy || !mongoose.Types.ObjectId.isValid(createdBy)) {
                throw new AppError(400, 'Valid creator ID is required');
            }

            console.log('Starting note creation with data:', JSON.stringify({
                ...noteData,
                createdBy
            }, null, 2));

            // Validate entity references
            await this.validateEntityReferences(noteData);

            // Ensure tags is an array
            const safeTags = Array.isArray(noteData.tags) 
                ? noteData.tags.filter(tag => tag != null && typeof tag === 'string')
                : [];

            const note = new Note({
                ...noteData,
                tags: safeTags,
                createdBy: new mongoose.Types.ObjectId(createdBy),
                modifiedBy: new mongoose.Types.ObjectId(createdBy),
                modificationHistory: [] // Initialize empty array
            });

            console.log('Note model created:', JSON.stringify(note.toObject(), null, 2));

            await note.save();
            console.log('Note saved successfully with ID:', note._id);

            // Modified population to avoid virtual fields
            const populatedNote = await Note.findById(note._id)
                .populate({
                    path: 'createdBy',
                    select: 'username'
                })
                .populate({
                    path: 'modifiedBy',
                    select: 'username'
                })
                .populate({
                    path: 'studentId',
                    select: 'name',
                    options: { lean: true }
                })
                .populate({
                    path: 'classId',
                    select: 'name',
                    options: { lean: true }
                })
                .lean();

            if (!populatedNote) {
                throw new AppError(500, 'Failed to retrieve created note');
            }

            await SystemLogger.log({
                severity: 'info',
                category: 'notes',
                action: 'create_note',
                performedBy: createdBy,
                targetId: note._id.toString(),
                details: {
                    type: note.type,
                    studentId: note.studentId?.toString(),
                    classId: note.classId?.toString()
                },
                status: 'success',
                timestamp: new Date()
            });

            // Format the response
            const formattedResponse = this.formatNoteResponse(populatedNote);
            console.log('Formatted response:', JSON.stringify(formattedResponse, null, 2));

            return formattedResponse;
        } catch (error) {
            console.error('Note creation failed:', {
                error,
                noteData,
                errorDetails: error instanceof Error ? {
                    message: error.message,
                    stack: error.stack,
                    name: error.name
                } : 'Unknown error type'
            });

            if (error instanceof mongoose.Error.ValidationError) {
                throw new AppError(400, `Validation error: ${Object.values(error.errors).map(e => e.message).join(', ')}`);
            }

            if (error instanceof mongoose.Error.CastError) {
                throw new AppError(400, `Invalid ID format: ${error.path}`);
            }

            if (error instanceof AppError) {
                throw error;
            }

            throw new AppError(500, `Error creating note: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    static async updateNote(
        id: string,
        updateData: UpdateNoteDTO,
        updatedBy: string,
        userRole: string
    ): Promise<NoteResponseDTO> {
        try {
            // Validate inputs
            if (!id || !mongoose.Types.ObjectId.isValid(id)) {
                throw new AppError(400, 'Invalid note ID');
            }

            if (!updatedBy || !mongoose.Types.ObjectId.isValid(updatedBy)) {
                throw new AppError(400, 'Valid updater ID is required');
            }

            if (!updateData || Object.keys(updateData).length === 0) {
                throw new AppError(400, 'Update data is required');
            }

            const note = await Note.findById(id)
                .populate({
                    path: 'createdBy',
                    select: 'username'
                })
                .populate({
                    path: 'modifiedBy',
                    select: 'username'
                })
                .populate({
                    path: 'studentId',
                    select: 'name',
                    options: { lean: true }
                })
                .populate({
                    path: 'classId',
                    select: 'name',
                    options: { lean: true }
                });

            if (!note) {
                throw new AppError(404, 'Note not found');
            }

            // Process and validate update fields
            const safeUpdateData: Partial<UpdateNoteDTO> = {};
            
            // Content validation
            if (updateData.content !== undefined) {
                if (typeof updateData.content !== 'string') {
                    throw new AppError(400, 'Content must be a string');
                }
                if (updateData.content.trim().length === 0) {
                    throw new AppError(400, 'Content cannot be empty');
                }
                safeUpdateData.content = updateData.content;
            }
            
            // Visibility validation
            if (updateData.visibility !== undefined) {
                const validVisibilities: NoteVisibility[] = ['teacher_only', 'all_staff', 'manager_only'];
                if (!validVisibilities.includes(updateData.visibility)) {
                    throw new AppError(400, 'Invalid visibility value');
                }
                safeUpdateData.visibility = updateData.visibility;
            }
            
            // Tags validation
            if (updateData.tags !== undefined) {
                if (!Array.isArray(updateData.tags)) {
                    throw new AppError(400, 'Tags must be an array');
                }
                
                // Filter out any non-string or empty tags
                const filteredTags = updateData.tags.filter(
                    tag => tag !== null && tag !== undefined && typeof tag === 'string' && tag.trim().length > 0
                );
                
                // Check for duplicates
                const uniqueTags = [...new Set(filteredTags)];
                
                // Enforce maximum tags limit
                if (uniqueTags.length > 10) {
                    throw new AppError(400, 'Maximum 10 tags allowed');
                }
                
                safeUpdateData.tags = uniqueTags;
            }

            // Update fields
            Object.entries(safeUpdateData).forEach(([field, value]) => {
                if (value !== undefined) {
                    note.set(field, value);
                }
            });

            // Update modification related fields
            note.modifiedBy = new mongoose.Types.ObjectId(updatedBy);
            note.modifiedAt = new Date();

            // Save the updated note
            await note.save();

            // Fetch the updated note with populated fields
            const updatedNote = await Note.findById(note._id)
                .populate({
                    path: 'createdBy',
                    select: 'username'
                })
                .populate({
                    path: 'modifiedBy',
                    select: 'username'
                })
                .populate({
                    path: 'studentId',
                    select: 'name',
                    options: { lean: true }
                })
                .populate({
                    path: 'classId',
                    select: 'name',
                    options: { lean: true }
                })
                .lean();

            if (!updatedNote) {
                throw new AppError(500, 'Failed to retrieve updated note');
            }

            await SystemLogger.log({
                severity: 'info',
                category: 'notes',
                action: 'update_note',
                performedBy: updatedBy,
                targetId: id,
                details: { updateData: safeUpdateData },
                status: 'success',
                timestamp: new Date()
            });

            return this.formatNoteResponse(updatedNote);
        } catch (error) {
            if (error instanceof AppError) throw error;
            
            if (error instanceof mongoose.Error.ValidationError) {
                throw new AppError(400, `Validation error: ${Object.values(error.errors)
                    .map(e => e.message).join(', ')}`);
            }
            
            if (error instanceof mongoose.Error.CastError) {
                throw new AppError(400, `Invalid format for field: ${error.path}`);
            }
            
            throw new AppError(500, `Error updating note: ${error instanceof Error 
                ? error.message : 'Unknown error'}`);
        }
    }

    static async deleteNote(
        id: string,
        deletedBy: string,
        userRole: string
    ): Promise<void> {
        try {
            if (!id || !mongoose.Types.ObjectId.isValid(id)) {
                throw new AppError(400, 'Invalid note ID');
            }

            if (!deletedBy || !mongoose.Types.ObjectId.isValid(deletedBy)) {
                throw new AppError(400, 'Valid deleter ID is required');
            }

            const note = await Note.findById(id);
            if (!note) {
                throw new AppError(404, 'Note not found');
            }

            // Need to wrap the function call in a try/catch to handle any role validation errors
            try {
                const hasAccess = await note.validateVisibilityAccess(
                    new mongoose.Types.ObjectId(deletedBy),
                    userRole
                );

                if (!hasAccess) {
                    throw new AppError(403, 'Not authorized to delete this note');
                }
            } catch (accessError) {
                if (accessError instanceof AppError) {
                    throw accessError;
                }
                throw new AppError(403, 'Error validating access permissions');
            }

            await note.deleteOne();

            await SystemLogger.log({
                severity: 'info',
                category: 'notes',
                action: 'delete_note',
                performedBy: deletedBy,
                targetId: id,
                details: {
                    noteType: note.type,
                    studentId: note.studentId?.toString(),
                    classId: note.classId?.toString()
                },
                status: 'success',
                timestamp: new Date()
            });
        } catch (error) {
            if (error instanceof AppError) throw error;
            
            if (error instanceof mongoose.Error.CastError) {
                throw new AppError(400, 'Invalid note ID format');
            }
            
            throw new AppError(500, `Error deleting note: ${error instanceof Error 
                ? error.message : 'Unknown error'}`);
        }
    }

    // Specialized note retrieval methods
    static async getStudentNotes(
        studentId: string,
        options: {
            page: number;
            limit: number;
            type?: NoteCategory;
            fromDate?: Date;
            toDate?: Date;
        },
        requestingUserId: string,
        userRole: string
    ) {
        try {
            // Validate student ID
            if (!studentId || !mongoose.Types.ObjectId.isValid(studentId)) {
                throw new AppError(400, 'Invalid student ID');
            }

            // Ensure pagination parameters are valid
            const safeOptions = {
                page: Math.max(1, options.page || 1),
                limit: Math.min(Math.max(1, options.limit || 10), 100),
                type: options.type,
                fromDate: options.fromDate instanceof Date ? options.fromDate : 
                    options.fromDate ? new Date(options.fromDate) : undefined,
                toDate: options.toDate instanceof Date ? options.toDate : 
                    options.toDate ? new Date(options.toDate) : undefined
            };

            // Check for valid dates
            if (safeOptions.fromDate && isNaN(safeOptions.fromDate.getTime())) {
                delete safeOptions.fromDate;
            }
            
            if (safeOptions.toDate && isNaN(safeOptions.toDate.getTime())) {
                delete safeOptions.toDate;
            }

            const query: FilterQuery<INote> = {
                studentId: new mongoose.Types.ObjectId(studentId),
                ...this.getVisibilityQuery(userRole, requestingUserId)
            };

            if (safeOptions.type) {
                query.type = safeOptions.type;
            }

            if (safeOptions.fromDate || safeOptions.toDate) {
                query.createdAt = {};
                if (safeOptions.fromDate) query.createdAt.$gte = safeOptions.fromDate;
                if (safeOptions.toDate) query.createdAt.$lte = safeOptions.toDate;
            }

            const [notes, total] = await Promise.all([
                Note.find(query)
                    .populate({
                        path: 'createdBy',
                        select: 'username'
                    })
                    .populate({
                        path: 'modifiedBy',
                        select: 'username'
                    })
                    .populate({
                        path: 'classId',
                        select: 'name',
                        options: { lean: true }
                    })
                    .sort({ createdAt: -1 })
                    .skip((safeOptions.page - 1) * safeOptions.limit)
                    .limit(safeOptions.limit)
                    .lean(),
                Note.countDocuments(query)
            ]);

            await SystemLogger.log({
                severity: 'info',
                category: 'notes',
                action: 'get_student_notes',
                performedBy: requestingUserId,
                targetId: studentId,
                details: {
                    studentId,
                    filters: safeOptions
                },
                status: 'success',
                timestamp: new Date()
            });

            return {
                notes: Array.isArray(notes) ? notes.map(note => this.formatNoteResponse(note)) : [],
                pagination: {
                    total: total || 0,
                    page: safeOptions.page,
                    limit: safeOptions.limit,
                    pages: Math.ceil((total || 0) / safeOptions.limit)
                }
            };
        } catch (error) {
            if (error instanceof AppError) throw error;
            
            if (error instanceof mongoose.Error.CastError) {
                throw new AppError(400, `Invalid format for field: ${error.path}`);
            }
            
            throw new AppError(500, `Error fetching student notes: ${error instanceof Error 
                ? error.message : 'Unknown error'}`);
        }
    }

    static async getClassNotes(
        classId: string,
        options: {
            page: number;
            limit: number;
            type?: NoteCategory;
            fromDate?: Date;
            toDate?: Date;
        },
        requestingUserId: string,
        userRole: string
    ) {
        try {
            // Validate class ID
            if (!classId || !mongoose.Types.ObjectId.isValid(classId)) {
                throw new AppError(400, 'Invalid class ID');
            }

            // Ensure pagination parameters are valid
            const safeOptions = {
                page: Math.max(1, options.page || 1),
                limit: Math.min(Math.max(1, options.limit || 10), 100),
                type: options.type,
                fromDate: options.fromDate instanceof Date ? options.fromDate : 
                    options.fromDate ? new Date(options.fromDate) : undefined,
                toDate: options.toDate instanceof Date ? options.toDate : 
                    options.toDate ? new Date(options.toDate) : undefined
            };

            // Check for valid dates
            if (safeOptions.fromDate && isNaN(safeOptions.fromDate.getTime())) {
                delete safeOptions.fromDate;
            }
            
            if (safeOptions.toDate && isNaN(safeOptions.toDate.getTime())) {
                delete safeOptions.toDate;
            }

            const query: FilterQuery<INote> = {
                classId: new mongoose.Types.ObjectId(classId),
                ...this.getVisibilityQuery(userRole, requestingUserId)
            };

            if (safeOptions.type) {
                query.type = safeOptions.type;
            }

            if (safeOptions.fromDate || safeOptions.toDate) {
                query.createdAt = {};
                if (safeOptions.fromDate) query.createdAt.$gte = safeOptions.fromDate;
                if (safeOptions.toDate) query.createdAt.$lte = safeOptions.toDate;
            }

            const [notes, total] = await Promise.all([
                Note.find(query)
                    .populate({
                        path: 'createdBy',
                        select: 'username'
                    })
                    .populate({
                        path: 'modifiedBy',
                        select: 'username'
                    })
                    .populate({
                        path: 'studentId',
                        select: 'name',
                        options: { lean: true }
                    })
                    .sort({ createdAt: -1 })
                    .skip((safeOptions.page - 1) * safeOptions.limit)
                    .limit(safeOptions.limit)
                    .lean(),
                Note.countDocuments(query)
            ]);

            await SystemLogger.log({
                severity: 'info',
                category: 'notes',
                action: 'get_class_notes',
                performedBy: requestingUserId,
                targetId: classId,
                details: {
                    classId,
                    filters: safeOptions
                },
                status: 'success',
                timestamp: new Date()
            });

            return {
                notes: Array.isArray(notes) ? notes.map(note => this.formatNoteResponse(note)) : [],
                pagination: {
                    total: total || 0,
                    page: safeOptions.page,
                    limit: safeOptions.limit,
                    pages: Math.ceil((total || 0) / safeOptions.limit)
                }
            };
        } catch (error) {
            if (error instanceof AppError) throw error;
            
            if (error instanceof mongoose.Error.CastError) {
                throw new AppError(400, `Invalid format for field: ${error.path}`);
            }
            
            throw new AppError(500, `Error fetching class notes: ${error instanceof Error 
                ? error.message : 'Unknown error'}`);
        }
    }

    // Bulk operations and search
    static async bulkOperation(
        operation: NoteBulkOperationDTO,
        performedBy: string,
        userRole: string
    ) {
        try {
            // Validate inputs
            if (!operation || !operation.noteIds || !Array.isArray(operation.noteIds) || operation.noteIds.length === 0) {
                throw new AppError(400, 'Note IDs are required for bulk operations');
            }

            if (!operation.operation) {
                throw new AppError(400, 'Operation type is required');
            }

            if (!performedBy || !mongoose.Types.ObjectId.isValid(performedBy)) {
                throw new AppError(400, 'Valid performer ID is required');
            }

            // Validate all note IDs
            const validNoteIds = operation.noteIds.filter(id => mongoose.Types.ObjectId.isValid(id));
            if (validNoteIds.length !== operation.noteIds.length) {
                throw new AppError(400, 'One or more invalid note IDs');
            }

            const notes = await Note.find({
                _id: { $in: validNoteIds.map(id => new mongoose.Types.ObjectId(id)) }
            })
                .populate({
                    path: 'createdBy',
                    select: 'username'
                })
                .populate({
                    path: 'modifiedBy',
                    select: 'username'
                })
                .populate({
                    path: 'studentId',
                    select: 'name',
                    options: { lean: true }
                })
                .populate({
                    path: 'classId',
                    select: 'name',
                    options: { lean: true }
                });

            if (!notes.length) {
                throw new AppError(404, 'No notes found with the provided IDs');
            }

            if (notes.length !== validNoteIds.length) {
                throw new AppError(400, 'One or more notes not found');
            }

            // Track notes with access issues
            const accessResults = await Promise.all(
                notes.map(async note => {
                    try {
                        const hasAccess = await note.validateVisibilityAccess(
                            new mongoose.Types.ObjectId(performedBy),
                            userRole
                        );
                        return { noteId: note._id.toString(), hasAccess };
                    } catch (error) {
                        return { noteId: note._id.toString(), hasAccess: false, error };
                    }
                })
            );

            const unauthorizedNotes = accessResults.filter(result => !result.hasAccess);
            if (unauthorizedNotes.length > 0) {
                throw new AppError(403, `Not authorized to modify ${unauthorizedNotes.length} notes`);
            }

            // Validate operation-specific parameters
            if (operation.operation === 'updateVisibility' && !operation.value) {
                throw new AppError(400, 'Visibility value is required for updateVisibility operation');
            }

            if (operation.operation === 'updateTags' && (!operation.value || !Array.isArray(operation.value))) {
                throw new AppError(400, 'Tags array is required for updateTags operation');
            }

            // Execute the operations with proper error handling
            const results = await Promise.allSettled(
                notes.map(async note => {
                    try {
                        switch (operation.operation) {
                            case 'delete':
                                return note.deleteOne();
                            case 'updateVisibility':
                                if (typeof operation.value !== 'string' || 
                                    !['teacher_only', 'all_staff', 'manager_only'].includes(operation.value)) {
                                    throw new Error('Invalid visibility value');
                                }
                                note.visibility = operation.value;
                                return note.save();
                            case 'updateTags':
                                if (!Array.isArray(operation.value)) {
                                    throw new Error('Tags must be an array');
                                }
                                const filteredTags = operation.value.filter(
                                    tag => tag !== null && tag !== undefined && 
                                    typeof tag === 'string' && tag.trim().length > 0
                                );
                                note.tags = [...new Set(filteredTags)].slice(0, 10); // Limit to 10 unique tags
                                return note.save();
                            default:
                                throw new Error('Invalid operation');
                        }
                    } catch (error) {
                        throw error instanceof Error ? error : new Error('Unknown error');
                    }
                })
            );

            const summary = {
                total: notes.length,
                successful: results.filter(r => r.status === 'fulfilled').length,
                failed: results.filter(r => r.status === 'rejected').length,
                errors: results
                    .map((r, i) => r.status === 'rejected' ?
                        { 
                            noteId: notes[i]._id.toString(), 
                            error: (r as PromiseRejectedResult).reason instanceof Error 
                                ? (r as PromiseRejectedResult).reason.message 
                                : 'Unknown error'
                        } :
                        null)
                    .filter(Boolean)
            };

            await SystemLogger.log({
                severity: 'info',
                category: 'notes',
                action: 'bulk_operation',
                performedBy,
                details: {
                    operation: operation.operation,
                    summary,
                    noteIds: operation.noteIds
                },
                status: 'success',
                timestamp: new Date()
            });

            return summary;
        } catch (error) {
            if (error instanceof AppError) throw error;
            
            if (error instanceof mongoose.Error.ValidationError) {
                throw new AppError(400, `Validation error: ${Object.values(error.errors)
                    .map(e => e.message).join(', ')}`);
            }
            
            if (error instanceof mongoose.Error.CastError) {
                throw new AppError(400, `Invalid format for field: ${error.path}`);
            }
            
            throw new AppError(500, `Error performing bulk operation: ${error instanceof Error 
                ? error.message : 'Unknown error'}`);
        }
    }

    // Search and export functionality
    static async searchNotes(
        options: NoteSearchOptions,
        requestingUserId: string,
        userRole: string
    ) {
        try {
            // Apply defaults and ensure all parameters have safe values
            const {
                search,
                searchFields = ['content', 'tags'],
                includeArchived = false,
                groupBy,
                page = 1,
                limit = 10,
                type,
                fromDate,
                toDate
            } = options;

            // Validate search parameters
            const safeOptions = {
                search: search && typeof search === 'string' ? search.trim() : '',
                searchFields: Array.isArray(searchFields) 
                    ? searchFields.filter(field => ['content', 'tags'].includes(field))
                    : ['content', 'tags'],
                includeArchived: !!includeArchived,
                groupBy: typeof groupBy === 'string' && 
                    ['student', 'class', 'type', 'date'].includes(groupBy) 
                    ? groupBy 
                    : undefined,
                page: Math.max(1, Number(page) || 1),
                limit: Math.min(Math.max(1, Number(limit) || 10), 100),
                type: type,
                fromDate: fromDate instanceof Date ? fromDate : 
                    fromDate ? new Date(fromDate) : undefined,
                toDate: toDate instanceof Date ? toDate : 
                    toDate ? new Date(toDate) : undefined
            };

            const query: FilterQuery<INote> = {
                ...this.getVisibilityQuery(userRole, requestingUserId)
            };

            // Add search criteria with proper validation
            if (safeOptions.search && safeOptions.search.length >= 2) {
                const searchQuery: any[] = [];
                if (safeOptions.searchFields.includes('content')) {
                    searchQuery.push({ content: { $regex: safeOptions.search, $options: 'i' } });
                }
                if (safeOptions.searchFields.includes('tags')) {
                    searchQuery.push({ tags: { $regex: safeOptions.search, $options: 'i' } });
                }
                if (searchQuery.length > 0) {
                    query.$or = searchQuery;
                }
            }

            // Apply additional filters with validation
            if (type) {
                query.type = type;
            }

            // Handle date range safely
            if (safeOptions.fromDate || safeOptions.toDate) {
                query.createdAt = {};
                
                if (safeOptions.fromDate && !isNaN(safeOptions.fromDate.getTime())) {
                    query.createdAt.$gte = safeOptions.fromDate;
                }
                
                if (safeOptions.toDate && !isNaN(safeOptions.toDate.getTime())) {
                    query.createdAt.$lte = safeOptions.toDate;
                }

                // If createdAt object is empty, remove it to avoid query issues
                if (Object.keys(query.createdAt).length === 0) {
                    delete query.createdAt;
                }
            }

            let notes;
            let total;

            // Handle grouping if specified
            if (safeOptions.groupBy) {
                try {
                    const groupField = safeOptions.groupBy === 'date' 
                        ? { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } } 
                        : `${safeOptions.groupBy}Id`;
                    
                    const aggregation = await Note.aggregate([
                        { $match: query },
                        {
                            $group: {
                                _id: groupField,
                                notes: { $push: '$$ROOT' },
                                count: { $sum: 1 }
                            }
                        },
                        { $sort: { _id: 1 } },
                        { $skip: (safeOptions.page - 1) * safeOptions.limit },
                        { $limit: safeOptions.limit }
                    ]);

                    const totalGroups = await Note.aggregate([
                        { $match: query },
                        { $group: { _id: groupField } },
                        { $count: 'total' }
                    ]);

                    notes = aggregation || [];
                    total = totalGroups.length > 0 ? totalGroups[0]?.total || 0 : 0;
                } catch (aggregationError) {
                    console.error('Aggregation error:', aggregationError);
                    // Fallback to non-grouped query if aggregation fails
                    notes = [];
                    total = 0;
                }
            } else {
                // Standard query without grouping
                [notes, total] = await Promise.all([
                    Note.find(query)
                        .populate({
                            path: 'createdBy',
                            select: 'username'
                        })
                        .populate({
                            path: 'modifiedBy',
                            select: 'username'
                        })
                        .populate({
                            path: 'studentId',
                            select: 'name',
                            options: { lean: true }
                        })
                        .populate({
                            path: 'classId',
                            select: 'name',
                            options: { lean: true }
                        })
                        .sort({ createdAt: -1 })
                        .skip((safeOptions.page - 1) * safeOptions.limit)
                        .limit(safeOptions.limit)
                        .lean(),
                    Note.countDocuments(query)
                ]);

                // Format notes safely
                notes = Array.isArray(notes) ? notes.map(note => this.formatNoteResponse(note)) : [];
            }

            await SystemLogger.log({
                severity: 'info',
                category: 'notes',
                action: 'search_notes',
                performedBy: requestingUserId,
                details: {
                    searchCriteria: {
                        ...safeOptions,
                        // Don't log the full search query for privacy
                        searchTermLength: safeOptions.search?.length || 0
                    },
                    resultCount: Array.isArray(notes) ? notes.length : 0
                },
                status: 'success',
                timestamp: new Date()
            });

            return {
                notes: notes || [],
                pagination: {
                    total: total || 0,
                    page: safeOptions.page,
                    limit: safeOptions.limit,
                    pages: Math.ceil((total || 0) / safeOptions.limit)
                }
            };
        } catch (error) {
            if (error instanceof AppError) throw error;
            
            if (error instanceof mongoose.Error.ValidationError) {
                throw new AppError(400, `Validation error: ${Object.values(error.errors)
                    .map(e => e.message).join(', ')}`);
            }
            
            if (error instanceof mongoose.Error.CastError) {
                throw new AppError(400, `Invalid format for field: ${error.path}`);
            }
            
            throw new AppError(500, `Error searching notes: ${error instanceof Error 
                ? error.message : 'Unknown error'}`);
        }
    }

    static async exportNotes(
        options: NoteExportOptions,
        exportedBy: string,
        userRole: string
    ): Promise<string> {
        try {
            // Validate export parameters
            if (!exportedBy || !mongoose.Types.ObjectId.isValid(exportedBy)) {
                throw new AppError(400, 'Valid exporter ID is required');
            }

            const safeOptions = {
                format: options.format === 'json' ? 'json' : 'csv',
                fields: Array.isArray(options.fields) ? options.fields.filter(f => f && typeof f === 'string') : undefined,
                includeHistory: !!options.includeHistory,
                dateRange: options.dateRange && 
                    options.dateRange.start instanceof Date && 
                    options.dateRange.end instanceof Date ? 
                    {
                        start: options.dateRange.start,
                        end: options.dateRange.end
                    } : undefined,
                groupBy: typeof options.groupBy === 'string' && 
                    ['student', 'class', 'type', 'date'].includes(options.groupBy) ? 
                    options.groupBy as 'student' | 'class' | 'type' | 'date' : undefined
            };

            const query: FilterQuery<INote> = {
                ...this.getVisibilityQuery(userRole, exportedBy)
            };

            // Apply date filter if provided
            if (safeOptions.dateRange) {
                // Validate date range
                if (isNaN(safeOptions.dateRange.start.getTime()) || isNaN(safeOptions.dateRange.end.getTime())) {
                    throw new AppError(400, 'Invalid date range');
                }
                
                // Ensure end date is after start date
                if (safeOptions.dateRange.end < safeOptions.dateRange.start) {
                    throw new AppError(400, 'End date must be after start date');
                }
                
                query.createdAt = {
                    $gte: safeOptions.dateRange.start,
                    $lte: safeOptions.dateRange.end
                };
            }

            const notes = await Note.find(query)
                .populate({
                    path: 'createdBy',
                    select: 'username'
                })
                .populate({
                    path: 'modifiedBy',
                    select: 'username'
                })
                .populate({
                    path: 'studentId',
                    select: 'name',
                    options: { lean: true }
                })
                .populate({
                    path: 'classId',
                    select: 'name',
                    options: { lean: true }
                })
                .sort({ createdAt: -1 })
                .lean();

            // Format the export data with safety checks
            let exportData = Array.isArray(notes) ? notes.map(note => {
                const baseData = this.formatNoteResponse(note);
                if (safeOptions.includeHistory) {
                    // Ensure modification history is a valid array
                    (baseData as any).modificationHistory = Array.isArray(note.modificationHistory) ?
                        note.modificationHistory.map(record => ({
                            modifiedBy: record?.modifiedBy?.toString() || '',
                            timestamp: record?.timestamp instanceof Date ? record.timestamp : new Date(),
                            changes: record?.changes || {}
                        })) : [];
                }
                return baseData;
            }) : [];

            if (safeOptions.groupBy) {
                exportData = this.groupNotesByField(exportData, safeOptions.groupBy);
            }

            await SystemLogger.log({
                severity: 'info',
                category: 'notes',
                action: 'export_notes',
                performedBy: exportedBy,
                details: {
                    format: safeOptions.format,
                    resultCount: exportData.length,
                    includeHistory: safeOptions.includeHistory,
                    groupBy: safeOptions.groupBy
                },
                status: 'success',
                timestamp: new Date()
            });

            // Convert to the requested format
            return safeOptions.format === 'json'
                ? JSON.stringify(exportData, null, 2)
                : this.convertToCSV(exportData, safeOptions.fields || 
                    (exportData.length > 0 ? Object.keys(exportData[0]) : []));
        } catch (error) {
            if (error instanceof AppError) throw error;
            
            if (error instanceof mongoose.Error.ValidationError) {
                throw new AppError(400, `Validation error: ${Object.values(error.errors)
                    .map(e => e.message).join(', ')}`);
            }
            
            if (error instanceof mongoose.Error.CastError) {
                throw new AppError(400, `Invalid format for field: ${error.path}`);
            }
            
            throw new AppError(500, `Error exporting notes: ${error instanceof Error 
                ? error.message : 'Unknown error'}`);
        }
    }

    /**
     * Validates that all entities referenced in a note actually exist in the database.
     * Handles both student, class, and related entity references.
     * 
     * @param noteData The note data to validate
     * @throws AppError if any referenced entity doesn't exist
     */
    private static async validateEntityReferences(noteData: CreateNoteDTO): Promise<void> {
        try {
            if (!noteData) {
                throw new AppError(400, 'Note data is required');
            }

            // Validate student reference if provided
            if (noteData.studentId) {
                if (!mongoose.Types.ObjectId.isValid(noteData.studentId)) {
                    throw new AppError(400, 'Invalid student ID format');
                }

                const student = await Student.findById(noteData.studentId);
                if (!student) {
                    throw new AppError(404, 'Referenced student not found');
                }
            }

            // Validate class reference if provided
            if (noteData.classId) {
                if (!mongoose.Types.ObjectId.isValid(noteData.classId)) {
                    throw new AppError(400, 'Invalid class ID format');
                }

                const classDoc = await Class.findById(noteData.classId);
                if (!classDoc) {
                    throw new AppError(404, 'Referenced class not found');
                }
            }

            // Validate related entity reference if provided
            if (noteData.relatedTo) {
                if (!noteData.relatedTo.type || !noteData.relatedTo.id) {
                    throw new AppError(400, 'Both type and id are required for related entity');
                }

                if (!mongoose.Types.ObjectId.isValid(noteData.relatedTo.id)) {
                    throw new AppError(400, 'Invalid related entity ID format');
                }

                const type = typeof noteData.relatedTo.type === 'string' 
                    ? noteData.relatedTo.type.toLowerCase() 
                    : '';
                let modelName: string;

                // Get the correct model name
                if (Object.prototype.hasOwnProperty.call(entityTypeToModel, type)) {
                    modelName = entityTypeToModel[type];
                } else {
                    modelName = noteData.relatedTo.type;
                }

                try {
                    // Check if model exists
                    if (!modelName || typeof modelName !== 'string') {
                        throw new AppError(400, 'Invalid related entity type');
                    }

                    // Since we can't guarantee the model exists without risking an exception
                    try {
                        const Model = mongoose.model(modelName);
                        const entity = await Model.findById(noteData.relatedTo.id);
                        if (!entity) {
                            throw new AppError(404, `Referenced ${noteData.relatedTo.type} not found`);
                        }
                    } catch (modelError) {
                        // Specifically catch the MissingSchemaError
                        if (modelError && (modelError as Error).name === 'MissingSchemaError') {
                            throw new AppError(400, `Invalid model type: ${modelName}`);
                        }
                        throw modelError;
                    }
                } catch (err) {
                    // Type guard to safely access error properties
                    const error = err as Error;
                    if (error && error.name === 'MissingSchemaError') {
                        throw new AppError(400, `Invalid model type: ${modelName}`);
                    }
                    throw error;
                }
            }

            // Ensure at least one reference exists
            if (!noteData.studentId && !noteData.classId && !noteData.relatedTo) {
                throw new AppError(400, 'Note must be associated with at least one entity (student, class, or related entity)');
            }
        } catch (error) {
            // Forward AppErrors
            if (error instanceof AppError) {
                throw error;
            }

            // Convert other errors to AppError with appropriate message
            console.error('Entity reference validation error:', error);
            throw new AppError(
                500,
                `Error validating entity references: ${error instanceof Error ? error.message : 'Unknown error'}`
            );
        }
    }

    private static formatNoteResponse(note: any): NoteResponseDTO {
        if (!note) {
            return {
                id: '',
                type: 'general', // Default type
                visibility: 'teacher_only', // Default visibility
                content: '',
                tags: [],
                createdBy: {
                    id: '',
                    username: 'Unknown'
                },
                createdAt: new Date(),
                modifiedAt: new Date(),
                modifiedBy: {
                    id: '',
                    username: 'Unknown'
                },
                modificationHistory: []
            };
        }

        try {
            // Safe getters with defaults and null checks
            const getId = (obj: any): string => {
                if (!obj) return '';
                return obj._id ? obj._id.toString() : obj.toString();
            };

            const getUsername = (obj: any): string => {
                return obj?.username || 'Unknown';
            };

            const getName = (obj: any): string => {
                return obj?.name || 'Unknown';
            };

            // Process tags safely
            const tags = Array.isArray(note.tags) ? 
                note.tags.filter(tag => tag !== null && tag !== undefined) : [];

            // Safely build response object with null coalescence
            return {
                id: getId(note._id),
                type: note.type ?? 'general',
                visibility: note.visibility ?? 'teacher_only',
                content: note.content ?? '',
                tags: tags,
                createdBy: {
                    id: getId(note.createdBy),
                    username: getUsername(note.createdBy)
                },
                createdAt: note.createdAt instanceof Date ? note.createdAt : new Date(),
                modifiedAt: note.modifiedAt instanceof Date ? note.modifiedAt : new Date(),
                modifiedBy: {
                    id: getId(note.modifiedBy),
                    username: getUsername(note.modifiedBy)
                },
                student: note.studentId ? {
                    id: getId(note.studentId),
                    name: getName(note.studentId)
                } : undefined,
                class: note.classId ? {
                    id: getId(note.classId),
                    name: getName(note.classId)
                } : undefined,
                relatedTo: note.relatedTo ? {
                    type: note.relatedTo.type ?? 'unknown',
                    id: getId(note.relatedTo.id)
                } : undefined,
                modificationHistory: Array.isArray(note.modificationHistory)
                    ? note.modificationHistory.map((record: any) => ({
                        modifiedBy: record?.modifiedBy ? getId(record.modifiedBy) : '',
                        timestamp: record?.timestamp instanceof Date ? record.timestamp : new Date(),
                        changes: record?.changes ?? {}
                    }))
                    : []
            };
        } catch (error) {
            console.error('Error formatting note response:', error);
            
            // Return a safe default object rather than throwing
            return {
                id: note?._id?.toString() ?? '',
                type: 'general',
                visibility: 'teacher_only',
                content: note?.content ?? '',
                tags: [],
                createdBy: {
                    id: '',
                    username: 'Error formatting note'
                },
                createdAt: new Date(),
                modifiedAt: new Date(),
                modifiedBy: {
                    id: '',
                    username: 'Error formatting note'
                },
                modificationHistory: []
            };
        }
    }

    private static groupNotesByField(notes: any[], groupBy: string): any[] {
        if (!Array.isArray(notes) || notes.length === 0) {
            return [];
        }

        try {
            const grouped = notes.reduce((acc: Record<string, any[]>, note: any) => {
                if (!note) return acc;

                let key: string;
                switch (groupBy) {
                    case 'date':
                        key = note.createdAt instanceof Date 
                            ? note.createdAt.toISOString().split('T')[0] 
                            : 'Invalid Date';
                        break;
                    case 'student':
                        key = note.student?.name || 'Unassigned';
                        break;
                    case 'class':
                        key = note.class?.name || 'Unassigned';
                        break;
                    case 'type':
                        key = note.type || 'Unknown';
                        break;
                    default:
                        key = 'Other';
                }
                
                if (!acc[key]) {
                    acc[key] = [];
                }
                
                acc[key].push(note);
                return acc;
            }, {});

            return Object.entries(grouped).map(([key, groupNotes]) => ({
                group: key,
                notes: Array.isArray(groupNotes) ? groupNotes : [],
                count: Array.isArray(groupNotes) ? groupNotes.length : 0
            }));
        } catch (error) {
            console.error('Error grouping notes:', error);
            return notes; // Return the original array on error
        }
    }

    private static convertToCSV(data: any[], fields: string[]): string {
        if (!Array.isArray(data) || data.length === 0) {
            return 'No data to export';
        }

        try {
            // Filter to ensure we have valid fields
            const validFields = fields.filter(field => typeof field === 'string' && field.trim().length > 0);
            
            if (validFields.length === 0) {
                // If no valid fields, extract them from the first data item
                const sampleItem = data[0];
                if (sampleItem && typeof sampleItem === 'object') {
                    validFields.push(...Object.keys(sampleItem));
                } else {
                    return 'Cannot determine fields for CSV export';
                }
            }
            
            const header = validFields.join(',');
            const rows = data.map(item => {
                if (!item) return validFields.map(() => '').join(',');
                
                return validFields.map(field => {
                    try {
                        const value = this.getNestedValue(item, field);
                        return this.formatCSVValue(value);
                    } catch (error) {
                        return ''; // Return empty string for any access errors
                    }
                }).join(',');
            });
            
            return [header, ...rows].join('\n');
        } catch (error) {
            console.error('Error converting to CSV:', error);
            return 'Error generating CSV: ' + (error instanceof Error ? error.message : 'Unknown error');
        }
    }

    private static getNestedValue(obj: any, path: string): any {
        if (!obj || !path) return '';
        
        try {
            return path.split('.').reduce((current, key) => {
                if (current === undefined || current === null) return '';
                return current[key];
            }, obj);
        } catch (error) {
            return '';
        }
    }

    private static formatCSVValue(value: any): string {
        if (value === null || value === undefined) return '';
        
        if (value instanceof Date) {
            return `"${value.toISOString()}"`;
        }
        
        if (typeof value === 'object') {
            try {
                return `"${JSON.stringify(value).replace(/"/g, '""')}"`;
            } catch (error) {
                return '"{Object}"';
            }
        }
        
        // Handle strings with quotes and commas properly
        return typeof value === 'string' 
            ? `"${value.replace(/"/g, '""')}"` 
            : `"${String(value)}"`;
    }
}