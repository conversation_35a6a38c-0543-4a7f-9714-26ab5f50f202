
import MainLayout from "@/components/layout/MainLayout";
import RoomDetailView from "@/components/rooms/RoomDetailView";
import { hasRole } from "@/lib/auth";
import { Navigate, useParams } from "react-router-dom";

const RoomDetail = () => {
  const { id } = useParams<{ id: string }>();
  
  // Check if user has proper permissions
  const canViewRooms = hasRole(['SuperAdmin', 'Manager', 'Secretary', 'Teacher']);

  if (!canViewRooms) {
    return <Navigate to="/dashboard" replace />;
  }

  return (
    <MainLayout>
      <RoomDetailView roomId={id || ''} />
    </MainLayout>
  );
};

export default RoomDetail;
