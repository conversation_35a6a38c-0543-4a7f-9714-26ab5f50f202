// server/src/routes/user.routes.ts
import express from 'express';
import { UserController } from '../controllers/user.controller';
import { authenticateToken, authorizeRoles } from '../middleware/auth.middleware';
import { validate } from '../middleware/validate.middleware';
import { userValidation } from '../validations/user.validation';
import { catchAsync } from '../middleware/error.middleware';
import { apiLimiter } from '../middleware/rateLimiter.middleware';

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Special routes that need to come BEFORE /:id routes
router.get(
  '/export',
  authorizeRoles('superAdmin', 'manager'),
  catchAsync(UserController.exportUserData)
);

router.get(
  '/validate/username',
  apiLimiter,
  catchAsync(UserController.validateUsername)
);

// Get users with role-based access
router.get(
  '/',
  authorizeRoles('superAdmin', 'manager'),
  catchAsync(UserController.getUsers)
);

// Regular ID-based routes
router.get(
  '/:id',
  authorizeRoles('superAdmin', 'manager'),
  catchAsync(UserController.getUserById)
);

router.patch(
  '/:id/status',
  authorizeRoles('superAdmin'),
  validate(userValidation.updateStatus),
  catchAsync(UserController.updateUserStatus)
);

router.patch(
  '/:id/role',
  authorizeRoles('superAdmin'),
  validate(userValidation.updateRole),
  catchAsync(UserController.updateUserRole)
);

router.get(
  '/:id/activities',
  authorizeRoles('superAdmin', 'manager'),
  catchAsync(UserController.getUserActivities)
);

// Bulk operations
router.post(
  '/bulk/status',
  authorizeRoles('superAdmin'),
  validate(userValidation.bulkUpdateStatus),
  catchAsync(UserController.bulkUpdateStatus)
);

router.post(
  '/bulk/role',
  authorizeRoles('superAdmin'),
  validate(userValidation.bulkUpdateRole),
  catchAsync(UserController.bulkUpdateRole)
);

export default router;