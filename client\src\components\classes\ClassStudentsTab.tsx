
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle, CardDescription } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { format } from "date-fns";
import { Progress } from "@/components/ui/progress";
import { ClassCapacity, StudentHistory } from "@/types/class";

interface ClassStudentsTabProps {
  capacity: ClassCapacity;
  studentHistory: StudentHistory[];
}

const ClassStudentsTab = ({ capacity, studentHistory }: ClassStudentsTabProps) => {
  const [studentTab, setStudentTab] = useState("current");
  
  // Calculate capacity percentage
  const capacityPercentage = (capacity.current / capacity.total) * 100;
  
  // Filter current students (no leave date)
  const currentStudents = studentHistory.filter(student => !student.leaveDate);
  
  // Filter former students (have leave date)
  const formerStudents = studentHistory.filter(student => student.leaveDate);
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Students</CardTitle>
        <CardDescription>
          Students enrolled in this class
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Capacity Visualization */}
        <div className="space-y-2">
          <div className="flex justify-between">
            <span className="text-sm font-medium">Class Capacity</span>
            <span className="text-sm">{capacity.current} / {capacity.total} Students</span>
          </div>
          <Progress value={capacityPercentage} className="h-2" />
          <p className="text-sm text-muted-foreground">
            {capacity.available} spots available
          </p>
        </div>
        
        {/* Current/Former Students Tabs */}
        <Tabs value={studentTab} onValueChange={setStudentTab}>
          <TabsList>
            <TabsTrigger value="current">Current Students ({currentStudents.length})</TabsTrigger>
            <TabsTrigger value="former">Former Students ({formerStudents.length})</TabsTrigger>
          </TabsList>
          
          {/* Current Students Table */}
          <TabsContent value="current">
            {currentStudents.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No students are currently enrolled in this class.
              </div>
            ) : (
              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Student Name</TableHead>
                      <TableHead>Join Date</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {currentStudents.map((student) => (
                      <TableRow key={student.studentId}>
                        <TableCell className="font-medium">{student.studentName}</TableCell>
                        <TableCell>{format(new Date(student.joinDate), "MMM d, yyyy")}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </TabsContent>
          
          {/* Former Students Table */}
          <TabsContent value="former">
            {formerStudents.length === 0 ? (
              <div className="text-center py-8 text-muted-foreground">
                No former students to display.
              </div>
            ) : (
              <div className="border rounded-md">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Student Name</TableHead>
                      <TableHead>Join Date</TableHead>
                      <TableHead>Leave Date</TableHead>
                      <TableHead>Reason</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {formerStudents.map((student) => (
                      <TableRow key={student.studentId}>
                        <TableCell className="font-medium">{student.studentName}</TableCell>
                        <TableCell>{format(new Date(student.joinDate), "MMM d, yyyy")}</TableCell>
                        <TableCell>{format(new Date(student.leaveDate), "MMM d, yyyy")}</TableCell>
                        <TableCell>{student.reason}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default ClassStudentsTab;
