import express from 'express';
import { AuthController } from '../controllers/auth.controller';
import { validate } from '../middleware/validate.middleware';
import { authValidation } from '../validations/auth.validation';
import { authenticateToken, authorizeRoles } from '../middleware/auth.middleware';
import { loginLimiter } from '../middleware/rateLimiter.middleware';
import { catchAsync } from '../middleware/error.middleware';

const router = express.Router();

router.post(
  '/login',
  loginLimiter,
  validate(authValidation.login),
  catchAsync(AuthController.login)
);

router.post(
  '/users',
  authenticateToken,
  authorizeRoles('superAdmin'),
  validate(authValidation.createUser),
  catchAsync(AuthController.createUser)
);

router.post(
  '/reset-password',
  authenticateToken,
  authorizeRoles('superAdmin'),
  validate(authValidation.resetPassword),
  catchAsync(AuthController.resetPassword)
);

router.post(
  '/change-password',
  authenticateToken,
  validate(authValidation.changePassword),
  catchAsync(AuthController.changePassword)
);

router.patch(
  '/users/:id/role',
  authenticateToken,
  authorizeRoles('superAdmin'),
  validate(authValidation.updateRole),
  catchAsync(AuthController.updateRole)
);

router.get(
  '/me',
  authenticateToken,
  catchAsync(AuthController.getCurrentUser)
);

router.post(
  '/logout',
  authenticateToken,
  catchAsync(AuthController.logout)
);

export default router;