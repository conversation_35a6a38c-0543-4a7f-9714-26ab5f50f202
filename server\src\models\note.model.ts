// server/src/models/note.model.ts
import mongoose, { Schema, Document, Types, Model } from 'mongoose';
import {
    INote,
    NoteCategory,
    NoteVisibility,
    RelatedEntityType,
    RelatedModelType,
    entityTypeToModel
} from '../types/notes.types';
import { AppError } from '../types/error.types';
import { canAccessNote, validateUserRole } from '../utils/permissionUtils';


// Interface for the document (instance methods)
export interface INoteDocument extends Document, Omit<INote, '_id'> {
    _id: Types.ObjectId;
    validateVisibilityAccess(userId: Types.ObjectId, userRole: string): Promise<boolean>;
    addModificationRecord(userId: Types.ObjectId, changes: Record<string, any>): Promise<void>;
    archiveNote(archivedBy: Types.ObjectId): Promise<void>;
}

// Interface for the model (static methods)
interface INoteModel extends Model<INoteDocument> {
    findVisibleNotes(userId: Types.ObjectId, userRole: string): Promise<INoteDocument[]>;
    validateNoteAccess(noteId: Types.ObjectId, userId: Types.ObjectId, userRole: string): Promise<boolean>;
}

const noteSchema = new Schema<INoteDocument>(
    {
        type: {
            type: String,
            required: true,
            enum: ['academic', 'behavioral', 'attendance', 'general'],
            index: true
        },
        visibility: {
            type: String,
            required: true,
            enum: ['teacher_only', 'all_staff', 'manager_only'],
            default: 'teacher_only',
            index: true
        },
        content: {
            type: String,
            required: true,
            maxlength: 5000
        },
        tags: [{
            type: String,
            trim: true,
            lowercase: true,
            index: true
        }],
        createdBy: {
            type: Schema.Types.ObjectId,
            ref: 'User',
            required: true,
            index: true
        },
        createdAt: {
            type: Date,
            default: Date.now,
            required: true
        },
        modifiedAt: {
            type: Date,
            default: Date.now
        },
        modifiedBy: {
            type: Schema.Types.ObjectId,
            ref: 'User',
            required: true
        },
        studentId: {
            type: Schema.Types.ObjectId,
            ref: 'Student',
            index: true,
            sparse: true
        },
        classId: {
            type: Schema.Types.ObjectId,
            ref: 'Class',
            index: true,
            sparse: true
        },
        relatedTo: {
            type: {
                type: String,
                validate: {
                    validator: function (v: string) {
                        // Check if it's either a lowercase entity type or capitalized model name
                        return Object.keys(entityTypeToModel).includes(v.toLowerCase()) ||
                            Object.values(entityTypeToModel).includes(v);
                    },
                    message: 'Invalid related entity type'
                }
            },
            id: {
                type: Schema.Types.ObjectId,
                refPath: 'relatedTo.type'
            }
        },
        modificationHistory: [{
            modifiedBy: {
                type: Schema.Types.ObjectId,
                ref: 'User',
                required: true
            },
            timestamp: {
                type: Date,
                default: Date.now,
                required: true
            },
            changes: {
                field: String,
                oldValue: Schema.Types.Mixed,
                newValue: Schema.Types.Mixed
            }
        }]
    },
    {
        timestamps: true,
        toJSON: { virtuals: true },
        toObject: { virtuals: true }
    }
);

// Indexes
noteSchema.index({ content: 'text', tags: 'text' });
noteSchema.index({ createdAt: -1 });
noteSchema.index({ 'relatedTo.type': 1, 'relatedTo.id': 1 });
noteSchema.index({ studentId: 1, createdAt: -1 });
noteSchema.index({ classId: 1, createdAt: -1 });
noteSchema.index({ type: 1, visibility: 1 });

// Add compound indexes for common query patterns
noteSchema.index({ visibility: 1, createdBy: 1, createdAt: -1 }); // For teacher's notes
noteSchema.index({ studentId: 1, type: 1, createdAt: -1 }); // For student notes by type
noteSchema.index({ classId: 1, type: 1, createdAt: -1 }); // For class notes by type

// Add partial indexes for specific conditions
noteSchema.index(
    { visibility: 1, createdAt: -1 },
    { partialFilterExpression: { visibility: 'all_staff' } }
); // For all_staff notes

noteSchema.index(
    { visibility: 1, createdBy: 1, createdAt: -1 },
    { partialFilterExpression: { visibility: 'teacher_only' } }
); // For teacher_only notes

// Optimize text search
noteSchema.index(
    { content: 'text', tags: 'text' },
    {
        weights: { content: 3, tags: 5 },
        name: 'content_tags_text'
    }
);

// Instance methods
noteSchema.methods.validateVisibilityAccess = async function (
    userId: Types.ObjectId,
    userRole: string
): Promise<boolean> {
    if (!validateUserRole(userRole)) {
        throw new AppError(403, `Invalid role: ${userRole}`);
    }

    return canAccessNote(
        this.visibility,
        userRole,
        this.createdBy.equals(userId)
    );
};

noteSchema.methods.addModificationRecord = async function (
    userId: Types.ObjectId,
    changes: Record<string, any>
): Promise<void> {
    this.modificationHistory.push({
        modifiedBy: userId,
        timestamp: new Date(),
        changes: Object.entries(changes).map(([field, { oldValue, newValue }]) => ({
            field,
            oldValue,
            newValue
        }))
    });
    this.modifiedAt = new Date();
    this.modifiedBy = userId;
    await this.save();
};

// Static methods
noteSchema.static('findVisibleNotes', async function (
    userId: Types.ObjectId,
    userRole: string
): Promise<INoteDocument[]> {
    const baseQuery: any = {};

    switch (userRole) {
        case 'superAdmin':
            // Can see all notes
            break;
        case 'manager':
            baseQuery.visibility = { $in: ['all_staff', 'manager_only'] };
            break;
        case 'secretary':
            baseQuery.visibility = 'all_staff';
            break;
        case 'teacher':
            baseQuery.$or = [
                { visibility: 'all_staff' },
                { visibility: 'teacher_only', createdBy: userId }
            ];
            break;
        default:
            throw new AppError(403, 'Invalid role for note access');
    }

    return this.find(baseQuery).sort({ createdAt: -1 });
});

noteSchema.static('validateNoteAccess', async function (
    noteId: Types.ObjectId,
    userId: Types.ObjectId,
    userRole: string
): Promise<boolean> {
    const note = await this.findById(noteId);
    if (!note) {
        throw new AppError(404, 'Note not found');
    }
    return note.validateVisibilityAccess(userId, userRole);
});

// Middleware
noteSchema.pre('save', async function (this: INoteDocument, next) {
    // Validate that at least one reference exists
    if (!this.studentId && !this.classId && !this.relatedTo) {
        throw new AppError(400, 'Note must be associated with at least one entity');
    }

    // Validate tags length
    if (this.tags && this.tags.length > 10) {
        throw new AppError(400, 'Maximum 10 tags allowed per note');
    }

    // Ensure proper reference paths for relatedTo
    if (this.relatedTo && this.relatedTo.type) {
        const inputType = this.relatedTo.type;
        const lowerType = typeof inputType === 'string' ? inputType.toLowerCase() : '';

        if (lowerType && Object.prototype.hasOwnProperty.call(entityTypeToModel, lowerType)) {
            // Need to use type assertion here to satisfy TypeScript
            this.relatedTo.type = entityTypeToModel[lowerType] as RelatedModelType;
        } else if (inputType && Object.values(entityTypeToModel).includes(inputType)) {
            // Type is already in correct format (capitalized)
            // No need to change it
        } else if (inputType) {
            throw new Error(`Invalid related entity type: ${inputType}`);
        }
    }

    next();
});

export const Note = mongoose.model<INoteDocument, INoteModel>('Note', noteSchema);