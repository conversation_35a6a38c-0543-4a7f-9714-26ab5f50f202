
import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { Calendar, Info } from "lucide-react";

interface StudentAttendanceTabProps {
  studentId: string;
}

// Mock attendance data
interface AttendanceRecord {
  id: string;
  date: string;
  className: string;
  status: 'present' | 'absent' | 'late' | 'excused';
  notes?: string;
}

const StudentAttendanceTab = ({ studentId }: StudentAttendanceTabProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [attendanceRecords, setAttendanceRecords] = useState<AttendanceRecord[]>([]);
  const [stats, setStats] = useState({
    present: 0,
    absent: 0,
    late: 0,
    excused: 0,
    total: 0,
    attendanceRate: 0
  });

  useEffect(() => {
    // In a real application, you would fetch attendance data from API
    const loadAttendanceData = async () => {
      setIsLoading(true);
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 800));
      
      const mockData: AttendanceRecord[] = [
        {
          id: "att1",
          date: "2023-11-05",
          className: "Class 10A",
          status: "present"
        },
        {
          id: "att2",
          date: "2023-11-12",
          className: "Class 10A",
          status: "present"
        },
        {
          id: "att3",
          date: "2023-11-19",
          className: "Class 10A",
          status: "absent",
          notes: "Family emergency"
        },
        {
          id: "att4",
          date: "2023-11-26",
          className: "Class 10A",
          status: "late",
          notes: "Arrived 15 minutes late"
        },
        {
          id: "att5",
          date: "2023-12-03",
          className: "Class 10A",
          status: "present"
        },
        {
          id: "att6",
          date: "2023-12-10",
          className: "Class 10A",
          status: "excused",
          notes: "Doctor's appointment"
        },
        {
          id: "att7",
          date: "2023-12-17",
          className: "Class 10A",
          status: "present"
        }
      ];
      
      setAttendanceRecords(mockData);
      
      // Calculate stats
      const present = mockData.filter(r => r.status === 'present').length;
      const absent = mockData.filter(r => r.status === 'absent').length;
      const late = mockData.filter(r => r.status === 'late').length;
      const excused = mockData.filter(r => r.status === 'excused').length;
      const total = mockData.length;
      
      setStats({
        present,
        absent,
        late,
        excused,
        total,
        attendanceRate: Math.round((present / total) * 100)
      });
      
      setIsLoading(false);
    };
    
    loadAttendanceData();
  }, [studentId]);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };
  
  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Attendance Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="p-4 pb-2">
            <CardTitle className="text-base">Present</CardTitle>
          </CardHeader>
          <CardContent className="p-4 pt-0">
            <div className="flex items-end justify-between">
              <span className="text-2xl font-bold">
                {stats.present}
              </span>
              <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                {Math.round((stats.present / stats.total) * 100)}%
              </Badge>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="p-4 pb-2">
            <CardTitle className="text-base">Absent</CardTitle>
          </CardHeader>
          <CardContent className="p-4 pt-0">
            <div className="flex items-end justify-between">
              <span className="text-2xl font-bold">
                {stats.absent}
              </span>
              <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                {Math.round((stats.absent / stats.total) * 100)}%
              </Badge>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="p-4 pb-2">
            <CardTitle className="text-base">Late</CardTitle>
          </CardHeader>
          <CardContent className="p-4 pt-0">
            <div className="flex items-end justify-between">
              <span className="text-2xl font-bold">
                {stats.late}
              </span>
              <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                {Math.round((stats.late / stats.total) * 100)}%
              </Badge>
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="p-4 pb-2">
            <CardTitle className="text-base">Attendance Rate</CardTitle>
          </CardHeader>
          <CardContent className="p-4 pt-0">
            <div className="flex items-end justify-between">
              <span className="text-2xl font-bold">
                {stats.attendanceRate}%
              </span>
              <Badge variant="outline" className={cn(
                stats.attendanceRate >= 90 ? "bg-green-50 text-green-700 border-green-200" :
                stats.attendanceRate >= 75 ? "bg-yellow-50 text-yellow-700 border-yellow-200" :
                "bg-red-50 text-red-700 border-red-200"
              )}>
                {stats.total} Classes
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>
      
      {/* Attendance Records */}
      <Card>
        <CardHeader>
          <CardTitle>Attendance History</CardTitle>
          <CardDescription>
            Complete attendance record for all classes
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Class</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Notes</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {attendanceRecords.length > 0 ? (
                attendanceRecords.map((record) => (
                  <TableRow key={record.id}>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Calendar size={16} className="text-muted-foreground" />
                        {formatDate(record.date)}
                      </div>
                    </TableCell>
                    <TableCell>{record.className}</TableCell>
                    <TableCell>
                      <Badge
                        variant="outline"
                        className={cn(
                          "capitalize",
                          record.status === "present" && "border-green-200 bg-green-50 text-green-700",
                          record.status === "absent" && "border-red-200 bg-red-50 text-red-700",
                          record.status === "late" && "border-yellow-200 bg-yellow-50 text-yellow-700",
                          record.status === "excused" && "border-blue-200 bg-blue-50 text-blue-700"
                        )}
                      >
                        {record.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {record.notes ? (
                        <div className="flex items-center gap-2">
                          <Info size={16} className="text-muted-foreground" />
                          {record.notes}
                        </div>
                      ) : (
                        "—"
                      )}
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={4} className="h-24 text-center">
                    No attendance records found.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};

export default StudentAttendanceTab;
