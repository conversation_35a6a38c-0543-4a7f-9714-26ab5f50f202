
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { 
  PaginatedRoomsResponse, 
  Room, 
  RoomFilters, 
  RoomFeature,
  RoomStatus 
} from "@/types/room";
import { fetchRooms, ROOM_FEATURES } from "@/services/roomService";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { 
  Building, 
  CalendarDays, 
  Clock, 
  Edit, 
  Eye, 
  Filter, 
  Grid, 
  Layout, 
  MapPin, 
  MoreHorizontal, 
  Plus, 
  Search, 
  Users, 
  X 
} from "lucide-react";
import { hasRole } from "@/lib/auth";
import { toast } from "sonner";
import Pagination from "@/components/common/Pagination";
import AvailabilitySearch from "@/components/rooms/AvailabilitySearch";
import { capitalizeFirstLetter } from "@/lib/utils";

const RoomDirectory = () => {
  const navigate = useNavigate();
  
  // State for rooms data and loading
  const [rooms, setRooms] = useState<Room[]>([]);
  const [loading, setLoading] = useState(true);
  const [pagination, setPagination] = useState({
    total: 0,
    page: 1,
    limit: 10,
    pages: 0
  });
  
  // State for filters
  const [filters, setFilters] = useState<RoomFilters>({
    page: 1,
    limit: 10,
    sortBy: 'name',
    sortOrder: 'asc'
  });
  const [searchText, setSearchText] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedFeatures, setSelectedFeatures] = useState<RoomFeature[]>([]);
  
  // State for view type
  const [viewType, setViewType] = useState<'list' | 'grid'>('list');

  // State for dynamic filter options
  const [availableBuildings, setAvailableBuildings] = useState<string[]>([]);
  const [availableFloors, setAvailableFloors] = useState<number[]>([]);

  // Check user permissions
  const canManageRooms = hasRole(['superAdmin', 'manager']);
  
  // Load rooms data
  const loadRooms = async () => {
    setLoading(true);
    try {
      const response = await fetchRooms({
        ...filters,
        features: selectedFeatures.length > 0 ? selectedFeatures : undefined,
        search: searchText || undefined
      });
      setRooms(response.data);
      setPagination(response.pagination);

      // Extract unique buildings and floors for filter options
      const buildings = [...new Set(response.data.map(room => room.building))].sort();
      const floors = [...new Set(response.data.map(room => room.floor))].sort((a, b) => a - b);
      setAvailableBuildings(buildings);
      setAvailableFloors(floors);
    } catch (error) {
      console.error("Error loading rooms:", error);
    } finally {
      setLoading(false);
    }
  };
  
  // Handle page change
  const handlePageChange = (page: number) => {
    setFilters(prev => ({ ...prev, page }));
  };
  
  // Handle sort change
  const handleSortChange = (field: string) => {
    setFilters(prev => ({
      ...prev,
      sortBy: field,
      sortOrder: prev.sortBy === field && prev.sortOrder === 'asc' ? 'desc' : 'asc'
    }));
  };
  
  // Handle search
  const handleSearch = () => {
    setFilters(prev => ({ ...prev, page: 1 }));
    loadRooms();
  };
  
  // Handle filter reset
  const handleResetFilters = () => {
    setSearchText('');
    setSelectedFeatures([]);
    setFilters({
      page: 1,
      limit: 10,
      sortBy: 'name',
      sortOrder: 'asc'
    });
  };
  
  // Handle feature toggle
  const handleFeatureToggle = (feature: RoomFeature) => {
    setSelectedFeatures(prev => 
      prev.includes(feature)
        ? prev.filter(f => f !== feature)
        : [...prev, feature]
    );
  };
  
  // Navigate to room detail
  const handleViewRoom = (roomId: string) => {
    navigate(`/rooms/${roomId}`);
  };
  
  // Navigate to room edit
  const handleEditRoom = (roomId: string) => {
    navigate(`/rooms/${roomId}/edit`);
  };
  
  // Navigate to create room
  const handleCreateRoom = () => {
    navigate('/rooms/create');
  };
  
  // Navigate to schedule maintenance
  const handleScheduleMaintenance = (roomId: string) => {
    navigate(`/rooms/${roomId}?tab=maintenance`);
  };
  
  // Effect to load rooms when filters change
  useEffect(() => {
    loadRooms();
  }, [filters]);
  
  // Status badge component
  const RoomStatusBadge = ({ status }: { status: RoomStatus }) => {
    const getStatusColor = () => {
      switch (status) {
        case 'active':
          return 'bg-green-500';
        case 'maintenance':
          return 'bg-amber-500';
        case 'inactive':
          return 'bg-gray-500';
        default:
          return 'bg-gray-500';
      }
    };
    
    return (
      <Badge className={`${getStatusColor()} capitalize`}>
        {status}
      </Badge>
    );
  };
  
  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Room Management</h1>
          <p className="text-muted-foreground">
            View, search, and manage all rooms in your institution
          </p>
        </div>
        
        {canManageRooms && (
          <Button onClick={handleCreateRoom}>
            <Plus className="mr-2 h-4 w-4" />
            Create Room
          </Button>
        )}
      </div>
      
      <Tabs defaultValue="all" className="w-full">
        <TabsList>
          <TabsTrigger value="all">All Rooms</TabsTrigger>
          <TabsTrigger value="availability">Find Available Room</TabsTrigger>
        </TabsList>
        
        <TabsContent value="all" className="space-y-4">
          <Card>
            <CardHeader className="pb-3">
              <div className="flex flex-col sm:flex-row justify-between gap-4">
                <div className="flex gap-2 w-full sm:w-auto">
                  <div className="relative w-full sm:w-[300px]">
                    <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                    <Input
                      type="search"
                      placeholder="Search rooms..."
                      className="pl-8 w-full"
                      value={searchText}
                      onChange={(e) => setSearchText(e.target.value)}
                      onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                    />
                  </div>
                  
                  <Button 
                    variant="outline" 
                    onClick={() => setShowFilters(!showFilters)}
                    className="flex gap-1 items-center"
                  >
                    <Filter className="h-4 w-4" />
                    <span className="hidden sm:inline">Filters</span>
                  </Button>
                  
                  <div className="flex gap-1">
                    <Button
                      variant={viewType === 'list' ? 'default' : 'outline'}
                      size="icon"
                      onClick={() => setViewType('list')}
                      title="List view"
                    >
                      <Layout className="h-4 w-4" />
                    </Button>
                    <Button
                      variant={viewType === 'grid' ? 'default' : 'outline'}
                      size="icon"
                      onClick={() => setViewType('grid')}
                      title="Grid view"
                    >
                      <Grid className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                <div className="flex items-center gap-2">
                  <Select
                    value={filters.sortBy}
                    onValueChange={(value) => handleSortChange(value)}
                  >
                    <SelectTrigger className="w-[150px]">
                      <SelectValue placeholder="Sort by" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="name">Name</SelectItem>
                      <SelectItem value="building">Building</SelectItem>
                      <SelectItem value="floor">Floor</SelectItem>
                      <SelectItem value="capacity">Capacity</SelectItem>
                      <SelectItem value="status">Status</SelectItem>
                    </SelectContent>
                  </Select>
                  
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleSortChange(filters.sortBy || 'name')}
                    title={filters.sortOrder === 'asc' ? 'Sort ascending' : 'Sort descending'}
                  >
                    {filters.sortOrder === 'asc' ? (
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-arrow-up">
                        <path d="m5 12 7-7 7 7"/>
                        <path d="M12 19V5"/>
                      </svg>
                    ) : (
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-arrow-down">
                        <path d="M12 5v14"/>
                        <path d="m19 12-7 7-7-7"/>
                      </svg>
                    )}
                  </Button>
                </div>
              </div>
              
              {showFilters && (
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 mt-4 pt-4 border-t">
                  <div>
                    <Label htmlFor="status-filter">Status</Label>
                    <Select
                      value={filters.status || ""}
                      onValueChange={(value) => 
                        setFilters(prev => ({ 
                          ...prev, 
                          status: value ? value as RoomStatus : undefined,
                          page: 1
                        }))
                      }
                    >
                      <SelectTrigger id="status-filter">
                        <SelectValue placeholder="All statuses" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">All statuses</SelectItem>
                        <SelectItem value="active">Active</SelectItem>
                        <SelectItem value="maintenance">Maintenance</SelectItem>
                        <SelectItem value="inactive">Inactive</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="building-filter">Building</Label>
                    <Select
                      value={filters.building || ""}
                      onValueChange={(value) => 
                        setFilters(prev => ({ 
                          ...prev, 
                          building: value || undefined,
                          page: 1
                        }))
                      }
                    >
                      <SelectTrigger id="building-filter">
                        <SelectValue placeholder="All buildings" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">All buildings</SelectItem>
                        {availableBuildings.map((building) => (
                          <SelectItem key={building} value={building}>
                            {building}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="floor-filter">Floor</Label>
                    <Select
                      value={filters.floor?.toString() || ""}
                      onValueChange={(value) => 
                        setFilters(prev => ({ 
                          ...prev, 
                          floor: value ? parseInt(value) : undefined,
                          page: 1
                        }))
                      }
                    >
                      <SelectTrigger id="floor-filter">
                        <SelectValue placeholder="All floors" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="">All floors</SelectItem>
                        {availableFloors.map((floor) => (
                          <SelectItem key={floor} value={floor.toString()}>
                            {floor === 0 ? 'Ground Floor' : `Floor ${floor}`}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div>
                    <Label htmlFor="capacity-filter">Min. Capacity</Label>
                    <Input
                      id="capacity-filter"
                      type="number"
                      min="0"
                      placeholder="Minimum capacity"
                      value={filters.minCapacity || ""}
                      onChange={(e) => 
                        setFilters(prev => ({
                          ...prev,
                          minCapacity: e.target.value ? parseInt(e.target.value) : undefined,
                          page: 1
                        }))
                      }
                    />
                  </div>
                  
                  <div className="col-span-full">
                    <Label className="block mb-2">Features</Label>
                    <div className="flex flex-wrap gap-2">
                      {ROOM_FEATURES.map((feature) => (
                        <div key={feature.value} className="flex items-center">
                          <Checkbox 
                            id={`feature-${feature.value}`}
                            checked={selectedFeatures.includes(feature.value as RoomFeature)}
                            onCheckedChange={() => handleFeatureToggle(feature.value as RoomFeature)}
                            className="mr-1.5"
                          />
                          <Label htmlFor={`feature-${feature.value}`} className="text-sm">
                            {feature.label}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div className="col-span-full flex justify-end">
                    <Button
                      variant="outline"
                      onClick={handleResetFilters}
                      className="mr-2"
                    >
                      Reset Filters
                    </Button>
                    <Button onClick={handleSearch}>
                      Apply Filters
                    </Button>
                  </div>
                </div>
              )}
            </CardHeader>
            
            <CardContent>
              {loading ? (
                <div className="py-8 text-center">Loading rooms...</div>
              ) : rooms.length === 0 ? (
                <div className="py-8 text-center">
                  <p className="text-muted-foreground">No rooms found matching your criteria.</p>
                </div>
              ) : viewType === 'list' ? (
                // List view
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="py-3 px-2 text-left">Name</th>
                        <th className="py-3 px-2 text-left">Building</th>
                        <th className="py-3 px-2 text-left">Floor</th>
                        <th className="py-3 px-2 text-left">Features</th>
                        <th className="py-3 px-2 text-center">Capacity</th>
                        <th className="py-3 px-2 text-center">Status</th>
                        <th className="py-3 px-2 text-center">Current Usage</th>
                        <th className="py-3 px-2 text-right">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {rooms.map((room) => (
                        <tr key={room.id} className="border-b hover:bg-muted/50">
                          <td className="py-3 px-2 font-medium">{room.name}</td>
                          <td className="py-3 px-2">{room.building}</td>
                          <td className="py-3 px-2">
                            {room.floor === 0 ? 'Ground Floor' : `Floor ${room.floor}`}
                          </td>
                          <td className="py-3 px-2">
                            <div className="flex flex-wrap gap-1">
                              {room.features.slice(0, 3).map((feature) => (
                                <Badge key={feature} variant="outline" className="text-xs">
                                  {capitalizeFirstLetter(feature)}
                                </Badge>
                              ))}
                              {room.features.length > 3 && (
                                <Badge variant="outline" className="text-xs">
                                  +{room.features.length - 3}
                                </Badge>
                              )}
                            </div>
                          </td>
                          <td className="py-3 px-2 text-center">{room.capacity}</td>
                          <td className="py-3 px-2 text-center">
                            <RoomStatusBadge status={room.status} />
                          </td>
                          <td className="py-3 px-2 text-center">
                            {room.currentSchedule && room.currentSchedule.timeSlots.length > 0 ? (
                              <span className="text-xs text-muted-foreground">
                                {room.currentSchedule.timeSlots[0].class.name || 'Class in progress'}
                              </span>
                            ) : (
                              <span className="text-xs text-muted-foreground">Available</span>
                            )}
                          </td>
                          <td className="py-3 px-2 text-right">
                            <div className="flex justify-end gap-1">
                              <Button 
                                size="icon" 
                                variant="ghost" 
                                onClick={() => handleViewRoom(room.id)}
                                title="View details"
                              >
                                <Eye className="h-4 w-4" />
                              </Button>
                              
                              {canManageRooms && (
                                <Button 
                                  size="icon" 
                                  variant="ghost" 
                                  onClick={() => handleEditRoom(room.id)}
                                  title="Edit room"
                                >
                                  <Edit className="h-4 w-4" />
                                </Button>
                              )}
                              
                              <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                  <Button size="icon" variant="ghost" title="More actions">
                                    <MoreHorizontal className="h-4 w-4" />
                                  </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent align="end">
                                  <DropdownMenuItem onClick={() => handleViewRoom(room.id)}>
                                    View Details
                                  </DropdownMenuItem>
                                  {canManageRooms && (
                                    <>
                                      <DropdownMenuItem onClick={() => handleEditRoom(room.id)}>
                                        Edit Room
                                      </DropdownMenuItem>
                                      <DropdownMenuItem onClick={() => handleScheduleMaintenance(room.id)}>
                                        Schedule Maintenance
                                      </DropdownMenuItem>
                                    </>
                                  )}
                                </DropdownMenuContent>
                              </DropdownMenu>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                // Grid view
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  {rooms.map((room) => (
                    <Card key={room.id} className="overflow-hidden">
                      <CardHeader className="pb-2">
                        <div className="flex justify-between items-start">
                          <div>
                            <CardTitle className="text-lg flex items-center">
                              {room.name}
                            </CardTitle>
                            <CardDescription>
                              {room.building}, {room.floor === 0 ? 'Ground Floor' : `Floor ${room.floor}`}
                            </CardDescription>
                          </div>
                          <RoomStatusBadge status={room.status} />
                        </div>
                      </CardHeader>
                      <CardContent className="pb-2">
                        <div className="grid grid-cols-2 gap-2 text-sm">
                          <div className="flex items-center">
                            <Users className="h-4 w-4 mr-1.5 text-muted-foreground" />
                            <span>Capacity: {room.capacity}</span>
                          </div>
                          
                          <div className="flex items-center">
                            <MapPin className="h-4 w-4 mr-1.5 text-muted-foreground" />
                            <span>{room.building}</span>
                          </div>
                          
                          {room.currentSchedule && room.currentSchedule.timeSlots.length > 0 && (
                            <div className="col-span-2 flex items-center mt-1 text-muted-foreground">
                              <Clock className="h-4 w-4 mr-1.5" />
                              <span>
                                {room.currentSchedule.timeSlots[0].class.name || 'Class in progress'}
                              </span>
                            </div>
                          )}
                        </div>
                        
                        <div className="mt-2 flex flex-wrap gap-1">
                          {room.features.slice(0, 4).map((feature) => (
                            <Badge key={feature} variant="secondary" className="text-xs">
                              {capitalizeFirstLetter(feature)}
                            </Badge>
                          ))}
                          {room.features.length > 4 && (
                            <Badge variant="secondary" className="text-xs">
                              +{room.features.length - 4}
                            </Badge>
                          )}
                        </div>
                      </CardContent>
                      <CardFooter className="flex justify-between pt-2">
                        <Button 
                          variant="outline" 
                          size="sm" 
                          onClick={() => handleViewRoom(room.id)}
                        >
                          <Eye className="h-3.5 w-3.5 mr-1.5" />
                          Details
                        </Button>
                        
                        {canManageRooms && (
                          <Button 
                            variant="outline" 
                            size="sm" 
                            onClick={() => handleEditRoom(room.id)}
                          >
                            <Edit className="h-3.5 w-3.5 mr-1.5" />
                            Edit
                          </Button>
                        )}
                      </CardFooter>
                    </Card>
                  ))}
                </div>
              )}
            </CardContent>
            
            <CardFooter>
              <div className="w-full flex justify-between items-center">
                <div className="text-sm text-muted-foreground">
                  Showing {pagination.page} of {pagination.pages} pages
                  ({pagination.total} rooms total)
                </div>
                
                <Pagination 
                  currentPage={pagination.page}
                  totalPages={pagination.pages}
                  onPageChange={handlePageChange}
                />
              </div>
            </CardFooter>
          </Card>
        </TabsContent>
        
        <TabsContent value="availability" className="space-y-4">
          <AvailabilitySearch />
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default RoomDirectory;
