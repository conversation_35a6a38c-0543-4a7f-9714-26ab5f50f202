
import { MakeupClass, RoomAvailability, ScheduleWithMakeup } from "@/types/class";

// Function to schedule a makeup class
export const scheduleMakeupClass = async (
  classId: string,
  makeupData: {
    originalDate: string;
    makeupDate: string;
    teacherId: string;
    roomId: string;
    timeStart: string;
    timeEnd: string;
    reason: string;
    notes?: string;
    notifyStudents?: boolean;
  }
): Promise<{ success: boolean; message: string; data: { makeupClasses: MakeupClass[] } }> => {
  try {
    console.log(`Scheduling makeup class for class ${classId}:`, makeupData);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 700));
    
    // Mock response for development
    return {
      success: true,
      message: "Makeup class scheduled successfully",
      data: {
        makeupClasses: [
          {
            id: `makeup-${Date.now()}`,
            originalDate: makeupData.originalDate,
            makeupDate: makeupData.makeupDate,
            teacherId: makeupData.teacherId,
            teacherName: "<PERSON>", // This would come from the API in a real app
            roomId: makeupData.roomId,
            room: "Room A101", // This would come from the API in a real app
            timeStart: makeupData.timeStart,
            timeEnd: makeupData.timeEnd,
            reason: makeupData.reason,
            notes: makeupData.notes,
            status: "scheduled",
            notifyStudents: makeupData.notifyStudents,
            approvedBy: {
              id: "admin1",
              username: "admin"
            }
          }
        ]
      }
    };
  } catch (error) {
    console.error("Error scheduling makeup class:", error);
    throw error;
  }
};

// Function to get available rooms for a makeup class
export const getAvailableRooms = async (
  date: string,
  timeStart: string,
  timeEnd: string
): Promise<RoomAvailability[]> => {
  try {
    console.log(`Checking room availability for date: ${date}, time: ${timeStart}-${timeEnd}`);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // Mock data for available rooms
    const rooms: RoomAvailability[] = [
      { id: "room1", name: "Room A101", available: true },
      { id: "room2", name: "Lab B202", available: true },
      { id: "room3", name: "Room C103", available: false, conflicts: ["Mathematics 101"] },
      { id: "room4", name: "Lab A105", available: true },
      { id: "room5", name: "Room D104", available: false, conflicts: ["Physics 101"] },
      { id: "room6", name: "Lab C205", available: true }
    ];
    
    return rooms;
  } catch (error) {
    console.error("Error fetching available rooms:", error);
    throw error;
  }
};

// Function to get class schedule including makeup classes
export const getClassScheduleWithMakeup = async (
  classId: string
): Promise<ScheduleWithMakeup> => {
  try {
    console.log(`Fetching schedule with makeup classes for class ${classId}`);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // Mock data for class schedule with makeup classes
    const schedule: ScheduleWithMakeup = {
      regularSchedule: [
        {
          day: "monday",
          times: [
            {
              start: "09:00",
              end: "11:00",
              teacher: "John Smith"
            }
          ]
        },
        {
          day: "wednesday",
          times: [
            {
              start: "13:00",
              end: "15:00",
              teacher: "John Smith"
            }
          ]
        }
      ],
      makeupClasses: [
        {
          id: "makeup1",
          originalDate: "2023-02-10",
          makeupDate: "2023-02-17",
          teacherId: "teacher1",
          teacherName: "John Smith",
          roomId: "room1",
          room: "Room A101",
          timeStart: "09:00",
          timeEnd: "11:00",
          reason: "Teacher absence",
          status: "completed"
        },
        {
          id: "makeup2",
          originalDate: "2023-03-15",
          makeupDate: "2023-03-22",
          teacherId: "teacher1",
          teacherName: "John Smith",
          roomId: "room1",
          room: "Room A101",
          timeStart: "13:00",
          timeEnd: "15:00",
          reason: "Public holiday",
          status: "scheduled"
        }
      ]
    };
    
    return schedule;
  } catch (error) {
    console.error("Error fetching class schedule with makeup:", error);
    throw error;
  }
};

// Function to update makeup class status
export const updateMakeupClassStatus = async (
  classId: string,
  makeupId: string,
  status: 'scheduled' | 'completed' | 'cancelled',
  notes?: string
): Promise<{ success: boolean; message: string }> => {
  try {
    console.log(`Updating makeup class ${makeupId} status to ${status}`);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Mock response for development
    return {
      success: true,
      message: `Makeup class has been ${status}`
    };
  } catch (error) {
    console.error("Error updating makeup class status:", error);
    throw error;
  }
};
