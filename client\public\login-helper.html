<!DOCTYPE html>
<html>
<head>
    <title>Quick Login Helper</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; max-width: 500px; margin: 0 auto; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 20px; padding: 10px; background: #f8f9fa; border-radius: 4px; }
        .error { background: #f8d7da; color: #721c24; }
        .success { background: #d4edda; color: #155724; }
    </style>
</head>
<body>
    <h1>Vertex Login Helper</h1>
    <p>Use this to quickly log in and get a valid JWT token for the reports.</p>
    
    <form id="loginForm">
        <div class="form-group">
            <label for="username">Username:</label>
            <input type="text" id="username" value="admin@local" required>
        </div>
        <div class="form-group">
            <label for="password">Password:</label>
            <input type="password" id="password" value="admin" required>
        </div>
        <button type="submit">Login & Set Token</button>
    </form>
    
    <div id="result" class="result" style="display: none;"></div>
    
    <div style="margin-top: 30px;">
        <h3>Default Credentials:</h3>
        <ul>
            <li><strong>Admin:</strong> admin@local / admin</li>
            <li><strong>Manager:</strong> manager@local / manager</li>
            <li><strong>Secretary:</strong> secretary@local / secretary</li>
            <li><strong>Teacher:</strong> teacher@local / teacher</li>
        </ul>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');
            
            try {
                resultDiv.style.display = 'block';
                resultDiv.className = 'result';
                resultDiv.innerHTML = 'Logging in...';
                
                const response = await fetch('http://localhost:3000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ username, password }),
                });
                
                if (!response.ok) {
                    throw new Error(`Login failed: ${response.status}`);
                }
                
                const data = await response.json();
                
                if (data.success && data.token) {
                    // Store the token in localStorage
                    localStorage.setItem('jwt_token', data.token);
                    
                    // Also store user data
                    const userData = {
                        id: data.user.id || data.user._id,
                        name: data.user.name || data.user.username,
                        email: data.user.email || data.user.username,
                        role: data.user.role,
                        token: data.token
                    };
                    
                    const encryptedUserData = btoa(JSON.stringify({
                        ...userData,
                        exp: Math.floor(Date.now() / 1000) + 60 * 60 * 24 * 7 // 7 days
                    }));
                    
                    localStorage.setItem('auth_token', encryptedUserData);
                    
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <strong>✅ Login Successful!</strong><br>
                        User: ${data.user.username} (${data.user.role})<br>
                        Token stored in localStorage.<br><br>
                        <strong>Next steps:</strong><br>
                        1. Go to <a href="http://localhost:5173" target="_blank">http://localhost:5173</a><br>
                        2. You should be automatically redirected to the dashboard<br>
                        3. Navigate to Reports to test the functionality
                    `;
                } else {
                    throw new Error('Invalid response from server');
                }
                
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `<strong>❌ Login Failed:</strong><br>${error.message}`;
            }
        });
    </script>
</body>
</html>
