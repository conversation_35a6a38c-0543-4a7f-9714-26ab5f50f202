// server/src/controllers/secretary.report.controller.ts
import { Request, Response, NextFunction } from 'express';
import { SecretaryReportService } from '../services/secretary.report.service';
import { SecretaryReportRequestDTO } from '../types/secretary.report.types';
import { AppError } from '../types/error.types';
import { SystemLogger } from '../services/logger.service';

export class Secretary<PERSON><PERSON>ortController {
    /**
     * Generate and download a report
     */
    static async generateReport(req: Request, res: Response, next: NextFunction) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        try {
            // Parse and validate request
            const reportRequest: SecretaryReportRequestDTO = {
                ...req.body,
                dateRange: {
                    startDate: new Date(req.body.dateRange.startDate),
                    endDate: new Date(req.body.dateRange.endDate)
                }
            };

            // Generate report
            const { data, contentType, filename } = await SecretaryReportService.generateReport(
                reportRequest,
                currentUser._id.toString()
            );

            // Log successful generation
            await SystemLogger.log({
                severity: 'info',
                category: 'reports',
                action: 'generate_secretary_report',
                performedBy: currentUser._id.toString(),
                details: {
                    reportType: reportRequest.type,
                    format: reportRequest.format,
                    filters: reportRequest.filters
                },
                status: 'success',
                timestamp: new Date()
            });

            // Set response headers
            res.setHeader('Content-Type', contentType);
            res.setHeader('Content-Disposition', `attachment; filename=${filename}`);
            
            // Send response
            res.send(data);

        } catch (error) {
            // Log error
            await SystemLogger.log({
                severity: 'error',
                category: 'reports',
                action: 'generate_secretary_report',
                performedBy: currentUser._id.toString(),
                details: {
                    error: error instanceof Error ? error.message : 'Unknown error',
                    reportRequest: req.body
                },
                status: 'failed',
                timestamp: new Date()
            });

            next(error);
        }
    }

    /**
     * Preview report data without generating file
     */
    static async previewReport(req: Request, res: Response, next: NextFunction) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        try {
            // Parse request (force JSON format for preview)
            const reportRequest: SecretaryReportRequestDTO = {
                ...req.body,
                format: 'json',
                dateRange: {
                    startDate: new Date(req.body.dateRange.startDate),
                    endDate: new Date(req.body.dateRange.endDate)
                }
            };

            // Generate preview data
            const { data } = await SecretaryReportService.generateReport(
                reportRequest,
                currentUser._id.toString()
            );

            // Send response
            res.json({
                success: true,
                data: JSON.parse(data as string)
            });

        } catch (error) {
            // Log error
            await SystemLogger.log({
                severity: 'error',
                category: 'reports',
                action: 'preview_secretary_report',
                performedBy: currentUser._id.toString(),
                details: {
                    error: error instanceof Error ? error.message : 'Unknown error',
                    reportRequest: req.body
                },
                status: 'failed',
                timestamp: new Date()
            });

            next(error);
        }
    }

    /**
     * Get available report options and metadata
     */
    static async getReportOptions(req: Request, res: Response) {
        const options = {
            types: [
                {
                    id: 'student_registration',
                    name: 'Student Registration Report',
                    description: 'Overview of student registrations and trends',
                    availableFormats: ['pdf', 'excel', 'csv', 'json'],
                    requiredDateRange: true,
                    availableFilters: ['registrationStatus']
                },
                {
                    id: 'payment_collection',
                    name: 'Payment Collection Report',
                    description: 'Summary of payment collections and status',
                    availableFormats: ['pdf', 'excel', 'csv', 'json'],
                    requiredDateRange: true,
                    availableFilters: ['paymentStatus']
                },
                {
                    id: 'class_capacity',
                    name: 'Class Capacity Report',
                    description: 'Analysis of class utilization and capacity',
                    availableFormats: ['pdf', 'excel', 'csv', 'json'],
                    requiredDateRange: false,
                    availableFilters: ['minCapacity', 'maxCapacity', 'building', 'floor']
                },
                {
                    id: 'student_attendance',
                    name: 'Student Attendance Report',
                    description: 'Overview of student attendance patterns',
                    availableFormats: ['pdf', 'excel', 'csv', 'json'],
                    requiredDateRange: true,
                    availableFilters: ['classId', 'attendanceRate']
                },
                {
                    id: 'payment_due',
                    name: 'Payment Due Report',
                    description: 'List of upcoming and overdue payments',
                    availableFormats: ['pdf', 'excel', 'csv', 'json'],
                    requiredDateRange: true,
                    availableFilters: ['paymentStatus']
                }
            ],
            formats: [
                {
                    id: 'pdf',
                    name: 'PDF Document',
                    mimeType: 'application/pdf'
                },
                {
                    id: 'excel',
                    name: 'Excel Spreadsheet',
                    mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                },
                {
                    id: 'csv',
                    name: 'CSV File',
                    mimeType: 'text/csv'
                },
                {
                    id: 'json',
                    name: 'JSON Data',
                    mimeType: 'application/json'
                }
            ],
            filterOptions: {
                registrationStatus: ['active', 'inactive'],
                paymentStatus: ['paid', 'pending', 'overdue'],
                groupBy: ['class', 'date', 'status', 'building'],
                sortBy: ['date', 'amount', 'name', 'dueDate'],
                sortOrder: ['asc', 'desc']
            }
        };

        res.json({
            success: true,
            data: options
        });
    }

    /**
     * Validate report request without generating
     */
    static async validateReportRequest(req: Request, res: Response) {
        try {
            const reportRequest: SecretaryReportRequestDTO = {
                ...req.body,
                dateRange: {
                    startDate: new Date(req.body.dateRange.startDate),
                    endDate: new Date(req.body.dateRange.endDate)
                }
            };

            // Perform validation checks
            const validationResults = {
                isValid: true,
                errors: [] as string[],
                warnings: [] as string[]
            };

            // Date range validation
            if (reportRequest.dateRange.endDate < reportRequest.dateRange.startDate) {
                validationResults.isValid = false;
                validationResults.errors.push('End date must be after start date');
            }

            // Date range too large
            const daysDifference = Math.ceil(
                (reportRequest.dateRange.endDate.getTime() - reportRequest.dateRange.startDate.getTime()) 
                / (1000 * 60 * 60 * 24)
            );
            if (daysDifference > 365) {
                validationResults.warnings.push('Date range exceeds one year');
            }

            // Format-specific validation
            if (reportRequest.format === 'pdf' && reportRequest.type === 'payment_collection') {
                validationResults.warnings.push('PDF format may not be optimal for detailed payment data');
            }

            res.json({
                success: true,
                data: validationResults
            });

        } catch (error) {
            res.status(400).json({
                success: false,
                error: error instanceof Error ? error.message : 'Invalid report request'
            });
        }
    }
}