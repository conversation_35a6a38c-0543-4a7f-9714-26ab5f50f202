
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { PaymentFilter, PaymentMethod, PaymentStatus, PaymentPeriod } from "@/types/payment";
import { CalendarIcon, X, Filter, Search } from "lucide-react";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface PaymentFiltersProps {
  filters: Partial<PaymentFilter>;
  onFilterChange: (filters: Partial<PaymentFilter>) => void;
  onResetFilters: () => void;
}

const PaymentFilters = ({
  filters,
  onFilterChange,
  onResetFilters,
}: PaymentFiltersProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [search, setSearch] = useState(filters.search || "");

  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onFilterChange({ ...filters, search, page: 1 });
  };

  const handleStatusChange = (value: string) => {
    if (!value) {
      const { status, ...rest } = filters;
      onFilterChange({ ...rest, page: 1 });
    } else {
      onFilterChange({ ...filters, status: value as PaymentStatus, page: 1 });
    }
  };

  const handleMethodChange = (value: string) => {
    if (!value) {
      const { method, ...rest } = filters;
      onFilterChange({ ...rest, page: 1 });
    } else {
      onFilterChange({ ...filters, method: value as PaymentMethod, page: 1 });
    }
  };

  const handlePeriodChange = (value: string) => {
    if (!value) {
      const { period, ...rest } = filters;
      onFilterChange({ ...rest, page: 1 });
    } else {
      onFilterChange({ ...filters, period: value as PaymentPeriod, page: 1 });
    }
  };

  const handleDateChange = (field: 'startDate' | 'endDate', value: string | undefined) => {
    onFilterChange({ ...filters, [field]: value, page: 1 });
  };

  const handleAmountChange = (field: 'minAmount' | 'maxAmount', value: string) => {
    if (!value) {
      const newFilters = { ...filters };
      delete newFilters[field];
      onFilterChange({ ...newFilters, page: 1 });
    } else {
      onFilterChange({ 
        ...filters, 
        [field]: Number(value), 
        page: 1 
      });
    }
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.status) count++;
    if (filters.method) count++;
    if (filters.period) count++;
    if (filters.startDate) count++;
    if (filters.endDate) count++;
    if (filters.minAmount) count++;
    if (filters.maxAmount) count++;
    return count;
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row gap-2 items-start sm:items-center">
        <form className="relative w-full sm:w-auto flex-1" onSubmit={handleSearchSubmit}>
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search by receipt, description, or student..."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="pl-8 w-full"
          />
          {search && (
            <Button
              variant="ghost"
              size="sm"
              type="button"
              className="absolute right-0 top-0 h-full px-3"
              onClick={() => {
                setSearch("");
                if (filters.search) {
                  const { search, ...rest } = filters;
                  onFilterChange({ ...rest });
                }
              }}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </form>
        
        <Button
          variant="outline"
          type="button"
          onClick={() => setIsExpanded(!isExpanded)}
          className="whitespace-nowrap"
        >
          <Filter className="mr-2 h-4 w-4" />
          Filters
          {getActiveFilterCount() > 0 && (
            <Badge
              variant="secondary"
              className="ml-2 rounded-full px-1.5 py-0.5"
            >
              {getActiveFilterCount()}
            </Badge>
          )}
        </Button>
        
        {getActiveFilterCount() > 0 && (
          <Button
            variant="ghost"
            size="sm"
            type="button"
            onClick={onResetFilters}
            className="whitespace-nowrap"
          >
            <X className="mr-2 h-4 w-4" />
            Clear all
          </Button>
        )}
      </div>

      {isExpanded && (
        <div className="grid sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 p-4 border rounded-md bg-background">
          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <Select
              value={filters.status || ""}
              onValueChange={handleStatusChange}
            >
              <SelectTrigger id="status">
                <SelectValue placeholder="All statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All statuses</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="voided">Voided</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="method">Payment Method</Label>
            <Select
              value={filters.method || ""}
              onValueChange={handleMethodChange}
            >
              <SelectTrigger id="method">
                <SelectValue placeholder="All methods" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All methods</SelectItem>
                <SelectItem value="cash">Cash</SelectItem>
                <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="period">Payment Period</Label>
            <Select
              value={filters.period || ""}
              onValueChange={handlePeriodChange}
            >
              <SelectTrigger id="period">
                <SelectValue placeholder="All periods" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">All periods</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
                <SelectItem value="quarterly">Quarterly</SelectItem>
                <SelectItem value="semi_annual">Semi-annual</SelectItem>
                <SelectItem value="annual">Annual</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Payment Date Range</Label>
            <div className="flex space-x-2">
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-[130px] pl-3 text-left font-normal"
                  >
                    {filters.startDate ? (
                      format(new Date(filters.startDate), "MMM d, yyyy")
                    ) : (
                      <span className="text-muted-foreground">From</span>
                    )}
                    <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={filters.startDate ? new Date(filters.startDate) : undefined}
                    onSelect={(date) => handleDateChange("startDate", date?.toISOString().split("T")[0])}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-[130px] pl-3 text-left font-normal"
                  >
                    {filters.endDate ? (
                      format(new Date(filters.endDate), "MMM d, yyyy")
                    ) : (
                      <span className="text-muted-foreground">To</span>
                    )}
                    <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={filters.endDate ? new Date(filters.endDate) : undefined}
                    onSelect={(date) => handleDateChange("endDate", date?.toISOString().split("T")[0])}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="minAmount">Min Amount</Label>
            <Input
              id="minAmount"
              type="number"
              placeholder="0.00"
              min="0"
              step="0.01"
              value={filters.minAmount || ""}
              onChange={(e) => handleAmountChange("minAmount", e.target.value)}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="maxAmount">Max Amount</Label>
            <Input
              id="maxAmount"
              type="number"
              placeholder="9999.99"
              min="0"
              step="0.01"
              value={filters.maxAmount || ""}
              onChange={(e) => handleAmountChange("maxAmount", e.target.value)}
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default PaymentFilters;
