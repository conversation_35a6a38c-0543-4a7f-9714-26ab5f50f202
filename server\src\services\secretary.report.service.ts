// server/src/services/secretary.report.service.ts
import mongoose from 'mongoose';
import ExcelJS from 'exceljs';
import PDFDocument from 'pdfkit';
import Papa from 'papaparse';
import { AppError } from '../types/error.types';
import { Student } from '../models/student.model';
import { Class } from '../models/class.model';
import { Payment } from '../models/payment.model';
import { Attendance } from '../models/attendance.model';
import { SystemLogger } from './logger.service';
import {
    SecretaryReportType,
    SecretaryReportRequestDTO,
    RegistrationReportData,
    PaymentCollectionReportData,
    ClassCapacityReportData,
    AttendanceReportData,
    PaymentDueReportData,
    ReportDateRange
} from '../types/secretary.report.types';

export class SecretaryReportService {
    /**
     * Main report generation method
     */
    static async generateReport(
        request: SecretaryReportRequestDTO,
        generatedBy: string
    ): Promise<{ data: Buffer | string; contentType: string; filename: string }> {
        try {
            // Generate report data based on type
            const reportData = await this.generateReportData(request);

            // Format the report
            const { data, contentType, filename } = await this.formatReport(
                reportData,
                request.type,
                request.format
            );

            // Log report generation
            await SystemLogger.log({
                severity: 'info',
                category: 'reports',
                action: 'generate_secretary_report',
                performedBy: generatedBy,
                details: {
                    reportType: request.type,
                    format: request.format,
                    filters: request.filters
                },
                status: 'success',
                timestamp: new Date()
            });

            return { data, contentType, filename };
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error generating report');
        }
    }

    /**
     * Report data generation methods
     */
    private static async generateReportData(request: SecretaryReportRequestDTO): Promise<any> {
        switch (request.type) {
            case 'student_registration':
                return this.generateRegistrationReport(request);
            case 'payment_collection':
                return this.generatePaymentCollectionReport(request);
            case 'class_capacity':
                return this.generateClassCapacityReport(request);
            case 'student_attendance':
                return this.generateAttendanceReport(request);
            case 'payment_due':
                return this.generatePaymentDueReport(request);
            default:
                throw new AppError(400, 'Unsupported report type');
        }
    }

    private static async generateRegistrationReport(
        request: SecretaryReportRequestDTO
    ): Promise<RegistrationReportData> {
        const { dateRange, filters } = request;

        // Base query
        const query: mongoose.FilterQuery<typeof Student> = {
            registeredAt: {
                $gte: dateRange.startDate,
                $lte: dateRange.endDate
            }
        };

        if (filters?.registrationStatus) {
            query.status = filters.registrationStatus;
        }

        // Get registrations
        const registrations = await Student.find(query)
            .populate('registeredBy', 'username')
            .populate('currentClass', 'name')
            .sort({ registeredAt: request.sortOrder || 'desc' });

        // Group by date
        const registrationsByDate = registrations.reduce((acc: any[], student) => {
            const date = student.registeredAt.toISOString().split('T')[0];
            const dateGroup = acc.find(group => group.date === date);

            const studentDetail = {
                studentName: student.name,
                className: (student.currentClass as any)?.name || 'Unassigned',
                registeredBy: (student.registeredBy as any)?.username || 'Unknown'
            };

            if (dateGroup) {
                dateGroup.count++;
                dateGroup.details.push(studentDetail);
            } else {
                acc.push({
                    date: new Date(date),
                    count: 1,
                    details: [studentDetail]
                });
            }

            return acc;
        }, []);

        // Calculate summary
        const [activeCount, inactiveCount] = await Promise.all([
            Student.countDocuments({ status: 'active' }),
            Student.countDocuments({ status: 'inactive' })
        ]);

        // Calculate registration trend
        const trend = this.calculateRegistrationTrend(registrationsByDate);

        return {
            period: dateRange,
            totalRegistrations: registrations.length,
            registrationsByDate,
            summary: {
                activeStudents: activeCount,
                inactiveStudents: inactiveCount,
                registrationTrend: trend
            }
        };
    }

    private static async generatePaymentCollectionReport(
        request: SecretaryReportRequestDTO
    ): Promise<PaymentCollectionReportData> {
        const { dateRange, filters } = request;

        // Base query
        const query: mongoose.FilterQuery<typeof Payment> = {
            date: {
                $gte: dateRange.startDate,
                $lte: dateRange.endDate
            },
            status: 'completed' // Only include completed payments
        };

        // Get payments
        const payments = await Payment.find(query)
            .populate('studentId', 'name')
            .populate('recordedBy', 'username')
            .sort({ date: request.sortOrder || 'desc' });

        // Group by date
        const paymentsByDate = payments.reduce((acc: any[], payment) => {
            const date = payment.date.toISOString().split('T')[0];
            const dateGroup = acc.find(group => group.date === date);

            const paymentDetail = {
                studentName: (payment.studentId as any)?.name || 'Unknown',
                amount: payment.amount,
                method: payment.method,
                recordedBy: (payment.recordedBy as any)?.username || 'Unknown'
            };

            if (dateGroup) {
                dateGroup.amount += payment.amount;
                dateGroup.payments.push(paymentDetail);
            } else {
                acc.push({
                    date: new Date(date),
                    amount: payment.amount,
                    payments: [paymentDetail]
                });
            }

            return acc;
        }, []);

        // Calculate summary
        const [pendingAmount, overdueAmount] = await Promise.all([
            this.calculatePendingPayments(),
            this.calculateOverduePayments()
        ]);

        const totalCollected = payments.reduce((sum, payment) => sum + payment.amount, 0);
        const totalExpected = totalCollected + pendingAmount + overdueAmount;
        const collectionRate = totalExpected > 0 ? (totalCollected / totalExpected) * 100 : 100;

        return {
            period: dateRange,
            totalCollected,
            paymentsByDate,
            summary: {
                totalPending: pendingAmount,
                totalOverdue: overdueAmount,
                collectionRate
            }
        };
    }

    private static async generateClassCapacityReport(
        request: SecretaryReportRequestDTO
    ): Promise<ClassCapacityReportData> {
        const { filters } = request;

        // Base query
        const query: mongoose.FilterQuery<typeof Class> = {
            status: 'active'
        };

        if (filters?.building) {
            query.building = filters.building;
        }
        if (filters?.floor) {
            query.floor = filters.floor;
        }
        if (filters?.minCapacity) {
            query.capacity = { $gte: filters.minCapacity };
        }
        if (filters?.maxCapacity) {
            query.capacity = { ...query.capacity, $lte: filters.maxCapacity };
        }

        // Get classes
        const classes = await Class.find(query).sort({ name: 1 });

        // Calculate statistics for each class
        const classStats = classes.map(cls => {
            const room = cls.room || 'Unassigned';
            // Extract building and floor from room if needed
            const [building = 'Unknown'] = room.split('-');
            
            return {
                className: cls.name,
                room,
                building,
                capacity: cls.capacity,
                currentCount: cls.currentStudentCount,
                utilizationRate: (cls.currentStudentCount / cls.capacity) * 100,
                waitingList: 0 // Implement waiting list logic if needed
            };
        });

        // Calculate summary
        const totalCapacity = classes.reduce((sum, cls) => sum + cls.capacity, 0);
        const totalEnrolled = classes.reduce((sum, cls) => sum + cls.currentStudentCount, 0);
        const averageUtilization = totalCapacity > 0 ? (totalEnrolled / totalCapacity) * 100 : 0;
        const fullClasses = classes.filter(cls => cls.currentStudentCount >= cls.capacity).length;

        return {
            period: request.dateRange,
            classes: classStats,
            summary: {
                totalCapacity,
                totalEnrolled,
                averageUtilization,
                fullClasses
            }
        };
    }

    private static async generateAttendanceReport(
        request: SecretaryReportRequestDTO
    ): Promise<AttendanceReportData> {
        const { dateRange, filters } = request;

        // Get all active classes or specific class if filtered
        const classQuery: mongoose.FilterQuery<typeof Class> = { status: 'active' };
        if (filters?.classId) {
            classQuery._id = new mongoose.Types.ObjectId(filters.classId);
        }

        const classes = await Class.find(classQuery);

        // Get attendance records for each class
        const attendanceByClass = await Promise.all(
            classes.map(async (cls) => {
                const attendanceRecords = await Attendance.find({
                    classId: cls._id,
                    date: {
                        $gte: dateRange.startDate,
                        $lte: dateRange.endDate
                    }
                });

                const records = attendanceRecords.map(record => {
                    const stats = record.students.reduce(
                        (acc: any, student) => {
                            acc[student.status]++;
                            return acc;
                        },
                        { present: 0, absent: 0, late: 0, excused: 0 }
                    );

                    const totalStudents = record.students.length;
                    const rate = totalStudents > 0 ?
                        ((stats.present + stats.late) / totalStudents) * 100 : 0;

                    return {
                        date: record.date,
                        ...stats,
                        rate
                    };
                });

                return {
                    className: cls.name,
                    totalStudents: cls.currentStudentCount,
                    records
                };
            })
        );

        // Calculate summary statistics
        let totalRate = 0;
        let lowestRate = 100;
        let highestRate = 0;
        let lowestAttendanceClass = '';
        let highestAttendanceClass = '';

        attendanceByClass.forEach(classData => {
            const classAvgRate = classData.records.reduce(
                (sum, record) => sum + record.rate,
                0
            ) / (classData.records.length || 1);

            totalRate += classAvgRate;

            if (classAvgRate < lowestRate) {
                lowestRate = classAvgRate;
                lowestAttendanceClass = classData.className;
            }
            if (classAvgRate > highestRate) {
                highestRate = classAvgRate;
                highestAttendanceClass = classData.className;
            }
        });

        return {
            period: dateRange,
            attendanceByClass,
            summary: {
                averageAttendanceRate: attendanceByClass.length > 0 ?
                    totalRate / attendanceByClass.length : 0,
                lowestAttendanceClass,
                highestAttendanceClass
            }
        };
    }

    private static async generatePaymentDueReport(
        request: SecretaryReportRequestDTO
    ): Promise<PaymentDueReportData> {
        const { dateRange, filters } = request;

        // Get all students with due payments
        const students = await Student.find({
            status: 'active',
            'payments.nextDueDate': {
                $gte: dateRange.startDate,
                $lte: dateRange.endDate
            }
        })
        .populate('currentClass', 'name')
        .populate({
            path: 'payments',
            match: {
                nextDueDate: {
                    $gte: dateRange.startDate,
                    $lte: dateRange.endDate
                }
            },
            options: { sort: { nextDueDate: 1 } }
        });

        const duePayments = students.map(student => {
            const latestPayment = student.payments[student.payments.length - 1];
            const status = this.determinePaymentStatus(latestPayment);

            if (filters?.paymentStatus && status !== filters.paymentStatus) {
                return null;
            }

            return {
                studentName: student.name,
                className: (student.currentClass as any)?.name || 'Unassigned',
                amount: latestPayment.remainingBalance,
                dueDate: latestPayment.nextDueDate,
                status,
                contactInfo: {
                    phone: student.contactInfo.phone,
                    email: student.contactInfo.email
                },
                lastPayment: latestPayment ? {
                    date: latestPayment.date,
                    amount: latestPayment.amount
                } : undefined
            };
        }).filter(Boolean) as any[];

        // Calculate summary
        const totalDueAmount = duePayments.reduce((sum, payment) => sum + payment.amount, 0);
        const overdueCount = duePayments.filter(payment => payment.status === 'overdue').length;
        const upcomingDueCount = duePayments.filter(payment => payment.status === 'pending').length;

        return {
            period: dateRange,
            duePayments,
            summary: {
                totalDueAmount,
                overdueCount,
                upcomingDueCount
            }
        };
    }

    /**
     * Helper methods
     */
    private static calculateRegistrationTrend(
        registrationsByDate: Array<{ date: Date; count: number }>
    ): 'increasing' | 'decreasing' | 'stable' {
        if (registrationsByDate.length < 2) return 'stable';

        const firstHalf = registrationsByDate.slice(0, Math.floor(registrationsByDate.length / 2));
        const secondHalf = registrationsByDate.slice(Math.floor(registrationsByDate.length / 2));

        const firstHalfAvg = firstHalf.reduce((sum, day) => sum + day.count, 0) / firstHalf.length;
        const secondHalfAvg = secondHalf.reduce((sum, day) => sum + day.count, 0) / secondHalf.length;

        const difference = secondHalfAvg - firstHalfAvg;
        if (difference > firstHalfAvg * 0.1) return 'increasing';
        if (difference < -firstHalfAvg * 0.1) return 'decreasing';
        return 'stable';
    }

    private static async calculatePendingPayments(): Promise<number> {
        const pendingPayments = await Payment.find({
            'status': 'pending',
            'nextDueDate': { $gt: new Date() }
        });
        return pendingPayments.reduce((sum, payment) => sum + payment.remainingBalance, 0);
    }

    private static async calculateOverduePayments(): Promise<number> {
        const overduePayments = await Payment.find({
            'status': 'pending',
            'nextDueDate': { $lte: new Date() }
        });
        return overduePayments.reduce((sum, payment) => sum + payment.remainingBalance, 0);
    }

    private static determinePaymentStatus(payment: any): 'paid' | 'pending' | 'overdue' {
        if (!payment) return 'pending';
        if (payment.remainingBalance <= 0) return 'paid';
        return payment.nextDueDate < new Date() ? 'overdue' : 'pending';
    }

    /**
     * Report formatting methods
     */
    private static async formatReport(
        data: any,
        type: SecretaryReportType,
        format: string
    ): Promise<{ data: Buffer | string; contentType: string; filename: string }> {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `${type}_report_${timestamp}`;

        switch (format) {
            case 'json':
                return {
                    data: JSON.stringify(data, null, 2),
                    contentType: 'application/json',
                    filename: `${filename}.json`
                };
            case 'csv':
                return {
                    data: this.convertToCSV(data),
                    contentType: 'text/csv',
                    filename: `${filename}.csv`
                };
            case 'excel':
                return {
                    data: await this.createExcel(data, type),
                    contentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    filename: `${filename}.xlsx`
                };
            case 'pdf':
                return {
                    data: await this.createPDF(data, type),
                    contentType: 'application/pdf',
                    filename: `${filename}.pdf`
                };
            default:
                throw new AppError(400, 'Unsupported format');
        }
    }

    private static convertToCSV(data: any): string {
        const flattenedData = this.flattenObject(data);
        return Papa.unparse(flattenedData);
    }

    private static async createExcel(data: any, type: SecretaryReportType): Promise<Buffer> {
        const workbook = new ExcelJS.Workbook();
        const worksheet = workbook.addWorksheet('Report');

        // Add title
        worksheet.addRow([this.formatTitle(type)]).font = { bold: true, size: 14 };
        worksheet.addRow([`Generated on: ${new Date().toLocaleString()}`]);
        worksheet.addRow([]);

        // Add summary section
        if (data.summary) {
            worksheet.addRow(['Summary']).font = { bold: true };
            Object.entries(data.summary).forEach(([key, value]) => {
                worksheet.addRow([this.formatKey(key), value]);
            });
            worksheet.addRow([]);
        }

        // Add main data
        const mainData = this.getMainDataForType(data, type);
        if (Array.isArray(mainData)) {
            const headers = Object.keys(mainData[0]);
            worksheet.addRow(headers.map(this.formatKey)).font = { bold: true };
            mainData.forEach(row => {
                worksheet.addRow(headers.map(header => row[header]));
            });
        }

        return workbook.xlsx.writeBuffer() as Promise<Buffer>;
    }

    private static async createPDF(data: any, type: SecretaryReportType): Promise<Buffer> {
        return new Promise((resolve, reject) => {
            const chunks: Buffer[] = [];
            const doc = new PDFDocument({ margin: 50 });

            doc.on('data', chunks.push.bind(chunks));
            doc.on('end', () => resolve(Buffer.concat(chunks)));
            doc.on('error', reject);

            // Add title
            doc.fontSize(16)
               .font('Helvetica-Bold')
               .text(this.formatTitle(type), { align: 'center' })
               .moveDown();

            // Add date
            doc.fontSize(10)
               .font('Helvetica')
               .text(`Generated on: ${new Date().toLocaleString()}`)
               .moveDown();

            // Add summary
            if (data.summary) {
                doc.fontSize(12)
                   .font('Helvetica-Bold')
                   .text('Summary')
                   .moveDown(0.5);

                Object.entries(data.summary).forEach(([key, value]) => {
                    doc.fontSize(10)
                       .font('Helvetica')
                       .text(`${this.formatKey(key)}: ${value}`)
                       .moveDown(0.2);
                });
                doc.moveDown();
            }

            // Add main data
            const mainData = this.getMainDataForType(data, type);
            if (Array.isArray(mainData)) {
                this.addTableToPDF(doc, mainData);
            }

            doc.end();
        });
    }

    private static addTableToPDF(doc: PDFKit.PDFDocument, data: any[]): void {
        if (!data.length) return;

        const headers = Object.keys(data[0]);
        const headerRow = headers.map(this.formatKey);

        // Calculate column widths
        const pageWidth = doc.page.width - 100;
        const columnWidth = pageWidth / headers.length;

        // Add headers
        doc.fontSize(10)
           .font('Helvetica-Bold');
        
        let xPos = 50;
        headerRow.forEach(header => {
            doc.text(header, xPos, doc.y, { width: columnWidth, align: 'left' });
            xPos += columnWidth;
        });
        doc.moveDown();

        // Add data rows
        doc.font('Helvetica');
        data.forEach(row => {
            xPos = 50;
            const rowHeight = doc.currentLineHeight();

            // Check if we need a new page
            if (doc.y + rowHeight > doc.page.height - 50) {
                doc.addPage();
                doc.fontSize(10);
            }

            headers.forEach(header => {
                doc.text(String(row[header]), xPos, doc.y, { 
                    width: columnWidth,
                    align: 'left'
                });
                xPos += columnWidth;
            });
            doc.moveDown();
        });
    }

    private static getMainDataForType(data: any, type: SecretaryReportType): any[] {
        switch (type) {
            case 'student_registration':
                return data.registrationsByDate;
            case 'payment_collection':
                return data.paymentsByDate;
            case 'class_capacity':
                return data.classes;
            case 'student_attendance':
                return data.attendanceByClass;
            case 'payment_due':
                return data.duePayments;
            default:
                return [];
        }
    }

    private static formatTitle(type: SecretaryReportType): string {
        return type.split('_').map(word => 
            word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ') + ' Report';
    }

    private static formatKey(key: string): string {
        return key.split('_').map(word => 
            word.charAt(0).toUpperCase() + word.slice(1)
        ).join(' ');
    }

    private static flattenObject(obj: any, prefix = ''): any {
        return Object.keys(obj).reduce((acc: any, key: string) => {
            const value = obj[key];
            const newKey = prefix ? `${prefix}.${key}` : key;

            if (value && typeof value === 'object' && !Array.isArray(value) && !(value instanceof Date)) {
                Object.assign(acc, this.flattenObject(value, newKey));
            } else if (Array.isArray(value)) {
                acc[newKey] = value.map(item => 
                    typeof item === 'object' ? JSON.stringify(item) : item
                ).join('; ');
            } else {
                acc[newKey] = value instanceof Date ? value.toISOString() : value;
            }
            return acc;
        }, {});
    }
}