
import MainLayout from "@/components/layout/MainLayout";
import RoomCreateForm from "@/components/rooms/RoomCreateForm";
import { hasRole } from "@/lib/auth";
import { Navigate } from "react-router-dom";

const RoomCreate = () => {
  // Check if user has proper permissions
  const canCreateRoom = hasRole(['SuperAdmin', 'Manager']);

  if (!canCreateRoom) {
    return <Navigate to="/rooms" replace />;
  }

  return (
    <MainLayout>
      <RoomCreateForm />
    </MainLayout>
  );
};

export default RoomCreate;
