// server/src/controllers/schedule.controller.ts
import { Request, Response } from 'express';
import { ScheduleService } from '../services/schedule.service';
import { AppError } from '../types/error.types';
import { SystemLogger } from '../services/logger.service';

export class ScheduleController {
    static async getDailySchedule(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { date, teacherId, roomId, classLevel } = req.query;

        const schedule = await ScheduleService.getDailySchedule(
            new Date(date as string),
            {
                teacherId: teacherId as string,
                roomId: roomId as string,
                classLevel: classLevel as string
            },
            currentUser._id.toString()
        );

        res.json({
            success: true,
            data: schedule
        });
    }

    static async getWeeklySchedule(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { startDate, teacherId, roomId, classLevel } = req.query;

        const schedule = await ScheduleService.getWeeklySchedule(
            new Date(startDate as string),
            {
                teacherId: teacherId as string,
                roomId: roomId as string,
                classLevel: classLevel as string
            },
            currentUser._id.toString()
        );

        res.json({
            success: true,
            data: schedule
        });
    }

    static async getScheduleByRange(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { startDate, endDate, teacherId, roomId, classLevel } = req.query;

        const schedule = await ScheduleService.getScheduleByRange(
            new Date(startDate as string),
            new Date(endDate as string),
            {
                teacherId: teacherId as string,
                roomId: roomId as string,
                classLevel: classLevel as string
            },
            currentUser._id.toString()
        );

        res.json({
            success: true,
            data: schedule
        });
    }

    static async getTeacherSchedule(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { teacherId } = req.params;
        const { startDate, endDate } = req.query;

        const schedule = await ScheduleService.getTeacherSchedule(
            teacherId,
            {
                startDate: startDate ? new Date(startDate as string) : undefined,
                endDate: endDate ? new Date(endDate as string) : undefined
            },
            currentUser._id.toString()
        );

        res.json({
            success: true,
            data: schedule
        });
    }

    static async getRoomSchedule(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { roomId } = req.params;
        const { startDate, endDate } = req.query;

        const schedule = await ScheduleService.getRoomSchedule(
            roomId,
            {
                startDate: startDate ? new Date(startDate as string) : undefined,
                endDate: endDate ? new Date(endDate as string) : undefined
            },
            currentUser._id.toString()
        );

        res.json({
            success: true,
            data: schedule
        });
    }

    static async getClassSchedule(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { classId } = req.params;
        const { startDate, endDate, includeMakeup } = req.query;

        const schedule = await ScheduleService.getClassSchedule(
            classId,
            {
                startDate: startDate ? new Date(startDate as string) : undefined,
                endDate: endDate ? new Date(endDate as string) : undefined,
                includeMakeup: includeMakeup === 'true'
            },
            currentUser._id.toString()
        );

        res.json({
            success: true,
            data: schedule
        });
    }
}
