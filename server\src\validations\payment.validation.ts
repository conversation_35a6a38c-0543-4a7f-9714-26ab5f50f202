// server/src/validations/payment.validation.ts
import Joi from 'joi';
import { PaymentMethod, PaymentPeriod } from '../types/payment.types';

// Reusable sub-schemas
const amountSchema = Joi.number()
    .positive()
    .precision(2)
    .required()
    .messages({
        'number.base': 'Amount must be a number',
        'number.positive': 'Amount must be positive',
        'number.precision': 'Amount cannot have more than 2 decimal places',
        'any.required': 'Amount is required'
    });

const dateSchema = Joi.date()
    .iso()
    .max('now')
    .required()
    .messages({
        'date.base': 'Invalid date format',
        'date.format': 'Date must be in ISO format',
        'date.max': 'Payment date cannot be in the future',
        'any.required': 'Date is required'
    });

const methodSchema = Joi.string()
    .valid('cash', 'bank_transfer', 'other')
    .required()
    .messages({
        'any.only': 'Invalid payment method',
        'any.required': 'Payment method is required'
    });

const periodSchema = Joi.string()
    .valid('monthly', 'quarterly', 'semi_annual', 'annual')
    .messages({
        'any.only': 'Invalid payment period'
    });

export const paymentValidation = {
    // GET /api/payments query parameters
    getPaymentsQuery: Joi.object({
        page: Joi.number()
            .min(1)
            .messages({
                'number.min': 'Page number must be greater than 0'
            }),
        limit: Joi.number()
            .min(1)
            .max(100)
            .messages({
                'number.min': 'Limit must be greater than 0',
                'number.max': 'Limit cannot exceed 100'
            }),
        sortBy: Joi.string()
            .valid('date', 'amount', 'nextDueDate')
            .messages({
                'any.only': 'Invalid sort field'
            }),
        sortOrder: Joi.string()
            .valid('asc', 'desc')
            .messages({
                'any.only': 'Sort order must be either asc or desc'
            }),
        status: Joi.string()
            .valid('pending', 'completed', 'voided')
            .messages({
                'any.only': 'Invalid payment status'
            }),
        method: methodSchema.optional(),
        studentId: Joi.string()
            .pattern(/^[0-9a-fA-F]{24}$/)
            .messages({
                'string.pattern.base': 'Invalid student ID format'
            }),
        dateRange: Joi.object({
            start: Joi.date().iso().required(),
            end: Joi.date().iso().min(Joi.ref('start')).required()
        }).messages({
            'date.min': 'End date must be after start date'
        }),
        amountRange: Joi.object({
            min: Joi.number().min(0).required(),
            max: Joi.number().greater(Joi.ref('min')).required()
        }),
        dueDateRange: Joi.object({
            start: Joi.date().iso().required(),
            end: Joi.date().iso().min(Joi.ref('start')).required()
        }),
        period: periodSchema,
        search: Joi.string()
            .min(2)
            .max(50)
            .messages({
                'string.min': 'Search term must be at least 2 characters long',
                'string.max': 'Search term cannot exceed 50 characters'
            })
    }),

    // POST /api/payments
    createPayment: Joi.object({
        studentId: Joi.string()
            .pattern(/^[0-9a-fA-F]{24}$/)
            .required()
            .messages({
                'string.pattern.base': 'Invalid student ID format',
                'any.required': 'Student ID is required'
            }),
        amount: amountSchema,
        method: methodSchema,
        date: dateSchema,
        nextDueDate: Joi.date()
            .iso()
            .greater('now')
            .messages({
                'date.greater': 'Next due date must be in the future'
            }),
        description: Joi.string()
            .min(3)
            .max(500)
            .required()
            .messages({
                'string.min': 'Description must be at least 3 characters long',
                'string.max': 'Description cannot exceed 500 characters',
                'any.required': 'Description is required'
            }),
        period: periodSchema,
        notes: Joi.string()
            .max(1000)
            .messages({
                'string.max': 'Notes cannot exceed 1000 characters'
            }),
        attachments: Joi.array()
            .items(
                Joi.string()
                    .uri()
                    .messages({
                        'string.uri': 'Invalid attachment URL format'
                    })
            )
            .max(5)
            .messages({
                'array.max': 'Maximum 5 attachments allowed'
            })
    }),

    // PATCH /api/payments/:id
    updatePayment: Joi.object({
        amount: amountSchema.optional(),
        method: methodSchema.optional(),
        date: dateSchema.optional(),
        nextDueDate: Joi.date()
            .iso()
            .greater('now')
            .messages({
                'date.greater': 'Next due date must be in the future'
            }),
        description: Joi.string()
            .min(3)
            .max(500)
            .messages({
                'string.min': 'Description must be at least 3 characters long',
                'string.max': 'Description cannot exceed 500 characters'
            }),
        notes: Joi.string()
            .max(1000)
            .messages({
                'string.max': 'Notes cannot exceed 1000 characters'
            }),
        attachments: Joi.array()
            .items(
                Joi.string()
                    .uri()
                    .messages({
                        'string.uri': 'Invalid attachment URL format'
                    })
            )
            .max(5)
            .messages({
                'array.max': 'Maximum 5 attachments allowed'
            })
    }).min(1).messages({
        'object.min': 'At least one field must be provided for update'
    }),

    // POST /api/payments/:id/void
    voidPayment: Joi.object({
        reason: Joi.string()
            .min(10)
            .max(500)
            .required()
            .messages({
                'string.min': 'Void reason must be at least 10 characters long',
                'string.max': 'Void reason cannot exceed 500 characters',
                'any.required': 'Void reason is required'
            }),
        notes: Joi.string()
            .max(1000)
            .messages({
                'string.max': 'Notes cannot exceed 1000 characters'
            })
    }),

    // GET /api/payments/statistics
    getPaymentStats: Joi.object({
        startDate: Joi.date()
            .iso()
            .allow(null, ''),
        endDate: Joi.date()
            .iso()
            .min(Joi.ref('startDate'))
            .allow(null, ''),
        groupBy: Joi.string()
            .valid('daily', 'monthly', 'method', 'status')
            .default('monthly')
            .messages({
                'any.only': 'Grouping option must be one of: daily, monthly, method, status'
            }),
        includeVoided: Joi.boolean()
            .default(true)
    }).unknown(true),

    // GET /api/payments/export
    exportPayments: Joi.object({
        format: Joi.string()
            .valid('csv', 'json')
            .default('csv')
            .messages({
                'any.only': 'Export format must be either csv or json'
            }),
        dateRange: Joi.object({
            start: Joi.date().iso().required(),
            end: Joi.date().iso().min(Joi.ref('start')).required()
        }),
        includeVoided: Joi.boolean(),
        groupBy: Joi.string()
            .valid('student', 'date', 'method', 'period')
            .messages({
                'any.only': 'Invalid grouping option'
            }),
        fields: Joi.string()
            .pattern(/^[a-zA-Z_]+(,[a-zA-Z_]+)*$/)
            .messages({
                'string.pattern.base': 'Invalid fields format. Use comma-separated field names'
            })
    }),

    // GET /api/payments/:id/receipt
    generateReceipt: Joi.object({
        format: Joi.string()
            .valid('pdf', 'html')
            .default('pdf')
            .messages({
                'any.only': 'Receipt format must be either pdf or html'
            })
    })
};

export default paymentValidation;