
// Basic room types - Updated to match backend response format
export interface Room {
  id: string;
  name: string;
  capacity: number;
  building: string;
  floor: number;
  features: string[];
  status: 'active' | 'maintenance' | 'inactive';
  currentSchedule?: {
    date: string;
    timeSlots: Array<{
      start: string;
      end: string;
      class: {
        id: string;
        name: string;
      };
    }>;
  };
  maintenanceSchedule: Array<{
    startDate: string;
    endDate: string;
    reason: string;
    scheduledBy: {
      id: string;
      username: string;
    };
  }>;
  lastModified: {
    at: Date;
    by: {
      id: string;
      username: string;
    };
  };
}

export interface RoomSchedule {
  id: string;
  day: string;
  timeStart: string;
  timeEnd: string;
  classId?: string;
  className?: string;
  teacherId?: string;
  teacherName?: string;
  recurring: boolean;
}

export interface RoomAvailability {
  id: string;
  name: string;
  available: boolean;
  conflicts?: string[];
}

// Enhanced Room filters - Updated to match backend query options
export interface RoomFilter {
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  status?: RoomStatus;
  building?: string;
  floor?: number;
  minCapacity?: number;
  features?: string[];
  search?: string;
  date?: string;
  timeStart?: string;
  timeEnd?: string;
}

// Alias for backward compatibility
export type RoomFilters = RoomFilter;

// Room schedule item (used for calendar display)
export interface RoomScheduleItem {
  id: string;
  title: string;
  type: 'class' | 'maintenance' | 'event';
  start: string;
  end: string;
  classId?: string;
  teacherId?: string;
  teacherName?: string;
  backgroundColor?: string;
}

// Room utilization stats
export interface RoomUtilization {
  period: string;
  usagePercentage: number;
  totalHours: number;
  usedHours: number;
  availableHours: number;
  peakTimes: {
    day: string;
    time: string;
    usagePercentage: number;
  }[];
}

// Maintenance periods
export interface MaintenancePeriod {
  id: string;
  startDate: string;
  endDate: string;
  reason: string;
  scheduledBy: {
    id: string;
    name: string;
  };
  createdAt: string;
}

// API Response types
export interface PaginatedRoomsResponse {
  success: boolean;
  data: Room[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}

export interface RoomAvailabilityResponse {
  available: boolean;
  conflictingEvents?: {
    id: string;
    type: string;
    name: string;
    start: string;
    end: string;
  }[];
}

export interface AvailableRoomsResponse {
  success: boolean;
  data: Room[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}

// Utility type for room features
export type RoomFeature = string;
export type RoomStatus = 'active' | 'maintenance' | 'inactive';
