import { toast } from "sonner";

// Types for dashboard data
export interface ClassStatistics {
  totalClasses: number;
  activeClasses: number;
  inactiveClasses: number;
  classesByLevel: {
    level: string;
    count: number;
    percentage: number;
  }[];
  classesByCapacity: {
    range: string;
    count: number;
    percentage: number;
  }[];
  averageClassSize: number;
  enrollmentTrend: {
    date: string;
    count: number;
  }[];
}

export interface StudentStatistics {
  totalStudents: number;
  activeStudents: number;
  inactiveStudents: number;
  pendingStudents: number;
  studentsByLevel: {
    level: string;
    count: number;
    percentage: number;
  }[];
  registrationTrend: {
    date: string;
    count: number;
  }[];
  retentionRate: number;
}

export interface AttendanceStatistics {
  overallAttendanceRate: number;
  attendanceByClass: {
    classId: string;
    className: string;
    attendanceRate: number;
    studentCount: number;
  }[];
  attendanceTrend: {
    date: string;
    rate: number;
    total: number;
    present: number;
    absent: number;
    excused: number;
  }[];
  topAttendingStudents: {
    studentId: string;
    studentName: string;
    attendanceRate: number;
    classCount: number;
  }[];
  lowAttendingStudents: {
    studentId: string;
    studentName: string;
    attendanceRate: number;
    classCount: number;
  }[];
}

export interface PaymentStatistics {
  totalRevenue: number;
  pendingPayments: number;
  overduePayments: number;
  revenueByPeriod: {
    period: string;
    amount: number;
    count: number;
  }[];
  revenueByMethod: {
    method: string;
    amount: number;
    percentage: number;
    count: number;
  }[];
  recentPayments: {
    id: string;
    studentName: string;
    amount: number;
    date: string;
    method: string;
  }[];
}

export interface RoomUtilization {
  totalRooms: number;
  activeRooms: number;
  underutilizedRooms: number;
  roomUtilizationRate: number;
  roomUtilizationByDay: {
    day: string;
    rate: number;
    hours: number;
  }[];
  roomUtilizationByRoom: {
    roomId: string;
    roomName: string;
    capacity: number;
    utilizationRate: number;
    hoursUsed: number;
    totalHours: number;
  }[];
}

export interface SystemActivity {
  recentActivities: {
    id: string;
    type: string;
    description: string;
    timestamp: string;
    user: string;
  }[];
  userActivity: {
    userId: string;
    userName: string;
    actionCount: number;
    lastActive: string;
  }[];
  activityByType: {
    type: string;
    count: number;
    percentage: number;
  }[];
}

export interface DashboardData {
  classStats: ClassStatistics;
  studentStats: StudentStatistics;
  attendanceStats: AttendanceStatistics;
  paymentStats: PaymentStatistics;
  roomUtilization: RoomUtilization;
  systemActivity: SystemActivity;
}

// Function to get class statistics
export async function getClassStatistics(params?: {
  fromDate?: string;
  toDate?: string;
  level?: string;
}): Promise<ClassStatistics> {
  try {
    // Build query parameters
    const queryParams = new URLSearchParams();
    if (params?.fromDate) queryParams.append('fromDate', params.fromDate);
    if (params?.toDate) queryParams.append('toDate', params.toDate);
    if (params?.level) queryParams.append('level', params.level);

    const response = await fetch(`http://localhost:3000/api/dashboard/class-statistics?${queryParams}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error("Error fetching class statistics:", error);
    toast.error("Failed to load class statistics");
    throw error;
  }
}

// Function to get student statistics
export async function getStudentStatistics(params?: {
  fromDate?: string;
  toDate?: string;
  level?: string;
  classId?: string;
}): Promise<StudentStatistics> {
  try {
    // Build query parameters
    const queryParams = new URLSearchParams();
    if (params?.fromDate) queryParams.append('fromDate', params.fromDate);
    if (params?.toDate) queryParams.append('toDate', params.toDate);
    if (params?.level) queryParams.append('level', params.level);

    const response = await fetch(`http://localhost:3000/api/dashboard/student-statistics?${queryParams}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error("Error fetching student statistics:", error);
    toast.error("Failed to load student statistics");
    throw error;
  }
}

// Function to get attendance statistics
export async function getAttendanceStatistics(params?: {
  fromDate?: string;
  toDate?: string;
  classId?: string;
  studentId?: string;
}): Promise<AttendanceStatistics> {
  try {
    // Build query parameters
    const queryParams = new URLSearchParams();
    if (params?.fromDate) queryParams.append('fromDate', params.fromDate);
    if (params?.toDate) queryParams.append('toDate', params.toDate);
    if (params?.classId) queryParams.append('classId', params.classId);
    if (params?.studentId) queryParams.append('studentId', params.studentId);

    const response = await fetch(`http://localhost:3000/api/dashboard/attendance-statistics?${queryParams}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error("Error fetching attendance statistics:", error);
    toast.error("Failed to load attendance statistics");
    throw error;
  }
}

// Function to get payment statistics
export async function getPaymentStatistics(params?: {
  startDate?: string;
  endDate?: string;
  groupBy?: 'daily' | 'monthly' | 'method' | 'status';
}): Promise<PaymentStatistics> {
  try {
    // In a real app, this would be an API call
    await new Promise(resolve => setTimeout(resolve, 650));
    
    // Mock data for payment statistics
    return {
      totalRevenue: 287500,
      pendingPayments: 34200,
      overduePayments: 12800,
      revenueByPeriod: [
        { period: "Jan 2023", amount: 45600, count: 38 },
        { period: "Feb 2023", amount: 42800, count: 35 },
        { period: "Mar 2023", amount: 48900, count: 41 },
        { period: "Apr 2023", amount: 51200, count: 43 },
        { period: "May 2023", amount: 47500, count: 39 },
        { period: "Jun 2023", amount: 51500, count: 42 }
      ],
      revenueByMethod: [
        { method: "Card", amount: 143750, percentage: 50, count: 115 },
        { method: "Cash", amount: 86250, percentage: 30, count: 69 },
        { method: "Transfer", amount: 43125, percentage: 15, count: 35 },
        { method: "Check", amount: 14375, percentage: 5, count: 12 }
      ],
      recentPayments: [
        { id: "payment1", studentName: "Alice Johnson", amount: 1200, date: "2023-06-15", method: "Card" },
        { id: "payment2", studentName: "Bob Smith", amount: 1500, date: "2023-06-14", method: "Transfer" },
        { id: "payment3", studentName: "Carol Williams", amount: 900, date: "2023-06-14", method: "Cash" },
        { id: "payment4", studentName: "David Miller", amount: 1200, date: "2023-06-13", method: "Card" },
        { id: "payment5", studentName: "Eve Brown", amount: 1700, date: "2023-06-12", method: "Check" }
      ]
    };
  } catch (error) {
    console.error("Error fetching payment statistics:", error);
    toast.error("Failed to load payment statistics");
    throw error;
  }
}

// Function to get room utilization
export async function getRoomUtilization(params?: {
  startDate?: string;
  endDate?: string;
  roomId?: string;
}): Promise<RoomUtilization> {
  try {
    // In a real app, this would be an API call
    await new Promise(resolve => setTimeout(resolve, 550));
    
    // Mock data for room utilization
    return {
      totalRooms: 12,
      activeRooms: 10,
      underutilizedRooms: 3,
      roomUtilizationRate: 72.5,
      roomUtilizationByDay: [
        { day: "Monday", rate: 85.2, hours: 34 },
        { day: "Tuesday", rate: 78.6, hours: 31 },
        { day: "Wednesday", rate: 82.4, hours: 33 },
        { day: "Thursday", rate: 76.9, hours: 30 },
        { day: "Friday", rate: 68.3, hours: 27 },
        { day: "Saturday", rate: 43.5, hours: 17 }
      ],
      roomUtilizationByRoom: [
        { roomId: "room1", roomName: "Room A101", capacity: 25, utilizationRate: 82.5, hoursUsed: 33, totalHours: 40 },
        { roomId: "room2", roomName: "Room B203", capacity: 30, utilizationRate: 77.5, hoursUsed: 31, totalHours: 40 },
        { roomId: "room3", roomName: "Room C305", capacity: 20, utilizationRate: 92.5, hoursUsed: 37, totalHours: 40 },
        { roomId: "room4", roomName: "Lab D102", capacity: 15, utilizationRate: 65.0, hoursUsed: 26, totalHours: 40 }
      ]
    };
  } catch (error) {
    console.error("Error fetching room utilization:", error);
    toast.error("Failed to load room utilization data");
    throw error;
  }
}

// Function to get system activity
export async function getSystemActivity(params?: {
  fromDate?: string;
  toDate?: string;
  limit?: number;
}): Promise<SystemActivity> {
  try {
    // In a real app, this would be an API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Mock data for system activity
    return {
      recentActivities: [
        { id: "activity1", type: "login", description: "User logged in", timestamp: "2023-06-15T09:23:11Z", user: "John Doe" },
        { id: "activity2", type: "student", description: "New student registered", timestamp: "2023-06-15T08:47:32Z", user: "Jane Smith" },
        { id: "activity3", type: "payment", description: "Payment processed", timestamp: "2023-06-15T08:12:45Z", user: "Robert Johnson" },
        { id: "activity4", type: "class", description: "Class schedule updated", timestamp: "2023-06-14T16:38:22Z", user: "Alice Brown" },
        { id: "activity5", type: "attendance", description: "Attendance marked", timestamp: "2023-06-14T15:05:17Z", user: "David Miller" }
      ],
      userActivity: [
        { userId: "user1", userName: "John Doe", actionCount: 45, lastActive: "2023-06-15T09:23:11Z" },
        { userId: "user2", userName: "Jane Smith", actionCount: 38, lastActive: "2023-06-15T08:47:32Z" },
        { userId: "user3", userName: "Robert Johnson", actionCount: 31, lastActive: "2023-06-15T08:12:45Z" },
        { userId: "user4", userName: "Alice Brown", actionCount: 27, lastActive: "2023-06-14T16:38:22Z" }
      ],
      activityByType: [
        { type: "login", count: 125, percentage: 23.5 },
        { type: "student", count: 98, percentage: 18.4 },
        { type: "payment", count: 87, percentage: 16.3 },
        { type: "class", count: 76, percentage: 14.3 },
        { type: "attendance", count: 146, percentage: 27.5 }
      ]
    };
  } catch (error) {
    console.error("Error fetching system activity:", error);
    toast.error("Failed to load system activity data");
    throw error;
  }
}

// Modified getDashboardData to be more reliable
export async function getDashboardData(role: string, params?: {
  fromDate?: string;
  toDate?: string;
}): Promise<Partial<DashboardData>> {
  try {
    // Build query parameters
    const queryParams = new URLSearchParams();
    if (params?.fromDate) queryParams.append('fromDate', params.fromDate);
    if (params?.toDate) queryParams.append('toDate', params.toDate);

    const response = await fetch(`http://localhost:3000/api/dashboard/data?${queryParams}`, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('jwt_token')}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data.data;
  } catch (error) {
    console.error("Error fetching dashboard data:", error);
    toast.error("Failed to load dashboard data. Using default display.");
    // Return a minimal valid data object instead of throwing
    return {
      classStats: {
        totalClasses: 0,
        activeClasses: 0,
        inactiveClasses: 0,
        classesByLevel: [],
        classesByCapacity: [],
        averageClassSize: 0,
        enrollmentTrend: []
      },
      studentStats: {
        totalStudents: 0,
        activeStudents: 0,
        inactiveStudents: 0,
        pendingStudents: 0,
        studentsByLevel: [],
        registrationTrend: [],
        retentionRate: 0
      }
    };
  }
}
