
import { useState } from "react";
import { Calendar as Calendar<PERSON><PERSON>, ChevronDown } from "lucide-react";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { cn } from "@/lib/utils";

interface DateRangeSelectorProps {
  onRangeChange: (range: { from: Date; to: Date }) => void;
  className?: string;
}

// Predefined date ranges
const DATE_RANGES = [
  { label: "Today", value: "today" },
  { label: "Yesterday", value: "yesterday" },
  { label: "Last 7 days", value: "last7days" },
  { label: "Last 30 days", value: "last30days" },
  { label: "This month", value: "thisMonth" },
  { label: "Last month", value: "lastMonth" },
  { label: "This year", value: "thisYear" },
  { label: "Custom range", value: "custom" },
];

const DateRangeSelector = ({ onRangeChange, className }: DateRangeSelectorProps) => {
  const [selectedRange, setSelectedRange] = useState("last7days");
  const [isOpen, setIsOpen] = useState(false);
  const [fromDate, setFromDate] = useState<Date | undefined>(
    new Date(Date.now() - 7 * 24 * 60 * 60 * 1000) // 7 days ago
  );
  const [toDate, setToDate] = useState<Date | undefined>(new Date());

  // Function to calculate date ranges based on selection
  const calculateDateRange = (rangeType: string): { from: Date; to: Date } => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    
    switch (rangeType) {
      case "today":
        return { from: today, to: now };
        
      case "yesterday": {
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        return { from: yesterday, to: yesterday };
      }
        
      case "last7days": {
        const last7Days = new Date(today);
        last7Days.setDate(last7Days.getDate() - 6);
        return { from: last7Days, to: today };
      }
        
      case "last30days": {
        const last30Days = new Date(today);
        last30Days.setDate(last30Days.getDate() - 29);
        return { from: last30Days, to: today };
      }
        
      case "thisMonth": {
        const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
        return { from: firstDayOfMonth, to: today };
      }
        
      case "lastMonth": {
        const firstDayOfLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        const lastDayOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
        return { from: firstDayOfLastMonth, to: lastDayOfLastMonth };
      }
        
      case "thisYear": {
        const firstDayOfYear = new Date(today.getFullYear(), 0, 1);
        return { from: firstDayOfYear, to: today };
      }
        
      case "custom":
        return { 
          from: fromDate || today, 
          to: toDate || today 
        };
        
      default:
        return { from: fromDate || today, to: toDate || today };
    }
  };

  // Handle range selection
  const handleRangeSelect = (value: string) => {
    setSelectedRange(value);
    
    if (value !== "custom") {
      const range = calculateDateRange(value);
      setFromDate(range.from);
      setToDate(range.to);
      onRangeChange(range);
    } else {
      setIsOpen(true);
    }
  };

  // Handle custom date selection
  const handleDateSelect = (date: Date | undefined) => {
    if (!fromDate || (fromDate && toDate)) {
      // Starting a new selection
      setFromDate(date);
      setToDate(undefined);
    } else {
      // Completing the selection
      if (date && date >= fromDate) {
        setToDate(date);
        onRangeChange({ from: fromDate, to: date });
        setIsOpen(false);
      } else if (date) {
        // If selecting a date before the from date, swap them
        setToDate(fromDate);
        setFromDate(date);
        onRangeChange({ from: date, to: fromDate });
        setIsOpen(false);
      }
    }
  };

  // Format date range for display
  const formatDateRange = (): string => {
    if (selectedRange !== "custom") {
      return DATE_RANGES.find(range => range.value === selectedRange)?.label || "";
    }
    
    if (!fromDate) return "Select dates";
    
    if (!toDate) {
      return `From ${format(fromDate, "PP")}`;
    }
    
    if (format(fromDate, "PP") === format(toDate, "PP")) {
      return format(fromDate, "PP");
    }
    
    return `${format(fromDate, "PP")} - ${format(toDate, "PP")}`;
  };

  return (
    <div className={cn("flex flex-col md:flex-row gap-2", className)}>
      <Select
        value={selectedRange}
        onValueChange={handleRangeSelect}
      >
        <SelectTrigger className="w-[200px]">
          <SelectValue placeholder="Select date range" />
        </SelectTrigger>
        <SelectContent>
          {DATE_RANGES.map((range) => (
            <SelectItem key={range.value} value={range.value}>
              {range.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      
      <Popover open={isOpen && selectedRange === "custom"} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            onClick={() => selectedRange === "custom" && setIsOpen(true)}
            className={cn(
              "w-full justify-between text-left font-normal",
              !fromDate && "text-muted-foreground"
            )}
          >
            <span>{formatDateRange()}</span>
            <CalendarIcon className="ml-2 h-4 w-4 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            initialFocus
            mode="range"
            defaultMonth={fromDate}
            selected={{
              from: fromDate,
              to: toDate,
            }}
            onSelect={(range) => {
              if (range?.from) setFromDate(range.from);
              if (range?.to) setToDate(range.to);
              if (range?.from && range?.to) {
                onRangeChange({ from: range.from, to: range.to });
                setIsOpen(false);
              }
            }}
            numberOfMonths={2}
          />
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default DateRangeSelector;
