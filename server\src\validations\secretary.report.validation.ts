// server/src/validations/secretary.report.validation.ts
import <PERSON><PERSON> from 'joi';

export const secretaryReportValidation = {
    generateReport: Joi.object({
        type: Joi.string()
            .valid(
                'student_registration',
                'payment_collection',
                'class_capacity',
                'student_attendance',
                'payment_due'
            )
            .required()
            .messages({
                'any.only': 'Invalid report type',
                'any.required': 'Report type is required'
            }),
        format: Joi.string()
            .valid('pdf', 'excel', 'csv', 'json')
            .required()
            .messages({
                'any.only': 'Invalid format',
                'any.required': 'Format is required'
            }),
        dateRange: Joi.object({
            startDate: Joi.date()
                .iso()
                .required()
                .messages({
                    'date.base': 'Invalid start date',
                    'any.required': 'Start date is required'
                }),
            endDate: Joi.date()
                .iso()
                .min(Joi.ref('startDate'))
                .required()
                .messages({
                    'date.base': 'Invalid end date',
                    'date.min': 'End date must be after start date',
                    'any.required': 'End date is required'
                })
        }).required(),
        filters: Joi.object({
            classId: Joi.string()
                .pattern(/^[0-9a-fA-F]{24}$/)
                .messages({
                    'string.pattern.base': 'Invalid class ID format'
                }),
            paymentStatus: Joi.string()
                .valid('paid', 'pending', 'overdue'),
            registrationStatus: Joi.string()
                .valid('active', 'inactive'),
            minCapacity: Joi.number()
                .integer()
                .min(1)
                .messages({
                    'number.base': 'Minimum capacity must be a number',
                    'number.integer': 'Minimum capacity must be an integer',
                    'number.min': 'Minimum capacity must be at least 1'
                }),
            maxCapacity: Joi.number()
                .integer()
                .min(Joi.ref('minCapacity'))
                .messages({
                    'number.base': 'Maximum capacity must be a number',
                    'number.integer': 'Maximum capacity must be an integer',
                    'number.min': 'Maximum capacity must be greater than minimum capacity'
                }),
            attendanceRate: Joi.number()
                .min(0)
                .max(100)
                .messages({
                    'number.base': 'Attendance rate must be a number',
                    'number.min': 'Attendance rate must be between 0 and 100',
                    'number.max': 'Attendance rate must be between 0 and 100'
                }),
            building: Joi.string(),
            floor: Joi.number()
                .integer()
                .min(0)
                .messages({
                    'number.base': 'Floor must be a number',
                    'number.integer': 'Floor must be an integer',
                    'number.min': 'Floor cannot be negative'
                })
        }),
        groupBy: Joi.string()
            .valid('class', 'date', 'status', 'building')
            .messages({
                'any.only': 'Invalid grouping option'
            }),
        sortBy: Joi.string()
            .valid('date', 'amount', 'name', 'dueDate')
            .messages({
                'any.only': 'Invalid sort field'
            }),
        sortOrder: Joi.string()
            .valid('asc', 'desc')
            .messages({
                'any.only': 'Sort order must be either asc or desc'
            })
    }).custom((value, helpers) => {
        // Additional validation based on report type
        switch (value.type) {
            case 'class_capacity':
                if (value.filters?.minCapacity && value.filters?.maxCapacity 
                    && value.filters.minCapacity > value.filters.maxCapacity) {
                    return helpers.error('any.custom', {
                        message: 'Maximum capacity must be greater than minimum capacity'
                    });
                }
                break;

            case 'payment_due':
                if (!value.filters?.paymentStatus && value.groupBy === 'status') {
                    return helpers.error('any.custom', {
                        message: 'Payment status filter is required when grouping by status'
                    });
                }
                break;

            case 'student_attendance':
                if (value.filters?.attendanceRate) {
                    const rate = value.filters.attendanceRate;
                    if (rate < 0 || rate > 100) {
                        return helpers.error('any.custom', {
                            message: 'Attendance rate must be between 0 and 100'
                        });
                    }
                }
                break;
        }
        return value;
    })
};