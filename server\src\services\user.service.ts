// server/src/services/user.service.ts
import mongoose, { FilterQuery } from 'mongoose';
import { User } from '../models/user.model';
import { AppError } from '../types/error.types';
import { SystemLogger } from './logger.service';
import { IUser } from '../types/auth.types';
import { AuthService } from './auth.service';

type UserRole = 'superAdmin' | 'manager' | 'secretary' | 'teacher';

interface UserQueryOptions {
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
    status?: 'active' | 'inactive';
    role?: UserRole;
    search?: string;
    fromDate?: Date;
    toDate?: Date;
    activityStatus?: 'online' | 'offline' | 'idle';
}

interface IUserActivity {
    userId: string;
    action: string;
    targetCollection: string;
    targetId?: string;
    details: any;
    timestamp: Date;
}

interface ArchiveDocument {
    _id: mongoose.Types.ObjectId;
    userId: mongoose.Types.ObjectId | undefined;  // Changed from string
    username: string;
    role: UserRole;
    archivedAt: Date;
    roleHistory: {
        role: string;
        changedAt: Date;
        changedBy: mongoose.Types.ObjectId;  // Changed from string
        reason: string;
    }[];
    lastLogin?: Date;
    createdAt: Date;
    createdBy: mongoose.Types.ObjectId;  // Changed from string
    modifiedAt: Date;
}

type RoleHierarchy = Record<UserRole, UserRole[]>;

export class UserService {
    private static readonly ROLES_HIERARCHY: RoleHierarchy = {
        superAdmin: ['manager', 'secretary', 'teacher'] as UserRole[],
        manager: ['teacher', 'secretary'] as UserRole[],
        secretary: [] as UserRole[],
        teacher: [] as UserRole[]
    };

    static async getUsers(options: UserQueryOptions = {}, requestingUserId: string) {
        try {
            const {
                page = 1,
                limit = 10,
                sortBy = 'username',
                sortOrder = 'asc',
                status,
                role,
                search,
                fromDate,
                toDate,
                activityStatus
            } = options;

            // Get requesting user's role for access control
            const requestingUser = await User.findById(requestingUserId);
            if (!requestingUser) {
                throw new AppError(404, 'Requesting user not found');
            }

            const query: FilterQuery<IUser> = {};

            // Role-based access control for user listing
            if (requestingUser.role !== 'superAdmin') {
                if (requestingUser.role === 'manager') {
                    query.role = { $in: ['teacher', 'secretary'] };
                } else {
                    throw new AppError(403, 'Unauthorized to list users');
                }
            }

            // Add filters
            if (status) {
                query.status = status;
            }

            if (role) {
                // Validate role access based on hierarchy
                if (!this.canManageRole(requestingUser.role as UserRole, role as UserRole)) {
                    throw new AppError(403, `Unauthorized to manage ${role} users`);
                }
                query.role = role;
            }

            if (search) {
                query.$or = [
                    { username: { $regex: search, $options: 'i' } },
                    { 'roleHistory.reason': { $regex: search, $options: 'i' } }
                ];
            }

            if (fromDate || toDate) {
                query.createdAt = {};
                if (fromDate) query.createdAt.$gte = fromDate;
                if (toDate) query.createdAt.$lte = toDate;
            }

            if (activityStatus) {
                const activityThreshold = new Date(Date.now() - 15 * 60 * 1000);
                if (activityStatus === 'online') {
                    query.lastLogin = { $gte: activityThreshold };
                } else if (activityStatus === 'offline') {
                    query.lastLogin = { $lt: activityThreshold };
                }
            }

            const skip = (page - 1) * limit;
            const sort: { [key: string]: 'asc' | 'desc' } = {
                [sortBy]: sortOrder
            };

            const users = await User.find(query)
                .select(this.getRoleSpecificFields(requestingUser.role))
                .sort(sort)
                .skip(skip)
                .limit(limit);

            const total = await User.countDocuments(query);

            await this.logUserActivity({
                userId: requestingUserId,
                action: 'list_users',
                targetCollection: 'users',
                details: { filters: options },
                timestamp: new Date()
            });

            return {
                users,
                pagination: {
                    total,
                    page,
                    limit,
                    pages: Math.ceil(total / limit)
                }
            };
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error fetching users');
        }
    }

    static async getUserById(id: string, requestingUserId: string) {
        const requestingUser = await User.findById(requestingUserId);
        if (!requestingUser) {
            throw new AppError(404, 'Requesting user not found');
        }

        const user = await User.findById(id)
            .select(this.getRoleSpecificFields(requestingUser.role));

        if (!user) {
            throw new AppError(404, 'User not found');
        }

        if (!this.canAccessUserDetails(requestingUser, user)) {
            throw new AppError(403, 'Unauthorized to view this user\'s details');
        }

        await this.logUserActivity({
            userId: requestingUserId,
            action: 'view_user',
            targetCollection: 'users',
            targetId: id,
            details: { viewedUser: user.username },
            timestamp: new Date()
        });

        return user;
    }

    static async updateUserStatus(
        id: string,
        status: 'active' | 'inactive',
        performedBy: string,
        reason: string
    ) {
        const [user, performingUser] = await Promise.all([
            User.findById(id),
            User.findById(performedBy)
        ]);

        if (!user || !performingUser) {
            throw new AppError(404, 'User not found');
        }

        if (!this.canManageRole(performingUser.role as UserRole, user.role as UserRole)) {
            throw new AppError(403, 'Unauthorized to manage this user\'s status');
        }

        if (id === performedBy && status === 'inactive') {
            throw new AppError(400, 'Cannot deactivate own account');
        }

        if (user.role === 'superAdmin' && status === 'inactive') {
            const activeSuperAdmins = await User.countDocuments({
                role: 'superAdmin',
                status: 'active',
                _id: { $ne: user._id }
            });

            if (activeSuperAdmins === 0) {
                throw new AppError(400, 'Cannot deactivate the last active SuperAdmin');
            }
        }

        if (status === 'inactive') {
            await this.archiveUserData(user);
        }

        if (status === 'active' && user.status === 'inactive') {
            await this.validateReactivation(user);
        }

        user.status = status;
        user.modifiedAt = new Date();
        await user.save();

        await SystemLogger.log({
            severity: 'info',
            category: 'user_management',
            action: 'status_update',
            performedBy,
            details: {
                targetUser: user.username,
                oldStatus: user.status,
                newStatus: status,
                reason
            },
            status: 'success',
            timestamp: new Date()
        });

        return user;
    }

    static async updateUserRole(
        id: string,
        newRole: UserRole,
        performedBy: string,
        reason: string
    ) {
        const [user, performingUser] = await Promise.all([
            User.findById(id),
            User.findById(performedBy)
        ]);

        if (!user || !performingUser) {
            throw new AppError(404, 'User not found');
        }

        if (!this.canManageRole(performingUser.role as UserRole, newRole) ||
            !this.canManageRole(performingUser.role as UserRole, user.role as UserRole)) {
            throw new AppError(403, 'Unauthorized to change to this role');
        }

        if (id === performedBy) {
            throw new AppError(400, 'Cannot update own role');
        }

        if (user.role === 'superAdmin' && newRole !== 'superAdmin') {
            const activeSuperAdmins = await User.countDocuments({
                role: 'superAdmin',
                status: 'active',
                _id: { $ne: user._id }
            });

            if (activeSuperAdmins === 0) {
                throw new AppError(400, 'Cannot change role: At least one SuperAdmin must exist');
            }
        }

        const oldRole = user.role;
        user.role = newRole;
        user.roleHistory.push({
            role: newRole,
            changedAt: new Date(),
            changedBy: new mongoose.Types.ObjectId(performedBy),  // Convert string to ObjectId
            reason
        });

        user.modifiedAt = new Date();
        await user.save();

        await SystemLogger.log({
            severity: 'info',
            category: 'user_management',
            action: 'role_update',
            performedBy,
            details: {
                targetUser: user.username,
                oldRole,
                newRole,
                reason
            },
            status: 'success',
            timestamp: new Date()
        });

        return user;
    }

    static async getUserActivities(userId: string, requestingUserId: string) {
        const [user, requestingUser] = await Promise.all([
            User.findById(userId),
            User.findById(requestingUserId)
        ]);

        if (!user || !requestingUser) {
            throw new AppError(404, 'User not found');
        }

        if (!this.canViewUserActivities(requestingUser, user)) {
            throw new AppError(403, 'Unauthorized to view user activities');
        }

        const activities = await SystemLogger.getUserActivities(userId);
        return activities;
    }

    private static canManageRole(managerRole: UserRole, targetRole: UserRole): boolean {
        return this.ROLES_HIERARCHY[managerRole]?.includes(targetRole) || managerRole === 'superAdmin';
    }

    public static async logUserActivity(activity: IUserActivity): Promise<void> {
        await SystemLogger.log({
            severity: 'info',
            category: 'user_activity',
            action: activity.action,
            performedBy: activity.userId,
            targetCollection: activity.targetCollection,
            targetId: activity.targetId,
            details: activity.details,
            timestamp: activity.timestamp,
            status: 'success'
        });
    }

    private static canAccessUserDetails(requestingUser: IUser, targetUser: IUser): boolean {
        if (!requestingUser._id || !targetUser._id) return false;

        if (requestingUser.role === 'superAdmin') return true;
        if (requestingUser.role === 'manager') {
            return ['teacher', 'secretary'].includes(targetUser.role as UserRole);
        }
        return requestingUser._id.toString() === targetUser._id.toString();
    }

    private static canViewUserActivities(requestingUser: IUser, targetUser: IUser): boolean {
        if (requestingUser.role === 'superAdmin') return true;
        if (requestingUser.role === 'manager') {
            return ['teacher', 'secretary'].includes(targetUser.role as UserRole);
        }
        return false;
    }

    private static getRoleSpecificFields(role: string): string {
        const baseFields = '-password -loginAttempts';
        if (role === 'superAdmin') return baseFields;
        if (role === 'manager') return `${baseFields} -roleHistory`;
        return `${baseFields} -roleHistory -loginAttempts`;
    }

    private static getAllowedExportFields(role: string): string[] {
        const baseFields = ['username', 'role', 'status', 'createdAt', 'lastLogin'];
        if (role === 'superAdmin') {
            return [...baseFields, 'roleHistory', 'createdBy', 'modifiedAt'];
        }
        return baseFields;
    }

    private static async archiveUserData(user: IUser): Promise<void> {
        const archiveData = {
            _id: new mongoose.Types.ObjectId(),
            userId: user._id,
            username: user.username,
            role: user.role,
            archivedAt: new Date(),
            roleHistory: user.roleHistory,
            lastLogin: user.lastLogin,
            createdAt: user.createdAt,
            createdBy: user.createdBy,
            modifiedAt: user.modifiedAt
        };
    
        if (!mongoose.connection.db) {
            throw new AppError(500, 'Database connection not established');
        }
    
        const result = await mongoose.connection.db
            .collection<ArchiveDocument>('user_archives')
            .insertOne(archiveData);
    
        await SystemLogger.log({
            severity: 'info',
            category: 'user_management',
            action: 'user_archived',
            performedBy: 'system',
            details: {
                userId: user._id,
                username: user.username,
                archiveId: result.insertedId // Use the insertedId from the result
            },
            status: 'success',
            timestamp: new Date()
        });
    }

    private static async validateReactivation(user: IUser): Promise<void> {
        const existingUser = await User.findOne({
            username: user.username,
            _id: { $ne: user._id },
            status: 'active'
        });

        if (existingUser) {
            throw new AppError(
                400,
                'Cannot reactivate user: username conflicts with an active user'
            );
        }

        const roleExists = ['superAdmin', 'manager', 'secretary', 'teacher'].includes(user.role as UserRole);
        if (!roleExists) {
            throw new AppError(400, 'Cannot reactivate user: invalid role');
        }

        if (!mongoose.connection.db) {
            throw new AppError(500, 'Database connection not established');
        }
        const archivedData = await mongoose.connection.db
            .collection('user_archives')
            .findOne({ userId: user._id });

        if (!archivedData) {
            await SystemLogger.log({
                severity: 'warning',
                category: 'user_management',
                action: 'reactivation_warning',
                performedBy: 'system',
                details: {
                    userId: user._id,
                    warning: 'No archive data found'
                },
                status: 'warning',
                timestamp: new Date()
            });
        }
    }

    static async checkUsernameExists(username: string): Promise<boolean> {
        const user = await User.findOne({ username });
        return !!user;
    }

    static async exportUsers(
        requestingUserId: string,
        format: string = 'csv',
        fields?: string[]
    ): Promise<string> {
        const requestingUser = await User.findById(requestingUserId);
        if (!requestingUser) {
            throw new AppError(404, 'Requesting user not found');
        }

        const query: FilterQuery<IUser> = {};
        if (requestingUser.role === 'manager') {
            query.role = { $in: ['teacher', 'secretary'] };
        } else if (requestingUser.role !== 'superAdmin') {
            throw new AppError(403, 'Unauthorized to export user data');
        }

        const allowedFields = this.getAllowedExportFields(requestingUser.role);
        const selectedFields = fields
            ? fields.filter(field => allowedFields.includes(field))
            : allowedFields;

        if (selectedFields.length === 0) {
            throw new AppError(400, 'No valid fields selected for export');
        }

        const users = await User.find(query)
            .select(selectedFields.join(' '))
            .sort({ username: 1 });

        await this.logUserActivity({
            userId: requestingUserId,
            action: 'export_users',
            targetCollection: 'users',
            details: { format, fields: selectedFields },
            timestamp: new Date()
        });

        return format === 'csv'
            ? this.convertToCSV(users, selectedFields)
            : JSON.stringify(users, null, 2);
    }

    private static convertToCSV(data: any[], fields: string[]): string {
        const header = fields.join(',');
        const rows = data.map(item => {
            return fields.map(field => {
                const value = this.getNestedValue(item, field);
                return this.formatCSVValue(value);
            }).join(',');
        });
        return [header, ...rows].join('\n');
    }

    private static getNestedValue(obj: any, path: string): any {
        return path.split('.').reduce((current, key) => current?.[key], obj);
    }

    private static formatCSVValue(value: any): string {
        if (value === null || value === undefined) return '';
        if (value instanceof Date) return value.toISOString();
        if (typeof value === 'object') return JSON.stringify(value);
        return `"${String(value).replace(/"/g, '""')}"`;
    }
}