// server/src/middleware/note.middleware.ts
import { Request, Response, NextFunction } from 'express';
import mongoose, { Types } from 'mongoose';
import { INoteDocument, Note } from '../models/note.model';
import { Student } from '../models/student.model';
import { Class } from '../models/class.model';
import { AppError } from '../types/error.types';
import { entityTypeToModel, NoteVisibility } from '../types/notes.types';
import { IUser } from '../types/auth.types';
import { SystemLogger } from '../services/logger.service';
import { getVisibilityQuery } from '../utils/permissionUtils';

export class NoteMiddleware {
    // Validate note exists
    static async validateNoteExists(
        req: Request,
        res: Response,
        next: NextFunction
    ): Promise<void> {
        try {
            const noteId = req.params.id;
            const note = await Note.findById(noteId);

            if (!note) {
                throw new AppError(404, 'Note not found');
            }

            // Attach note to request for later use
            req.note = note as INoteDocument;
            next();
        } catch (error) {
            next(error);
        }
    }

    // Validate user has access to the note
    static async validateNoteAccess(
        req: Request,
        res: Response,
        next: NextFunction
    ): Promise<void> {
        try {
            const currentUser = req.user as IUser;
            const note = req.note;

            if (!note) {
                throw new AppError(404, 'Note not found');
            }

            if (!currentUser?._id) {
                throw new AppError(401, 'Authentication required');
            }

            // Validate that the user role is valid
            const validRoles = ['superAdmin', 'manager', 'secretary', 'teacher'];
            if (!validRoles.includes(currentUser.role)) {
                throw new AppError(403, `Invalid role: ${currentUser.role}`);
            }

            const hasAccess = await note.validateVisibilityAccess(
                currentUser._id,
                currentUser.role
            );

            if (!hasAccess) {
                // Log attempted unauthorized access
                await SystemLogger.log({
                    severity: 'warning',
                    category: 'security',
                    action: 'unauthorized_note_access',
                    performedBy: currentUser._id.toString(),
                    targetId: note._id.toString(),
                    details: {
                        noteVisibility: note.visibility,
                        userRole: currentUser.role
                    },
                    status: 'failed',
                    timestamp: new Date()
                });

                throw new AppError(403, 'Not authorized to access this note');
            }

            next();
        } catch (error) {
            next(error);
        }
    }

    // Validate related entities exist (student, class)
    static async validateRelatedEntities(
        req: Request,
        res: Response,
        next: NextFunction
    ): Promise<void> {
        try {
            const { studentId, classId, relatedTo } = req.body;
            const validationPromises = [];

            // Validate student exists if provided
            if (studentId) {
                if (!mongoose.Types.ObjectId.isValid(studentId)) {
                    throw new AppError(400, 'Invalid student ID format');
                }

                validationPromises.push(
                    Student.findById(studentId)
                        .then(student => {
                            if (!student) throw new AppError(404, 'Referenced student not found');
                            return true;
                        })
                );
            }

            // Validate class exists if provided
            if (classId) {
                if (!mongoose.Types.ObjectId.isValid(classId)) {
                    throw new AppError(400, 'Invalid class ID format');
                }

                validationPromises.push(
                    Class.findById(classId)
                        .then(classDoc => {
                            if (!classDoc) throw new AppError(404, 'Referenced class not found');
                            return true;
                        })
                );
            }

            // Validate related entity if provided
            if (relatedTo) {
                const { type, id } = relatedTo;

                if (relatedTo && relatedTo.type && relatedTo.id) {
                    const { type, id } = relatedTo;

                    if (!mongoose.Types.ObjectId.isValid(id)) {
                        throw new AppError(400, 'Invalid related entity ID format');
                    }

                    validationPromises.push(
                        Promise.resolve().then(() => {
                            try {
                                const modelName = entityTypeToModel[type.toLowerCase()] || type;
                                const Model = mongoose.model(modelName);
                                return Model.findById(id)
                                    .then(entity => {
                                        if (!entity) throw new AppError(404, `Referenced ${type} not found`);
                                        return true;
                                    });
                            } catch (err) {
                                const error = err as Error;
                                if (error?.name === 'MissingSchemaError') {
                                    throw new AppError(400, `Invalid model type: ${type}`);
                                }
                                throw error;
                            }
                        })
                    );
                }
            }

            // Check if at least one reference exists
            if (!studentId && !classId && !relatedTo) {
                throw new AppError(400, 'Note must be associated with at least one entity');
            }

            // Wait for all validations to complete in parallel
            await Promise.all(validationPromises);
            next();
        } catch (error) {
            next(error);
        }
    }

    // Validate note visibility permissions
    static async validateVisibilityPermissions(
        req: Request,
        res: Response,
        next: NextFunction
    ): Promise<void> {
        try {
            const currentUser = req.user as IUser;
            const { visibility } = req.body;

            if (!visibility) {
                return next();
            }

            // Validate visibility permissions based on role
            switch (currentUser.role) {
                case 'superAdmin':
                case 'manager':
                    // Can set any visibility level
                    break;
                case 'teacher':
                    if (!['teacher_only', 'all_staff'].includes(visibility)) {
                        throw new AppError(403, 'Teachers can only set teacher_only or all_staff visibility');
                    }
                    break;
                default:
                    throw new AppError(403, 'Not authorized to set note visibility');
            }

            next();
        } catch (error) {
            next(error);
        }
    }

    // Validate note modification permissions
    static async validateModificationPermissions(
        req: Request,
        res: Response,
        next: NextFunction
    ): Promise<void> {
        try {
            const currentUser = req.user as IUser;
            const note = req.note;

            if (!note || !currentUser?._id) {
                throw new AppError(401, 'Authentication required');
            }

            // SuperAdmin can modify any note
            if (currentUser.role === 'superAdmin') {
                return next();
            }

            // Managers can modify any note they can see
            if (currentUser.role === 'manager' &&
                ['all_staff', 'manager_only'].includes(note.visibility)) {
                return next();
            }

            // Teachers can only modify their own notes or notes with appropriate visibility
            if (currentUser.role === 'teacher') {
                if (note.visibility === 'teacher_only' &&
                    !note.createdBy.equals(currentUser._id)) {
                    throw new AppError(403, 'Not authorized to modify this note');
                }
            }

            next();
        } catch (error) {
            next(error);
        }
    }

    // Validate bulk operation permissions
    static async validateBulkOperationPermissions(
        req: Request,
        res: Response,
        next: NextFunction
    ): Promise<void> {
        try {
            const currentUser = req.user as IUser;
            const { noteIds, operation } = req.body;

            if (!currentUser?._id) {
                throw new AppError(401, 'Authentication required');
            }

            // SuperAdmin can access all notes
            if (currentUser.role === 'superAdmin') {
                return next();
            }

            // Create a visibility query based on user role
            const visibilityQuery = getVisibilityQuery(currentUser.role, currentUser._id.toString());

            // Count notes that match both the ID list and visibility criteria (one query instead of multiple)
            const count = await Note.countDocuments({
                _id: { $in: noteIds.map((id: string) => new mongoose.Types.ObjectId(id)) },
                ...visibilityQuery
            });

            // Check if user has access to all requested notes
            if (count !== noteIds.length) {
                throw new AppError(403, `Not authorized to ${operation} one or more notes`);
            }

            next();
        } catch (error) {
            next(error);
        }
    }

    // Validate search permissions
    static async validateSearchPermissions(
        req: Request,
        res: Response,
        next: NextFunction
    ): Promise<void> {
        try {
            const currentUser = req.user as IUser;
            const { visibility } = req.query;

            if (visibility) {
                switch (currentUser.role) {
                    case 'superAdmin':
                    case 'manager':
                        // Can search any visibility level
                        break;
                    case 'teacher':
                        if (!['teacher_only', 'all_staff'].includes(visibility as string)) {
                            throw new AppError(403, 'Not authorized to search notes with this visibility level');
                        }
                        break;
                    case 'secretary':
                        if (visibility !== 'all_staff') {
                            throw new AppError(403, 'Secretaries can only search all_staff notes');
                        }
                        break;
                    default:
                        throw new AppError(403, 'Invalid role for note search');
                }
            }

            next();
        } catch (error) {
            next(error);
        }
    }
}