// server/src/types/attendance.types.ts
import { Types } from 'mongoose';

// Base types
export type AttendanceStatus = 'present' | 'absent' | 'late' | 'excused';
export type ExcuseStatus = 'pending' | 'approved' | 'rejected';

// Excuse document structure
export interface ExcuseDocument {
  reason: string;
  documentUrl?: string;
  verifiedBy?: Types.ObjectId;
  verifiedAt?: Date;
  status: ExcuseStatus;
  notes?: string;
}

// Individual student attendance record
export interface StudentAttendanceRecord {
  studentId: Types.ObjectId;
  status: AttendanceStatus;
  arrivalTime?: Date;
  excuse?: ExcuseDocument;
  notes?: string;
}

// Main attendance interface
export interface IAttendance {
  _id?: Types.ObjectId;
  classId: Types.ObjectId;
  date: Date;
  teacherId: Types.ObjectId;
  students: StudentAttendanceRecord[];
  isMakeupClass: boolean;
  makeupClassId?: Types.ObjectId;
  createdAt: Date;
  modifiedAt: Date;
  lastModifiedBy: Types.ObjectId;
  modificationHistory: Array<{
    modifiedBy: Types.ObjectId;
    timestamp: Date;
    changes: Record<string, any>;
  }>;
}

// DTOs for various operations
export interface MarkAttendanceDTO {
  studentId: string;
  status: AttendanceStatus;
  arrivalTime?: Date;
  excuse?: {
    reason: string;
    documentUrl?: string;
  };
  notes?: string;
}

export interface BulkMarkAttendanceDTO {
  records: MarkAttendanceDTO[];
}

export interface AddExcuseDTO {
  studentId: string;
  reason: string;
  documentUrl?: string;
  notes?: string;
}

export interface VerifyExcuseDTO {
  status: ExcuseStatus;
  verifiedBy: string;
  notes?: string;
}

// Query options for fetching attendance
export interface AttendanceQueryOptions {
  page?: number;
  limit?: number;
  sortBy?: 'date' | 'modifiedAt';
  sortOrder?: 'asc' | 'desc';
  startDate?: Date;
  endDate?: Date;
  classId?: string;
  teacherId?: string;
  studentId?: string;
  status?: AttendanceStatus;
  isMakeupClass?: boolean;
}

// Response DTOs
export interface AttendanceResponseDTO {
  id: string;
  classId: string;
  className?: string;
  date: Date;
  teacherId: string;
  teacherName?: string;
  students: Array<{
    studentId: string;
    studentName?: string;
    status: AttendanceStatus;
    arrivalTime?: Date;
    excuse?: {
      reason: string;
      documentUrl?: string;
      status: ExcuseStatus;
      verifiedBy?: string;
      verifiedAt?: Date;
    };
    notes?: string;
  }>;
  isMakeupClass: boolean;
  makeupClassId?: string;
  lastModified: {
    by: string;
    at: Date;
  };
}

// Statistics and Analytics
export interface StudentAttendanceStats {
  studentId: string;
  studentName?: string;
  totalClasses: number;
  present: number;
  absent: number;
  late: number;
  excused: number;
  attendanceRate: number;
  averageDelay?: number; // For late arrivals
  excusePattern: {
    total: number;
    approved: number;
    rejected: number;
    pending: number;
    commonReasons: Array<{ reason: string; count: number; }>;
  };
}

export interface ClassAttendanceStats {
  classId: string;
  className?: string;
  period: {
    start: Date;
    end: Date;
  };
  totalDays: number;
  averageAttendanceRate: number;
  attendanceByDate: Array<{
    date: Date;
    presentCount: number;
    absentCount: number;
    lateCount: number;
    excusedCount: number;
    attendanceRate: number;
  }>;
  studentStats: StudentAttendanceStats[];
}

// Export options
export interface AttendanceExportOptions {
  format: 'csv' | 'json';
  dateRange: {
    start: Date;
    end: Date;
  };
  includeStudentDetails?: boolean;
  includeExcuses?: boolean;
  includeMakeupClasses?: boolean;
  groupBy?: 'date' | 'student' | 'class';
}

// Bulk operations
export interface AttendanceBulkOperationDTO {
  operation: 'mark' | 'excuse' | 'verify';
  dates: Date[];
  classIds: string[];
  data: Record<string, any>;
  reason?: string;
}

// Error types
export interface AttendanceValidationError {
  field: string;
  message: string;
  code: string;
}

export interface AttendanceOperationError {
  operation: string;
  attendanceId: string;
  reason: string;
  details?: any;
}

// Event types for attendance-related notifications
export interface AttendanceEvent {
  type: 'marked' | 'modified' | 'excused' | 'verified';
  attendanceId: string;
  classId: string;
  studentIds: string[];
  timestamp: Date;
  details: any;
  notifyUsers: string[];
}

// Cache-related types for performance optimization
export interface AttendanceCacheKey {
  type: 'class' | 'student' | 'stats';
  id: string;
  date?: string;
  period?: string;
}

// Reporting types
export interface AttendanceReportTemplate {
  id: string;
  name: string;
  type: 'individual' | 'class' | 'summary';
  format: 'pdf' | 'excel';
  fields: string[];
  grouping?: {
    by: 'date' | 'student' | 'class';
    order: 'asc' | 'desc';
  };
  filters?: Record<string, any>;
}

// For consolidated attendance view across multiple classes
export interface ConsolidatedAttendanceRecord {
  date: Date;
  status: AttendanceStatus;
  className: string;
  classId: string;
  arrivalTime?: Date;
  excuse?: ExcuseDocument;
  notes?: string;
}

export interface UnifiedAttendanceResponse {
  studentId: string;
  studentName: string;
  attendanceRecords: ConsolidatedAttendanceRecord[];
  statistics: {
    totalDays: number;
    present: number;
    absent: number;
    late: number;
    excused: number;
    attendanceRate: number;
    byClass: Array<{
      classId: string;
      className: string;
      totalDays: number;
      present: number;
      absent: number;
      late: number;
      excused: number;
      attendanceRate: number;
    }>;
  };
}