import React, { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { fetchClasses } from "@/services/classService";
import { mergeClasses, checkMergeCompatibility } from "@/services/classMergeService";
import { Class } from "@/types/class";
import { ClassMergeRequest, MergeWizardStep, ClassOperationStatus, MergeCompatibilityResult } from "@/types/classMerge";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { AlertTriangle, ArrowRight, Check, X, AlertCircle, Users, Merge } from "lucide-react";
import { Progress } from "@/components/ui/progress";

interface ClassMergeDialogProps {
  isOpen: boolean;
  onClose: () => void;
  sourceClass: Class;
  onSuccess: () => void;
}

const ClassMergeDialog = ({ isOpen, onClose, sourceClass, onSuccess }: ClassMergeDialogProps) => {
  const { toast } = useToast();
  const [step, setStep] = useState<MergeWizardStep>("select-target");
  const [targetClassId, setTargetClassId] = useState<string>("");
  const [reason, setReason] = useState<string>("");
  const [mergeStatus, setMergeStatus] = useState<ClassOperationStatus>("idle");
  const [compatibility, setCompatibility] = useState<MergeCompatibilityResult | null>(null);

  useEffect(() => {
    if (isOpen) {
      setStep("select-target");
      setTargetClassId("");
      setReason("");
      setMergeStatus("idle");
      setCompatibility(null);
    }
  }, [isOpen]);

  const { data: classesData, isLoading: isLoadingClasses } = useQuery({
    queryKey: ["classes", { filter: { level: sourceClass.level, status: "active" } }],
    queryFn: () => fetchClasses({
      page: 1,
      limit: 100,
      level: sourceClass.level,
      status: "active",
      search: ""
    }),
    enabled: isOpen
  });

  const compatibleClasses = classesData?.data.filter(c => c.id !== sourceClass.id) || [];

  useEffect(() => {
    const checkCompatibility = async () => {
      if (!targetClassId) return;
      
      try {
        const result = await checkMergeCompatibility(sourceClass.id, targetClassId);
        setCompatibility(result);
      } catch (error) {
        console.error("Error checking compatibility:", error);
        toast({
          title: "Error",
          description: "Failed to check class compatibility",
          variant: "destructive"
        });
      }
    };

    if (targetClassId) {
      checkCompatibility();
    }
  }, [targetClassId, sourceClass.id, toast]);

  const targetClass = compatibleClasses.find(c => c.id === targetClassId);

  const handleMerge = async () => {
    if (!targetClassId || !reason) return;
    
    setMergeStatus("loading");
    
    try {
      const mergeData: ClassMergeRequest = {
        targetClassId,
        reason
      };
      
      await mergeClasses(sourceClass.id, mergeData);
      setMergeStatus("success");
      toast({
        title: "Classes Merged Successfully",
        description: `Classes have been merged into ${targetClass?.name}`,
      });
      
      setTimeout(() => {
        onSuccess();
        onClose();
      }, 1500);
    } catch (error) {
      setMergeStatus("error");
      toast({
        title: "Merge Failed",
        description: error instanceof Error ? error.message : "Failed to merge classes",
        variant: "destructive"
      });
    }
  };

  const canProceed = () => {
    switch (step) {
      case "select-target":
        return !!targetClassId && compatibility?.compatible;
      case "review-details":
        return !!reason && reason.length >= 10;
      case "confirm":
        return true;
      default:
        return false;
    }
  };

  const nextStep = () => {
    switch (step) {
      case "select-target":
        setStep("review-details");
        break;
      case "review-details":
        setStep("confirm");
        break;
      default:
        break;
    }
  };

  const prevStep = () => {
    switch (step) {
      case "review-details":
        setStep("select-target");
        break;
      case "confirm":
        setStep("review-details");
        break;
      default:
        break;
    }
  };

  const renderStepContent = () => {
    switch (step) {
      case "select-target":
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="source-class">Source Class</Label>
              <Input 
                id="source-class" 
                value={sourceClass.name} 
                disabled 
                className="bg-muted"
              />
              <p className="text-sm text-muted-foreground">
                Level: {sourceClass.level} • Room: {sourceClass.room} • 
                Students: {sourceClass.capacity.current}/{sourceClass.capacity.total}
              </p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="target-class">Select Target Class to Merge With</Label>
              <Select
                value={targetClassId}
                onValueChange={(value) => setTargetClassId(value)}
                disabled={isLoadingClasses || compatibleClasses.length === 0}
              >
                <SelectTrigger id="target-class">
                  <SelectValue placeholder="Select a class" />
                </SelectTrigger>
                <SelectContent>
                  {compatibleClasses.map((classItem) => (
                    <SelectItem key={classItem.id} value={classItem.id}>
                      {classItem.name} - {classItem.capacity.current}/{classItem.capacity.total} students
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              {isLoadingClasses && (
                <p className="text-sm text-muted-foreground">Loading compatible classes...</p>
              )}
              
              {!isLoadingClasses && compatibleClasses.length === 0 && (
                <div className="flex items-center mt-2 text-sm text-amber-600">
                  <AlertTriangle className="mr-2 h-4 w-4" />
                  No compatible classes found. Create a new class or change filters.
                </div>
              )}
            </div>
            
            {targetClassId && compatibility && (
              <Card className={compatibility.compatible ? "border-green-200" : "border-red-200"}>
                <CardHeader className="py-4">
                  <CardTitle className="text-base flex items-center">
                    {compatibility.compatible ? (
                      <>
                        <Check className="mr-2 h-5 w-5 text-green-500" />
                        Classes are compatible for merging
                      </>
                    ) : (
                      <>
                        <X className="mr-2 h-5 w-5 text-red-500" />
                        Classes cannot be merged
                      </>
                    )}
                  </CardTitle>
                </CardHeader>
                <CardContent className="py-2">
                  {!compatibility.compatible && (
                    <ul className="space-y-1 text-sm text-red-600">
                      {compatibility.issues.map((issue, index) => (
                        <li key={index} className="flex items-start">
                          <AlertCircle className="mr-2 h-4 w-4 mt-0.5 flex-shrink-0" />
                          {issue}
                        </li>
                      ))}
                    </ul>
                  )}
                  
                  {compatibility.mergedCapacity && (
                    <div className="space-y-2 mt-2">
                      <p className="text-sm font-medium">Merged Class Capacity</p>
                      <div className="flex items-center justify-between text-sm">
                        <span>
                          {compatibility.mergedCapacity.current} / {compatibility.mergedCapacity.total} students
                        </span>
                        <span className={compatibility.mergedCapacity.available >= 0 ? "text-green-600" : "text-red-600"}>
                          {compatibility.mergedCapacity.available >= 0 
                            ? `${compatibility.mergedCapacity.available} spots available` 
                            : `Exceeds capacity by ${Math.abs(compatibility.mergedCapacity.available)}`}
                        </span>
                      </div>
                      <Progress 
                        value={(compatibility.mergedCapacity.current / compatibility.mergedCapacity.total) * 100} 
                        className={`h-2 ${compatibility.mergedCapacity.available < 0 ? "bg-red-500" : ""}`}
                      />
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </div>
        );
      
      case "review-details":
        return (
          <div className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <Card>
                <CardHeader className="py-4">
                  <CardTitle className="text-base">Source Class</CardTitle>
                </CardHeader>
                <CardContent className="py-2">
                  <p className="font-medium">{sourceClass.name}</p>
                  <p className="text-sm text-muted-foreground">Level: {sourceClass.level}</p>
                  <p className="text-sm text-muted-foreground">Room: {sourceClass.room}</p>
                  <p className="text-sm text-muted-foreground">
                    Students: {sourceClass.capacity.current}/{sourceClass.capacity.total}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Teachers: {sourceClass.teachers.map(t => t.name).join(", ")}
                  </p>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="py-4">
                  <CardTitle className="text-base">Target Class</CardTitle>
                </CardHeader>
                <CardContent className="py-2">
                  <p className="font-medium">{targetClass?.name}</p>
                  <p className="text-sm text-muted-foreground">Level: {targetClass?.level}</p>
                  <p className="text-sm text-muted-foreground">Room: {targetClass?.room}</p>
                  <p className="text-sm text-muted-foreground">
                    Students: {targetClass?.capacity.current}/{targetClass?.capacity.total}
                  </p>
                  <p className="text-sm text-muted-foreground">
                    Teachers: {targetClass?.teachers.map(t => t.name).join(", ")}
                  </p>
                </CardContent>
              </Card>
            </div>
            
            <Card>
              <CardHeader className="py-4">
                <CardTitle className="text-base">Merged Result</CardTitle>
              </CardHeader>
              <CardContent className="py-2">
                <div className="space-y-2">
                  <p className="font-medium">{targetClass?.name}</p>
                  <p className="text-sm text-muted-foreground">Level: {targetClass?.level}</p>
                  <p className="text-sm text-muted-foreground">Room: {targetClass?.room}</p>
                  
                  {compatibility?.mergedCapacity && (
                    <div>
                      <p className="text-sm text-muted-foreground">
                        Students: {compatibility.mergedCapacity.current}/{compatibility.mergedCapacity.total}
                      </p>
                      <Progress 
                        value={(compatibility.mergedCapacity.current / compatibility.mergedCapacity.total) * 100} 
                        className="h-2 mt-1"
                      />
                    </div>
                  )}
                  
                  <div className="mt-3">
                    <p className="text-sm font-medium">Teachers:</p>
                    <ul className="text-sm text-muted-foreground">
                      {[...new Set([
                        ...(sourceClass.teachers || []),
                        ...(targetClass?.teachers || [])
                      ].map(t => t.name))].map((teacherName, idx) => (
                        <li key={idx} className="flex items-center">
                          <Check className="mr-2 h-3 w-3 text-green-500" />
                          {teacherName}
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <div className="space-y-2">
              <Label htmlFor="merge-reason" className="font-medium">
                Reason for Merging <span className="text-red-500">*</span>
              </Label>
              <Textarea
                id="merge-reason"
                placeholder="Explain why these classes are being merged..."
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                className="min-h-[100px]"
              />
              <p className="text-sm text-muted-foreground">
                {reason.length < 10 ? (
                  <span className="text-amber-600">Please provide a detailed reason (minimum 10 characters)</span>
                ) : (
                  "Thank you for providing a reason"
                )}
              </p>
            </div>
          </div>
        );
      
      case "confirm":
        return (
          <div className="space-y-4">
            <div className="rounded-lg border p-4 bg-amber-50">
              <div className="flex items-start">
                <AlertTriangle className="mr-3 h-5 w-5 text-amber-600 mt-0.5" />
                <div>
                  <h4 className="font-medium text-amber-800">This action cannot be undone</h4>
                  <p className="text-sm text-amber-700 mt-1">
                    Merging classes will permanently combine all students from {sourceClass.name} into {targetClass?.name}.
                    The source class will be marked as merged and will no longer be available.
                  </p>
                </div>
              </div>
            </div>
            
            <Card>
              <CardHeader className="py-4">
                <CardTitle className="text-base">Merge Summary</CardTitle>
              </CardHeader>
              <CardContent className="py-2">
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Source Class:</span>
                    <span className="font-medium">{sourceClass.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Target Class:</span>
                    <span className="font-medium">{targetClass?.name}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Students to Transfer:</span>
                    <span className="font-medium">{sourceClass.capacity.current}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-sm text-muted-foreground">Resulting Class Size:</span>
                    <span className="font-medium">
                      {compatibility?.mergedCapacity ? compatibility.mergedCapacity.current : "-"}/{compatibility?.mergedCapacity ? compatibility.mergedCapacity.total : "-"}
                    </span>
                  </div>
                  <div className="border-t pt-2 mt-2">
                    <p className="text-sm font-medium mb-1">Reason for merging:</p>
                    <p className="text-sm text-muted-foreground">{reason}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <div className="flex items-center justify-center">
              <Button
                variant="destructive"
                className="mt-2 w-full max-w-xs flex items-center justify-center"
                onClick={handleMerge}
                disabled={mergeStatus === "loading"}
              >
                {mergeStatus === "loading" ? (
                  <>Loading...</>
                ) : mergeStatus === "success" ? (
                  <>
                    <Check className="mr-2 h-4 w-4" />
                    Merged Successfully
                  </>
                ) : (
                  <>
                    <Merge className="mr-2 h-4 w-4" />
                    Confirm Merge
                  </>
                )}
              </Button>
            </div>
          </div>
        );
      
      default:
        return null;
    }
  };

  const renderProgress = () => {
    const steps = ["Select Target", "Review Details", "Confirm"];
    const currentStepIndex = steps.indexOf(
      step === "select-target" ? "Select Target" :
      step === "review-details" ? "Review Details" : "Confirm"
    );
    
    return (
      <div className="mb-6">
        <div className="flex justify-between">
          {steps.map((stepName, index) => (
            <div 
              key={index}
              className={`flex flex-col items-center ${
                index <= currentStepIndex ? "text-primary" : "text-muted-foreground"
              }`}
            >
              <div
                className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                  index < currentStepIndex 
                    ? "bg-primary text-primary-foreground border-primary" 
                    : index === currentStepIndex
                    ? "border-primary text-primary"
                    : "border-muted-foreground text-muted-foreground"
                }`}
              >
                {index < currentStepIndex ? (
                  <Check className="h-4 w-4" />
                ) : (
                  index + 1
                )}
              </div>
              <span className="text-xs mt-1">{stepName}</span>
            </div>
          ))}
        </div>
        <div className="relative mt-2">
          <div className="absolute top-0 left-0 right-0 h-1 bg-muted rounded-full" />
          <div 
            className="absolute top-0 left-0 h-1 bg-primary rounded-full transition-all"
            style={{ width: `${(currentStepIndex / (steps.length - 1)) * 100}%` }}
          />
        </div>
      </div>
    );
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Merge Classes</DialogTitle>
          <DialogDescription>
            Combine two classes into one. All students from the source class will be moved to the target class.
          </DialogDescription>
        </DialogHeader>
        
        {renderProgress()}
        
        <div className="py-2">
          {renderStepContent()}
        </div>
        
        <DialogFooter className="flex items-center justify-between sm:justify-between gap-2">
          {step !== "select-target" ? (
            <Button
              type="button"
              variant="outline"
              onClick={prevStep}
              disabled={mergeStatus === "loading" || mergeStatus === "success"}
            >
              Back
            </Button>
          ) : (
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={mergeStatus === "loading" || mergeStatus === "success"}
            >
              Cancel
            </Button>
          )}
          
          {step !== "confirm" && (
            <Button
              type="button"
              onClick={nextStep}
              disabled={!canProceed() || mergeStatus === "loading" || mergeStatus === "success"}
            >
              Next
              <ArrowRight className="ml-2 h-4 w-4" />
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ClassMergeDialog;

