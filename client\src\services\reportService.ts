import { toast } from "sonner";
import { 
  ReportOption, 
  ReportFormat, 
  ReportFilterOption, 
  ReportTemplate, 
  ReportGenerationRequest, 
  ReportPreviewData, 
  ReportResponse, 
  SavedReport 
} from "@/types/reports";

// Get all available report options
export const getAllReportOptions = (): ReportOption[] => {
  return [
    {
      id: "student_attendance",
      name: "Student Attendance Report",
      description: "Detailed attendance records for specific students",
      category: "attendance",
      formats: ["pdf", "excel", "csv"],
      type: "attendance",
      icon: "clipboard-list",
      role: ["teacher", "secretary", "manager", "superadmin"],
      filterOptions: [
        {
          id: "student_id",
          label: "Student",
          type: "select",
          placeholder: "Select student",
          options: [
            { label: "All Students", value: "all" },
            { label: "<PERSON>", value: "student1" },
            { label: "Maria Garcia", value: "student2" },
            { label: "Ahmed Khan", value: "student3" },
          ]
        },
        {
          id: "date_range",
          label: "Date Range",
          type: "select",
          placeholder: "Select period",
          options: [
            { label: "Current Month", value: "current_month" },
            { label: "Previous Month", value: "previous_month" },
            { label: "Current Term", value: "current_term" },
            { label: "Custom Range", value: "custom" },
          ]
        },
        {
          id: "include_excused",
          label: "Include Excused Absences",
          type: "checkbox",
          description: "Include absences that have been excused"
        }
      ]
    },
    {
      id: "class_attendance",
      name: "Class Attendance Report",
      description: "Attendance summary for specific classes",
      category: "attendance",
      formats: ["pdf", "excel", "csv"],
      type: "attendance",
      icon: "users",
      role: ["teacher", "secretary", "manager", "superadmin"],
      filterOptions: [
        {
          id: "class_id",
          label: "Class",
          type: "select",
          placeholder: "Select class",
          options: [
            { label: "All Classes", value: "all" },
            { label: "English 101", value: "class1" },
            { label: "Math 201", value: "class2" },
            { label: "Science 101", value: "class3" },
          ]
        },
        {
          id: "date_range",
          label: "Date Range",
          type: "select",
          placeholder: "Select period",
          options: [
            { label: "Current Month", value: "current_month" },
            { label: "Previous Month", value: "previous_month" },
            { label: "Current Term", value: "current_term" },
            { label: "Custom Range", value: "custom" },
          ]
        }
      ]
    },
    {
      id: "student_progress",
      name: "Student Progress Report",
      description: "Academic progress tracking for students",
      category: "students",
      formats: ["pdf", "excel"],
      type: "academic",
      icon: "graduation-cap",
      role: ["teacher", "manager", "superadmin"],
      filterOptions: [
        {
          id: "student_id",
          label: "Student",
          type: "select",
          placeholder: "Select student",
          options: [
            { label: "All Students", value: "all" },
            { label: "John Smith", value: "student1" },
            { label: "Maria Garcia", value: "student2" },
            { label: "Ahmed Khan", value: "student3" },
          ]
        },
        {
          id: "term",
          label: "Term",
          type: "select",
          placeholder: "Select term",
          options: [
            { label: "Current Term", value: "current" },
            { label: "Previous Term", value: "previous" },
            { label: "All Terms", value: "all" },
          ]
        }
      ]
    },
    {
      id: "payment_summary",
      name: "Payment Summary Report",
      description: "Summary of all payments received",
      category: "payments",
      formats: ["pdf", "excel", "csv"],
      type: "financial",
      icon: "credit-card",
      role: ["secretary", "manager", "superadmin"],
      filterOptions: [
        {
          id: "date_range",
          label: "Date Range",
          type: "select",
          placeholder: "Select period",
          options: [
            { label: "Current Month", value: "current_month" },
            { label: "Previous Month", value: "previous_month" },
            { label: "Current Year", value: "current_year" },
            { label: "Custom Range", value: "custom" },
          ]
        },
        {
          id: "payment_method",
          label: "Payment Method",
          type: "select",
          placeholder: "Select method",
          options: [
            { label: "All Methods", value: "all" },
            { label: "Credit Card", value: "card" },
            { label: "Cash", value: "cash" },
            { label: "Bank Transfer", value: "transfer" },
          ]
        }
      ]
    },
    {
      id: "outstanding_payments",
      name: "Outstanding Payments Report",
      description: "List of all outstanding payments",
      category: "payments",
      formats: ["pdf", "excel", "csv"],
      type: "financial",
      icon: "alert-circle",
      role: ["secretary", "manager", "superadmin"],
      filterOptions: [
        {
          id: "overdue_days",
          label: "Overdue By",
          type: "select",
          placeholder: "Select overdue period",
          options: [
            { label: "Any Overdue", value: "any" },
            { label: "Over 30 Days", value: "30" },
            { label: "Over 60 Days", value: "60" },
            { label: "Over 90 Days", value: "90" },
          ]
        }
      ]
    },
    {
      id: "enrollment_trends",
      name: "Enrollment Trends Report",
      description: "Analysis of enrollment patterns over time",
      category: "students",
      formats: ["pdf", "excel"],
      type: "analytics",
      icon: "bar-chart-2",
      role: ["manager", "superadmin"],
      filterOptions: [
        {
          id: "time_period",
          label: "Time Period",
          type: "select",
          placeholder: "Select period",
          options: [
            { label: "Last 6 Months", value: "6months" },
            { label: "Last Year", value: "1year" },
            { label: "Last 2 Years", value: "2years" },
            { label: "Custom Range", value: "custom" },
          ]
        },
        {
          id: "group_by",
          label: "Group By",
          type: "select",
          placeholder: "Select grouping",
          options: [
            { label: "Month", value: "month" },
            { label: "Quarter", value: "quarter" },
            { label: "Year", value: "year" },
          ]
        }
      ]
    },
    {
      id: "teacher_performance",
      name: "Teacher Performance Report",
      description: "Performance metrics for teachers",
      category: "system",
      formats: ["pdf", "excel"],
      type: "analytics",
      icon: "activity",
      role: ["manager", "superadmin"],
      filterOptions: [
        {
          id: "teacher_id",
          label: "Teacher",
          type: "select",
          placeholder: "Select teacher",
          options: [
            { label: "All Teachers", value: "all" },
            { label: "Jane Doe", value: "teacher1" },
            { label: "Robert Johnson", value: "teacher2" },
            { label: "Sarah Williams", value: "teacher3" },
          ]
        },
        {
          id: "time_period",
          label: "Time Period",
          type: "select",
          placeholder: "Select period",
          options: [
            { label: "Current Term", value: "current_term" },
            { label: "Previous Term", value: "previous_term" },
            { label: "Current Year", value: "current_year" },
            { label: "Custom Range", value: "custom" },
          ]
        }
      ]
    },
    {
      id: "new_registrations",
      name: "New Registrations Report",
      description: "List of newly registered students",
      category: "students",
      formats: ["pdf", "excel", "csv"],
      type: "registration",
      icon: "user-plus",
      role: ["secretary", "manager", "superadmin"],
      filterOptions: [
        {
          id: "date_range",
          label: "Registration Date",
          type: "select",
          placeholder: "Select period",
          options: [
            { label: "Last Week", value: "1week" },
            { label: "Last Month", value: "1month" },
            { label: "Last 3 Months", value: "3months" },
            { label: "Custom Range", value: "custom" },
          ]
        }
      ]
    }
  ];
};

// Export getReportOptions as an alias for getAllReportOptions for backward compatibility
export const getReportOptions = (userRole: string): Promise<ReportOption[]> => {
  // Simulate API call
  return new Promise((resolve) => {
    setTimeout(() => {
      const allOptions = getAllReportOptions();
      // Filter options based on user role if needed
      const filteredOptions = userRole 
        ? allOptions.filter(option => !option.role || option.role.includes(userRole.toLowerCase()))
        : allOptions;
      resolve(filteredOptions);
    }, 500);
  });
};

// Get saved report templates
export const getReportTemplates = async (): Promise<ReportTemplate[]> => {
  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 500));
  
  return [
    {
      id: "template1",
      name: "Monthly Attendance",
      description: "Standard monthly attendance report",
      reportType: "class_attendance",
      format: "pdf",
      parameters: {
        class_id: "all",
        date_range: "current_month"
      },
      createdBy: "John Smith",
      createdAt: "2023-05-15T10:30:00Z",
      lastUsed: "2023-06-01T14:20:00Z",
      isShared: true
    },
    {
      id: "template2",
      name: "Outstanding Payments (30+ days)",
      description: "Payments overdue by more than 30 days",
      reportType: "outstanding_payments",
      format: "excel",
      parameters: {
        overdue_days: "30"
      },
      createdBy: "Jane Doe",
      createdAt: "2023-04-20T09:15:00Z",
      lastUsed: "2023-06-05T11:45:00Z",
      isShared: true
    },
    {
      id: "template3",
      name: "Quarterly Enrollment Trends",
      description: "Enrollment analysis by quarter",
      reportType: "enrollment_trends",
      format: "pdf",
      parameters: {
        time_period: "1year",
        group_by: "quarter"
      },
      createdBy: "Robert Johnson",
      createdAt: "2023-03-10T15:45:00Z",
      lastUsed: "2023-06-02T10:30:00Z",
      isShared: false
    }
  ];
};

// Get saved reports
export const getSavedReports = async (): Promise<SavedReport[]> => {
  // Simulate API call
  await new Promise(resolve => setTimeout(resolve, 500));
  
  return [
    {
      id: "report1",
      name: "Monthly Attendance Summary",
      type: "attendance",
      format: "pdf",
      createdAt: "2023-06-10T14:30:00Z",
      createdBy: "John Smith",
      lastViewed: "2023-06-12T09:15:00Z",
      url: "/reports/files/monthly-attendance-june.pdf",
      generatedAt: "2023-06-10T14:30:00Z",
      size: "1.2 MB",
      downloadCount: 5
    },
    {
      id: "report2",
      name: "Outstanding Payments Q2",
      type: "financial",
      format: "excel",
      createdAt: "2023-06-05T11:45:00Z",
      createdBy: "Jane Doe",
      lastViewed: "2023-06-11T13:20:00Z",
      url: "/reports/files/outstanding-payments-q2.xlsx",
      generatedAt: "2023-06-05T11:45:00Z",
      size: "856 KB",
      downloadCount: 8
    },
    {
      id: "report3",
      name: "Student Progress - Spring Term",
      type: "academic",
      format: "pdf",
      createdAt: "2023-05-28T09:30:00Z",
      createdBy: "Robert Johnson",
      lastViewed: "2023-06-10T10:15:00Z",
      url: "/reports/files/student-progress-spring.pdf",
      generatedAt: "2023-05-28T09:30:00Z",
      size: "3.4 MB",
      downloadCount: 12
    },
    {
      id: "report4",
      name: "New Registrations - May",
      type: "registration",
      format: "csv",
      createdAt: "2023-06-01T16:20:00Z",
      createdBy: "Sarah Williams",
      lastViewed: "2023-06-08T14:30:00Z",
      url: "/reports/files/new-registrations-may.csv",
      generatedAt: "2023-06-01T16:20:00Z",
      size: "425 KB",
      downloadCount: 3
    },
    {
      id: "report5",
      name: "Teacher Performance Review",
      type: "analytics",
      format: "pdf",
      createdAt: "2023-05-15T13:45:00Z",
      createdBy: "Michael Brown",
      lastViewed: "2023-06-07T11:10:00Z",
      url: "/reports/files/teacher-performance-q2.pdf",
      generatedAt: "2023-05-15T13:45:00Z",
      size: "2.1 MB",
      downloadCount: 6
    }
  ];
};

// Generate report preview
export const previewReport = async (
  request: ReportGenerationRequest
): Promise<ReportPreviewData> => {
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock preview data based on report type
    switch (request.reportType) {
      case "student_attendance":
        return {
          columns: [
            { header: "Date", accessor: "date" },
            { header: "Status", accessor: "status" },
            { header: "Class", accessor: "class" },
            { header: "Notes", accessor: "notes" }
          ],
          data: [
            { date: "2023-06-01", status: "Present", class: "English 101", notes: "" },
            { date: "2023-06-02", status: "Present", class: "English 101", notes: "" },
            { date: "2023-06-05", status: "Absent", class: "English 101", notes: "Excused - Doctor's appointment" },
            { date: "2023-06-06", status: "Present", class: "English 101", notes: "" },
            { date: "2023-06-07", status: "Present", class: "English 101", notes: "" },
            { date: "2023-06-08", status: "Present", class: "English 101", notes: "Arrived 10 minutes late" },
            { date: "2023-06-09", status: "Present", class: "English 101", notes: "" }
          ],
          summary: [
            { title: "Total Classes", value: 7 },
            { title: "Present", value: 6 },
            { title: "Absent", value: 1 },
            { title: "Attendance Rate", value: "85.7%" }
          ]
        };
        
      case "class_attendance":
        return {
          columns: [
            { header: "Student", accessor: "student" },
            { header: "Present", accessor: "present" },
            { header: "Absent", accessor: "absent" },
            { header: "Excused", accessor: "excused" },
            { header: "Rate", accessor: "rate" }
          ],
          data: [
            { student: "John Smith", present: 18, absent: 2, excused: 1, rate: "90%" },
            { student: "Maria Garcia", present: 20, absent: 0, excused: 0, rate: "100%" },
            { student: "Ahmed Khan", present: 17, absent: 3, excused: 2, rate: "85%" },
            { student: "Lisa Chen", present: 16, absent: 4, excused: 3, rate: "80%" },
            { student: "Michael Brown", present: 19, absent: 1, excused: 1, rate: "95%" }
          ],
          summary: [
            { title: "Total Students", value: 5 },
            { title: "Class Sessions", value: 20 },
            { title: "Average Attendance", value: "90%" }
          ],
          charts: [
            {
              title: "Attendance Trend",
              type: "line",
              data: {
                labels: ["Week 1", "Week 2", "Week 3", "Week 4"],
                datasets: [
                  {
                    label: "Attendance Rate",
                    data: [92, 88, 90, 94]
                  }
                ]
              }
            }
          ]
        };
        
      case "payment_summary":
        return {
          columns: [
            { header: "Date", accessor: "date" },
            { header: "Student", accessor: "student" },
            { header: "Amount", accessor: "amount" },
            { header: "Method", accessor: "method" },
            { header: "Status", accessor: "status" }
          ],
          data: [
            { date: "2023-06-01", student: "John Smith", amount: "$500", method: "Credit Card", status: "Completed" },
            { date: "2023-06-02", student: "Maria Garcia", amount: "$500", method: "Bank Transfer", status: "Completed" },
            { date: "2023-06-03", student: "Ahmed Khan", amount: "$500", method: "Cash", status: "Completed" },
            { date: "2023-06-05", student: "Lisa Chen", amount: "$500", method: "Credit Card", status: "Completed" },
            { date: "2023-06-07", student: "Michael Brown", amount: "$500", method: "Bank Transfer", status: "Completed" }
          ],
          summary: [
            { title: "Total Payments", value: 5 },
            { title: "Total Amount", value: "$2,500" },
            { title: "Credit Card", value: "$1,000" },
            { title: "Bank Transfer", value: "$1,000" },
            { title: "Cash", value: "$500" }
          ],
          charts: [
            {
              title: "Payment Methods",
              type: "pie",
              data: {
                labels: ["Credit Card", "Bank Transfer", "Cash"],
                datasets: [
                  {
                    data: [40, 40, 20]
                  }
                ]
              }
            }
          ]
        };
        
      case "outstanding_payments":
        return {
          columns: [
            { header: "Student", accessor: "student" },
            { header: "Amount Due", accessor: "amount" },
            { header: "Due Date", accessor: "dueDate" },
            { header: "Days Overdue", accessor: "daysOverdue" },
            { header: "Last Contact", accessor: "lastContact" }
          ],
          data: [
            { student: "John Smith", amount: "$500", dueDate: "2023-05-01", daysOverdue: 40, lastContact: "2023-05-15" },
            { student: "Maria Garcia", amount: "$500", dueDate: "2023-05-15", daysOverdue: 26, lastContact: "2023-05-20" },
            { student: "Ahmed Khan", amount: "$500", dueDate: "2023-05-10", daysOverdue: 31, lastContact: "2023-05-25" }
          ],
          summary: [
            { title: "Total Outstanding", value: "$1,500" },
            { title: "Students Affected", value: 3 },
            { title: "Average Days Overdue", value: 32 }
          ]
        };
        
      default:
        return {
          columns: [
            { header: "No Preview Available", accessor: "message" }
          ],
          data: [
            { message: "No preview data available for this report type" }
          ]
        };
    }
  } catch (error) {
    console.error("Error generating report preview:", error);
    toast.error("Failed to generate report preview");
    throw error;
  }
};

// Generate and download report
export const generateReport = async (
  request: ReportGenerationRequest
): Promise<ReportResponse> => {
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Mock response
    const reportId = `report_${Date.now()}`;
    const timestamp = new Date().toISOString();
    
    // Get report name based on type
    const reportOptions = getAllReportOptions();
    const reportOption = reportOptions.find(option => option.id === request.reportType);
    const reportName = reportOption?.name || "Generated Report";
    
    return {
      id: reportId,
      name: request.templateName || reportName,
      url: `/reports/files/${reportId}.${request.format}`,
      createdAt: timestamp,
      format: request.format,
      type: reportOption?.type || "general",
      reportType: request.reportType,
      generatedAt: timestamp,
      generatedBy: "Current User",
      parameters: request.filters,
      status: "completed"
    };
  } catch (error) {
    console.error("Error generating report:", error);
    toast.error("Failed to generate report");
    throw error;
  }
};

// Save report as template
export const saveReportTemplate = async (
  name: string,
  description: string,
  reportType: string,
  format: ReportFormat,
  parameters: Record<string, any>,
  isShared: boolean
): Promise<ReportTemplate> => {
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Mock response
    const templateId = `template_${Date.now()}`;
    const timestamp = new Date().toISOString();
    
    return {
      id: templateId,
      name,
      description,
      reportType,
      format,
      parameters,
      createdBy: "Current User",
      createdAt: timestamp,
      isShared
    };
  } catch (error) {
    console.error("Error saving report template:", error);
    toast.error("Failed to save report template");
    throw error;
  }
};

// Delete saved report
export const deleteSavedReport = async (reportId: string): Promise<boolean> => {
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Mock successful deletion
    return true;
  } catch (error) {
    console.error("Error deleting report:", error);
    toast.error("Failed to delete report");
    throw error;
  }
};

// Delete report template
export const deleteReportTemplate = async (templateId: string): Promise<boolean> => {
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Mock successful deletion
    return true;
  } catch (error) {
    console.error("Error deleting template:", error);
    toast.error("Failed to delete template");
    throw error;
  }
};

// Get report by ID
export const getReportById = async (reportId: string): Promise<SavedReport | null> => {
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // Get all saved reports and find the one with matching ID
    const reports = await getSavedReports();
    const report = reports.find(r => r.id === reportId);
    
    return report || null;
  } catch (error) {
    console.error("Error fetching report:", error);
    toast.error("Failed to fetch report");
    throw error;
  }
};
