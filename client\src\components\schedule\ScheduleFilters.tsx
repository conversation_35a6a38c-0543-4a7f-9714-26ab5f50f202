
import React, { useState } from "react";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

const ScheduleFilters = () => {
  const [teacher, setTeacher] = useState<string>("");
  const [classLevel, setClassLevel] = useState<string>("");
  const [room, setRoom] = useState<string>("");
  const [keyword, setKeyword] = useState<string>("");

  const handleReset = () => {
    setTeacher("");
    setClassLevel("");
    setRoom("");
    setKeyword("");
  };

  const handleApply = () => {
    // Apply filters logic would go here
    console.log({ teacher, classLevel, room, keyword });
  };

  return (
    <div className="bg-muted/50 p-4 rounded-md mb-4">
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="space-y-2">
          <Label htmlFor="teacher">Teacher</Label>
          <Select value={teacher} onValueChange={setTeacher}>
            <SelectTrigger id="teacher">
              <SelectValue placeholder="Any teacher" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectItem value="any">Any teacher</SelectItem>
                <SelectItem value="teacher1">John Smith</SelectItem>
                <SelectItem value="teacher2">Jane Doe</SelectItem>
                <SelectItem value="teacher3">Robert Johnson</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="classLevel">Class Level</Label>
          <Select value={classLevel} onValueChange={setClassLevel}>
            <SelectTrigger id="classLevel">
              <SelectValue placeholder="Any level" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectItem value="any">Any level</SelectItem>
                <SelectItem value="beginner">Beginner</SelectItem>
                <SelectItem value="intermediate">Intermediate</SelectItem>
                <SelectItem value="advanced">Advanced</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="room">Room</Label>
          <Select value={room} onValueChange={setRoom}>
            <SelectTrigger id="room">
              <SelectValue placeholder="Any room" />
            </SelectTrigger>
            <SelectContent>
              <SelectGroup>
                <SelectItem value="any">Any room</SelectItem>
                <SelectItem value="room1">Room 101</SelectItem>
                <SelectItem value="room2">Room 102</SelectItem>
                <SelectItem value="room3">Main Hall</SelectItem>
              </SelectGroup>
            </SelectContent>
          </Select>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="keyword">Search</Label>
          <Input
            id="keyword"
            placeholder="Search by keyword"
            value={keyword}
            onChange={(e) => setKeyword(e.target.value)}
          />
        </div>
      </div>
      
      <div className="flex justify-end mt-4 space-x-2">
        <Button variant="outline" onClick={handleReset}>Reset</Button>
        <Button onClick={handleApply}>Apply Filters</Button>
      </div>
    </div>
  );
};

export default ScheduleFilters;
