// server/src/services/auth.service.ts
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { User } from '../models/user.model';
import { AUTH_CONFIG } from '../config/auth.config';
import { IUser } from '../types/auth.types';
import crypto from 'crypto';

export class AuthService {
  static async validatePassword(password: string): Promise<boolean> {
    const { minLength, requireUppercase, requireLowercase, requireNumbers, requireSpecialChars } = AUTH_CONFIG.PASSWORD_REQUIREMENTS;

    if (password.length < minLength) return false;
    if (requireUppercase && !/[A-Z]/.test(password)) return false;
    if (requireLowercase && !/[a-z]/.test(password)) return false;
    if (requireNumbers && !/[0-9]/.test(password)) return false;
    if (requireSpecialChars && !/[!@#$%^&*]/.test(password)) return false;

    return true;
  }

  static async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, AUTH_CONFIG.PASSWORD_SALT_ROUNDS);
  }

  static async comparePasswords(password: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(password, hashedPassword);
  }

  static generateToken(userId: string): string {
    return jwt.sign(
      { userId },
      AUTH_CONFIG.JWT_SECRET as jwt.Secret,
      {
        expiresIn: AUTH_CONFIG.JWT_EXPIRATION
      }
    );
  }

  static async checkLoginAttempts(user: IUser, ipAddress: string): Promise<boolean> {
    const recentAttempts = user.loginAttempts.filter(attempt =>
      !attempt.success &&
      attempt.timestamp.getTime() > Date.now() - AUTH_CONFIG.LOGIN_ATTEMPT_WINDOW &&
      attempt.ipAddress === ipAddress
    );

    return recentAttempts.length < AUTH_CONFIG.MAX_LOGIN_ATTEMPTS;
  }

  static generateSecurePassword(): string {
    const length = 12;
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    let password = '';

    // Ensure at least one of each required character type
    password += 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'[Math.floor(Math.random() * 26)]; // uppercase
    password += 'abcdefghijklmnopqrstuvwxyz'[Math.floor(Math.random() * 26)]; // lowercase
    password += '0123456789'[Math.floor(Math.random() * 10)]; // number
    password += '!@#$%^&*'[Math.floor(Math.random() * 8)]; // special char

    // Fill the rest randomly
    while (password.length < length) {
      const randomBytes = crypto.randomBytes(1);
      const randomIndex = randomBytes[0] % chars.length;
      password += chars[randomIndex];
    }

    // Shuffle the password
    return password.split('').sort(() => Math.random() - 0.5).join('');
  }
}