
import { useState, useEffect } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { TeacherScheduleItem } from "@/types/class";
import { TeacherScheduleData, ScheduleOperation } from "@/types/teacherScheduling";
import { capitalizeFirstLetter } from "@/lib/utils";
import { Trash, Save, X, AlertCircle } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface ScheduleEditingControlsProps {
  currentOperation: ScheduleOperation;
  scheduleData?: TeacherScheduleItem;
  teacherId?: string;
  teachers: TeacherScheduleData[];
  onSave: (teacherId: string, scheduleItem: TeacherScheduleItem) => void;
  onCancel: () => void;
  onDelete?: (teacherId: string, scheduleItem: TeacherScheduleItem) => void;
  conflicts?: TeacherScheduleItem[];
}

const ScheduleEditingControls = ({
  currentOperation,
  scheduleData,
  teacherId,
  teachers,
  onSave,
  onCancel,
  onDelete,
  conflicts,
}: ScheduleEditingControlsProps) => {
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [teacher, setTeacher] = useState(teacherId || "");
  const [day, setDay] = useState(scheduleData?.day || "monday");
  const [timeStart, setTimeStart] = useState(scheduleData?.timeStart || "09:00");
  const [timeEnd, setTimeEnd] = useState(scheduleData?.timeEnd || "10:00");
  const [hasValidationErrors, setHasValidationErrors] = useState(false);
  const [validationMessages, setValidationMessages] = useState<string[]>([]);
  
  // Days of the week for the dropdown
  const daysOfWeek = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"];
  
  useEffect(() => {
    // Reset the form when the operation or data changes
    setTeacher(teacherId || "");
    setDay(scheduleData?.day || "monday");
    setTimeStart(scheduleData?.timeStart || "09:00");
    setTimeEnd(scheduleData?.timeEnd || "10:00");
    validateForm();
  }, [currentOperation, scheduleData, teacherId]);
  
  // Custom validation for the form
  const validateForm = () => {
    const errors: string[] = [];
    
    if (!teacher) {
      errors.push("Teacher is required");
    }
    
    // Time format validation
    const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
    if (!timeRegex.test(timeStart)) {
      errors.push("Invalid start time format (HH:MM)");
    }
    
    if (!timeRegex.test(timeEnd)) {
      errors.push("Invalid end time format (HH:MM)");
    }
    
    // Check that end time is after start time
    if (timeRegex.test(timeStart) && timeRegex.test(timeEnd)) {
      const [startHour, startMinute] = timeStart.split(':').map(Number);
      const [endHour, endMinute] = timeEnd.split(':').map(Number);
      
      const startMinutes = startHour * 60 + startMinute;
      const endMinutes = endHour * 60 + endMinute;
      
      if (endMinutes <= startMinutes) {
        errors.push("End time must be after start time");
      }
    }
    
    setValidationMessages(errors);
    setHasValidationErrors(errors.length > 0);
    
    return errors.length === 0;
  };
  
  // Check if a time slot has a conflict
  const hasConflict = (): boolean => {
    if (!conflicts) return false;
    
    return conflicts.some(
      conflict => 
        conflict.day === day && 
        (
          // Check if the time ranges overlap
          (timeStart < conflict.timeEnd && timeEnd > conflict.timeStart)
        )
    );
  };
  
  const handleSave = () => {
    if (validateForm()) {
      const scheduleItem: TeacherScheduleItem = {
        day,
        timeStart,
        timeEnd,
      };
      
      onSave(teacher, scheduleItem);
    }
  };
  
  const handleDelete = () => {
    if (onDelete && teacherId && scheduleData) {
      onDelete(teacherId, scheduleData);
      setShowDeleteDialog(false);
    }
  };
  
  const getOperationTitle = (): string => {
    switch (currentOperation) {
      case "create": return "Add Schedule Slot";
      case "edit": return "Edit Schedule Slot";
      default: return "Schedule Slot";
    }
  };
  
  const getOperationDescription = (): string => {
    switch (currentOperation) {
      case "create": return "Add a new time slot to the schedule";
      case "edit": return "Modify an existing schedule slot";
      default: return "View schedule slot details";
    }
  };
  
  return (
    <>
      <Card>
        <CardHeader className="pb-3">
          <CardTitle>{getOperationTitle()}</CardTitle>
          <CardDescription>
            {getOperationDescription()}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {currentOperation === "view" ? (
            <div className="space-y-4">
              <div>
                <Label className="text-sm text-muted-foreground">Teacher</Label>
                <div className="font-medium mt-1">
                  {teachers.find(t => t.teacherId === teacherId)?.name || "Unknown"}
                </div>
              </div>
              <div>
                <Label className="text-sm text-muted-foreground">Day</Label>
                <div className="font-medium mt-1">
                  {capitalizeFirstLetter(day)}
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-sm text-muted-foreground">Start Time</Label>
                  <div className="font-medium mt-1">{timeStart}</div>
                </div>
                <div>
                  <Label className="text-sm text-muted-foreground">End Time</Label>
                  <div className="font-medium mt-1">{timeEnd}</div>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div>
                <Label htmlFor="teacher">Teacher</Label>
                <Select 
                  value={teacher} 
                  onValueChange={setTeacher}
                  disabled={currentOperation === "edit" && teacherId !== undefined}
                >
                  <SelectTrigger id="teacher">
                    <SelectValue placeholder="Select teacher" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      {teachers.map((t) => (
                        <SelectItem key={t.teacherId} value={t.teacherId}>
                          {t.name}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
              
              <div>
                <Label htmlFor="day">Day</Label>
                <Select value={day} onValueChange={setDay}>
                  <SelectTrigger id="day">
                    <SelectValue placeholder="Select day" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      {daysOfWeek.map((d) => (
                        <SelectItem key={d} value={d}>
                          {capitalizeFirstLetter(d)}
                        </SelectItem>
                      ))}
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="timeStart">Start Time</Label>
                  <Input
                    id="timeStart"
                    value={timeStart}
                    onChange={(e) => setTimeStart(e.target.value)}
                    placeholder="HH:MM"
                  />
                </div>
                <div>
                  <Label htmlFor="timeEnd">End Time</Label>
                  <Input
                    id="timeEnd"
                    value={timeEnd}
                    onChange={(e) => setTimeEnd(e.target.value)}
                    placeholder="HH:MM"
                  />
                </div>
              </div>
              
              {/* Validation errors */}
              {hasValidationErrors && (
                <div className="text-destructive text-sm space-y-1">
                  {validationMessages.map((message, index) => (
                    <div key={index} className="flex items-center">
                      <AlertCircle className="h-4 w-4 mr-1" />
                      {message}
                    </div>
                  ))}
                </div>
              )}
              
              {/* Conflict warning */}
              {hasConflict() && (
                <div className="p-3 bg-destructive/10 text-destructive rounded-md text-sm flex items-start">
                  <AlertCircle className="h-4 w-4 mr-2 mt-0.5 flex-shrink-0" />
                  <div>
                    <p className="font-medium">Scheduling Conflict</p>
                    <p className="mt-1">
                      This teacher is already scheduled for another class during this time slot.
                    </p>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
        <CardFooter className="flex justify-between">
          {currentOperation === "view" ? (
            <Button onClick={onCancel}>Close</Button>
          ) : (
            <>
              <div className="flex space-x-2">
                <Button variant="outline" onClick={onCancel}>
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
                {currentOperation === "edit" && onDelete && (
                  <Button 
                    variant="destructive" 
                    onClick={() => setShowDeleteDialog(true)}
                  >
                    <Trash className="h-4 w-4 mr-2" />
                    Delete
                  </Button>
                )}
              </div>
              <Button onClick={handleSave} disabled={hasValidationErrors}>
                <Save className="h-4 w-4 mr-2" />
                Save
              </Button>
            </>
          )}
        </CardFooter>
      </Card>
      
      {/* Confirmation dialog for deleting a schedule slot */}
      <AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Schedule Slot</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete this schedule slot?
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default ScheduleEditingControls;
