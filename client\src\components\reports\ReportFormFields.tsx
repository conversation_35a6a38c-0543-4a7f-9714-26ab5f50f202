
import React from 'react';
import { 
  ReportOption, 
  ReportFormat, 
  ReportGenerationRequest,
  ReportTemplate
} from '@/types/reports';

import {
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormDescription,
  FormMessage,
} from "@/components/ui/form";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Separator } from "@/components/ui/separator";

interface ReportFormFieldsProps {
  reportOption: ReportOption;
  formData: ReportGenerationRequest;
  onFormChange: (field: string, value: any) => void;
  templates: ReportTemplate[];
  onLoadTemplate: (templateId: string) => void;
}

const ReportFormFields = ({ 
  reportOption, 
  formData, 
  onFormChange,
  templates,
  onLoadTemplate
}: ReportFormFieldsProps) => {
  return (
    <div className="space-y-8">
      {/* Format Selection */}
      <div className="space-y-4">
        <div>
          <h3 className="text-lg font-medium">Report Format</h3>
          <p className="text-sm text-muted-foreground">
            Select the format for your report
          </p>
        </div>
        
        <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4">
          {reportOption.formats.map(format => (
            <div 
              key={format}
              className={`
                border rounded-lg p-4 cursor-pointer transition-colors
                flex flex-col items-center space-y-2
                ${formData.format === format ? 'border-primary bg-primary/5' : 'border-border hover:border-primary/50'}
              `}
              onClick={() => onFormChange('format', format)}
            >
              <span className="text-3xl">
                {format === 'pdf' && '📄'}
                {format === 'excel' && '📊'}
                {format === 'csv' && '📑'}
                {format === 'json' && '{ }'}
              </span>
              <span className="text-sm font-medium">{String(format).toUpperCase()}</span>
            </div>
          ))}
        </div>
      </div>
      
      <Separator />
      
      {/* Date Range Selection */}
      <div className="space-y-4">
        <div>
          <h3 className="text-lg font-medium">Date Range</h3>
          <p className="text-sm text-muted-foreground">
            Set the time period for the report data
          </p>
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <div className="space-y-2">
            <label htmlFor="startDate" className="text-sm font-medium">
              Start Date
            </label>
            <Input
              id="startDate"
              type="date"
              value={formData.dateRange.startDate}
              onChange={(e) => onFormChange('startDate', e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <label htmlFor="endDate" className="text-sm font-medium">
              End Date
            </label>
            <Input
              id="endDate"
              type="date"
              value={formData.dateRange.endDate}
              onChange={(e) => onFormChange('endDate', e.target.value)}
            />
          </div>
        </div>
      </div>
      
      <Separator />
      
      {/* Report Specific Filters */}
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium">Report Parameters</h3>
          <p className="text-sm text-muted-foreground">
            Configure specific options for this report type
          </p>
        </div>
        
        {/* Display different filter options based on report type */}
        {reportOption.filterOptions && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {reportOption.filterOptions.map(filter => (
              <div key={filter.id} className="space-y-2">
                <label htmlFor={filter.id} className="text-sm font-medium">
                  {filter.label}
                </label>
                
                {filter.type === 'select' && (
                  <Select
                    value={(formData.filters[filter.id] || '').toString()}
                    onValueChange={(value) => onFormChange(`filter.${filter.id}`, value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={`Select ${filter.label}`} />
                    </SelectTrigger>
                    <SelectContent>
                      {filter.options?.map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
                
                {filter.type === 'text' && (
                  <Input
                    id={filter.id}
                    value={(formData.filters[filter.id] || '').toString()}
                    onChange={(e) => onFormChange(`filter.${filter.id}`, e.target.value)}
                    placeholder={filter.placeholder || ''}
                  />
                )}
                
                {filter.type === 'number' && (
                  <Input
                    id={filter.id}
                    type="number"
                    value={(formData.filters[filter.id] || '').toString()}
                    onChange={(e) => onFormChange(`filter.${filter.id}`, parseInt(e.target.value) || 0)}
                    placeholder={filter.placeholder || ''}
                  />
                )}
                
                {filter.type === 'checkbox' && (
                  <div className="flex items-center space-x-2">
                    <Checkbox 
                      id={filter.id}
                      checked={!!formData.filters[filter.id]} 
                      onCheckedChange={(checked) => 
                        onFormChange(`filter.${filter.id}`, !!checked)
                      }
                    />
                    <label htmlFor={filter.id} className="text-sm text-muted-foreground">
                      {filter.placeholder || filter.label}
                    </label>
                  </div>
                )}
                
                {filter.description && (
                  <p className="text-xs text-muted-foreground mt-1">
                    {filter.description}
                  </p>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
      
      {/* Templates Section */}
      {templates.length > 0 && (
        <>
          <Separator />
          
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium">Saved Templates</h3>
              <p className="text-sm text-muted-foreground">
                Load a previously saved template
              </p>
            </div>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              {templates.map(template => (
                <Card 
                  key={template.id}
                  className="cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => onLoadTemplate(template.id)}
                >
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">{template.name}</CardTitle>
                    <CardDescription className="text-xs">
                      {new Date(template.createdAt).toLocaleDateString()}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="pb-3">
                    <div className="text-xs text-muted-foreground">
                      Format: {template.format.toUpperCase()}
                    </div>
                    {Object.keys(template.parameters).length > 0 && (
                      <div className="mt-2 flex flex-wrap gap-1">
                        {Object.keys(template.parameters).slice(0, 3).map(key => (
                          <span key={key} className="text-xs bg-muted px-2 py-1 rounded">
                            {key}
                          </span>
                        ))}
                        {Object.keys(template.parameters).length > 3 && (
                          <span className="text-xs bg-muted px-2 py-1 rounded">
                            +{Object.keys(template.parameters).length - 3} more
                          </span>
                        )}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default ReportFormFields;
