// server/src/scripts/addSampleData.ts
import mongoose from 'mongoose';
import { Student } from '../models/student.model';
import { Payment } from '../models/payment.model';
import { User } from '../models/user.model';
import { DB_CONFIG } from '../config/database.config';

async function addSampleData() {
    try {
        // Connect to database
        await mongoose.connect(DB_CONFIG.URI);
        console.log('Connected to database');

        // Find admin user to use as recordedBy
        const adminUser = await User.findOne({ username: 'admin@local' });
        if (!adminUser) {
            console.error('Admin user not found. Please run seed script first.');
            return;
        }

        // Create sample students
        const students = [
            {
                name: '<PERSON>',
                email: '<EMAIL>',
                phone: '************',
                dateOfBirth: new Date('2010-05-15'),
                address: '123 Main St, City, State',
                emergencyContact: {
                    name: '<PERSON>',
                    relationship: 'Mother',
                    phone: '************'
                },
                status: 'active',
                currentLevel: 'Beginner',
                enrollmentDate: new Date('2023-09-01'),
                createdBy: adminUser._id,
                createdAt: new Date(),
                modifiedAt: new Date(),
                payments: []
            },
            {
                name: 'Alice <PERSON>',
                email: '<EMAIL>',
                phone: '************',
                dateOfBirth: new Date('2012-08-22'),
                address: '456 Oak Ave, City, State',
                emergencyContact: {
                    name: 'Bob Smith',
                    relationship: 'Father',
                    phone: '************'
                },
                status: 'active',
                currentLevel: 'Intermediate',
                enrollmentDate: new Date('2023-08-15'),
                createdBy: adminUser._id,
                createdAt: new Date(),
                modifiedAt: new Date(),
                payments: []
            },
            {
                name: 'Michael Johnson',
                email: '<EMAIL>',
                phone: '************',
                dateOfBirth: new Date('2011-12-10'),
                address: '789 Pine St, City, State',
                emergencyContact: {
                    name: 'Sarah Johnson',
                    relationship: 'Mother',
                    phone: '************'
                },
                status: 'active',
                currentLevel: 'Advanced',
                enrollmentDate: new Date('2023-07-01'),
                createdBy: adminUser._id,
                createdAt: new Date(),
                modifiedAt: new Date(),
                payments: []
            }
        ];

        console.log('Creating sample students...');
        const createdStudents = [];
        
        for (const studentData of students) {
            const existingStudent = await Student.findOne({ email: studentData.email });
            if (existingStudent) {
                console.log(`Student ${studentData.name} already exists, skipping...`);
                createdStudents.push(existingStudent);
                continue;
            }

            const student = new Student(studentData);
            await student.save();
            createdStudents.push(student);
            console.log(`✅ Created student: ${student.name}`);
        }

        // Create sample payments
        console.log('Creating sample payments...');
        const payments = [
            {
                studentId: createdStudents[0]._id,
                amount: 250.00,
                remainingBalance: 0,
                status: 'completed',
                method: 'cash',
                date: new Date('2023-10-05'),
                nextDueDate: new Date('2023-11-05'),
                description: 'Monthly Tuition - October 2023',
                period: 'monthly',
                recordedBy: adminUser._id,
                recordedAt: new Date('2023-10-05'),
                receiptNumber: 'RCP-2023-001',
                notes: 'Payment received on time'
            },
            {
                studentId: createdStudents[1]._id,
                amount: 300.00,
                remainingBalance: 0,
                status: 'completed',
                method: 'bank_transfer',
                date: new Date('2023-10-06'),
                nextDueDate: new Date('2023-11-06'),
                description: 'Monthly Tuition - October 2023',
                period: 'monthly',
                recordedBy: adminUser._id,
                recordedAt: new Date('2023-10-06'),
                receiptNumber: 'RCP-2023-002'
            },
            {
                studentId: createdStudents[2]._id,
                amount: 500.00,
                remainingBalance: 0,
                status: 'voided',
                method: 'other',
                date: new Date('2023-10-07'),
                description: 'Quarterly Tuition - Q4 2023',
                period: 'quarterly',
                recordedBy: adminUser._id,
                recordedAt: new Date('2023-10-07'),
                voidedBy: adminUser._id,
                voidedAt: new Date('2023-10-07'),
                voidReason: 'Incorrect amount',
                receiptNumber: 'RCP-2023-003'
            },
            {
                studentId: createdStudents[0]._id,
                amount: 150.00,
                remainingBalance: 0,
                status: 'completed',
                method: 'cash',
                date: new Date('2023-09-05'),
                nextDueDate: new Date('2023-10-05'),
                description: 'Monthly Tuition - September 2023',
                period: 'monthly',
                recordedBy: adminUser._id,
                recordedAt: new Date('2023-09-05'),
                receiptNumber: 'RCP-2023-004'
            },
            {
                studentId: createdStudents[1]._id,
                amount: 250.00,
                remainingBalance: 250.00,
                status: 'pending',
                method: 'other',
                date: new Date('2023-10-08'),
                nextDueDate: new Date('2023-11-08'),
                description: 'Monthly Tuition - October 2023',
                period: 'monthly',
                recordedBy: adminUser._id,
                recordedAt: new Date('2023-10-08'),
                receiptNumber: 'RCP-2023-005',
                notes: 'Payment pending confirmation'
            }
        ];

        for (const paymentData of payments) {
            const existingPayment = await Payment.findOne({ receiptNumber: paymentData.receiptNumber });
            if (existingPayment) {
                console.log(`Payment ${paymentData.receiptNumber} already exists, skipping...`);
                continue;
            }

            const payment = new Payment(paymentData);
            await payment.save();
            console.log(`✅ Created payment: ${payment.receiptNumber} - $${payment.amount}`);
        }

        console.log('\n🎉 Sample data created successfully!');
        console.log('\n📋 Created:');
        console.log(`- ${createdStudents.length} students`);
        console.log(`- ${payments.length} payments`);

    } catch (error) {
        console.error('Error adding sample data:', error);
        if (error instanceof Error) {
            console.error('Error details:', error.message);
        }
    } finally {
        await mongoose.disconnect();
        console.log('Disconnected from database');
    }
}

// Run the function
addSampleData();
