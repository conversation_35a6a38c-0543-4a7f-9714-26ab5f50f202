// server/src/routes/dashboard.routes.ts
import { Router } from 'express';
import { DashboardController } from '../controllers/dashboard.controller';
import { authorizeRoles } from '../middleware/auth.middleware';
import { validate } from '../middleware/validate.middleware';
import { dashboardValidation } from '../validations/dashboard.validation';
import { catchAsync } from '../middleware/error.middleware';
import { apiLimiter } from '../middleware/rateLimiter.middleware';

const router = Router();

// Get comprehensive dashboard data
router.get(
    '/data',
    authorizeRoles('superAdmin', 'manager', 'secretary', 'teacher'),
    validate(dashboardValidation.getDashboardData),
    catchAsync(DashboardController.getDashboardData)
);

// Get class statistics
router.get(
    '/class-statistics',
    authorizeRoles('superAdmin', 'manager', 'secretary', 'teacher'),
    validate(dashboardValidation.getStatistics),
    catchAsync(DashboardController.getClassStatistics)
);

// Get student statistics
router.get(
    '/student-statistics',
    authorizeRoles('superAdmin', 'manager', 'secretary', 'teacher'),
    validate(dashboardValidation.getStatistics),
    catchAsync(DashboardController.getStudentStatistics)
);

// Get attendance statistics
router.get(
    '/attendance-statistics',
    authorizeRoles('superAdmin', 'manager', 'teacher'),
    validate(dashboardValidation.getStatistics),
    catchAsync(DashboardController.getAttendanceStatistics)
);

// Get payment statistics
router.get(
    '/payment-statistics',
    authorizeRoles('superAdmin', 'manager', 'secretary'),
    validate(dashboardValidation.getPaymentStatistics),
    catchAsync(DashboardController.getPaymentStatistics)
);

// Get room utilization
router.get(
    '/room-utilization',
    authorizeRoles('superAdmin', 'manager'),
    validate(dashboardValidation.getRoomUtilization),
    catchAsync(DashboardController.getRoomUtilization)
);

// Get system activity
router.get(
    '/system-activity',
    authorizeRoles('superAdmin', 'manager', 'secretary'),
    validate(dashboardValidation.getSystemActivity),
    catchAsync(DashboardController.getSystemActivity)
);

// Apply rate limiting to all routes
router.use(apiLimiter);

export default router;
