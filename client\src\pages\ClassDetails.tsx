
import { useParams, useNavigate } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { fetchClassById } from "@/services/classService";
import { hasRole } from "@/lib/auth";
import MainLayout from "@/components/layout/MainLayout";
import ClassDetailsView from "@/components/classes/ClassDetailsView";

const ClassDetails = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  
  const { 
    data: classData, 
    isLoading,
    isError,
    error
  } = useQuery({
    queryKey: ["class", id],
    queryFn: () => fetchClassById(id || ""),
    enabled: !!id,
  });

  // Handle error state
  if (isError) {
    return (
      <MainLayout>
        <div className="container mx-auto py-6">
          <div className="flex flex-col items-center justify-center h-64 text-center">
            <h2 className="text-2xl font-bold text-destructive mb-2">Error Loading Class</h2>
            <p className="text-muted-foreground mb-4">
              {error instanceof Error ? error.message : "Failed to load class details."}
            </p>
            <button
              onClick={() => navigate("/classes")}
              className="inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground"
            >
              Return to Classes
            </button>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="container mx-auto py-6">
        <ClassDetailsView 
          classData={classData?.data} 
          isLoading={isLoading} 
        />
      </div>
    </MainLayout>
  );
};

export default ClassDetails;
