
import { useEffect, useState } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate, Link } from "react-router-dom";
import { useQuery } from "@tanstack/react-query";
import { fetchStudent, archiveStudent, restoreStudent } from "@/services/studentService";
import MainLayout from "@/components/layout/MainLayout";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { toast } from "sonner";
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { cn } from "@/lib/utils";
import {
  Edit,
  Archive,
  RotateCcw,
  CreditCard,
  Repeat,
  MessageSquare,
  ChevronLeft,
  Trash2
} from "lucide-react";
import StudentOverviewTab from "@/components/students/StudentOverviewTab";
import StudentAttendanceTab from "@/components/students/StudentAttendanceTab";
import StudentPaymentsTab from "@/components/students/StudentPaymentsTab";
import StudentClassesTab from "@/components/students/StudentClassesTab";
import StudentNotesTab from "@/components/students/StudentNotesTab";
import StudentHistoryTab from "@/components/students/StudentHistoryTab";

export default function StudentDetail() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("overview");
  const [isArchiving, setIsArchiving] = useState(false);
  const [isRestoring, setIsRestoring] = useState(false);

  // Fetch student details
  const { 
    data, 
    isLoading, 
    isError, 
    refetch 
  } = useQuery({
    queryKey: ['student', id],
    queryFn: () => fetchStudent(id!),
    enabled: !!id
  });

  const student = data?.data;

  const handleArchive = async () => {
    if (!id) return;
    
    setIsArchiving(true);
    try {
      const result = await archiveStudent(id);
      if (result.success) {
        toast.success("Student archived successfully");
        refetch();
      } else {
        toast.error("Failed to archive student");
      }
    } catch (error) {
      toast.error("An error occurred while archiving student");
      console.error(error);
    } finally {
      setIsArchiving(false);
    }
  };

  const handleRestore = async () => {
    if (!id) return;
    
    setIsRestoring(true);
    try {
      const result = await restoreStudent(id);
      if (result.success) {
        toast.success("Student restored successfully");
        refetch();
      } else {
        toast.error("Failed to restore student");
      }
    } catch (error) {
      toast.error("An error occurred while restoring student");
      console.error(error);
    } finally {
      setIsRestoring(false);
    }
  };

  if (isLoading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full"></div>
        </div>
      </MainLayout>
    );
  }

  if (isError || !student) {
    return (
      <MainLayout>
        <div className="flex flex-col items-center justify-center min-h-[60vh]">
          <p className="text-xl text-muted-foreground mb-4">
            Could not load student details
          </p>
          <Button onClick={() => refetch()}>Try Again</Button>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header with navigation and actions */}
        <div className="flex flex-col md:flex-row md:items-center gap-4 justify-between">
          <div className="flex items-center gap-2">
            <Button 
              variant="ghost" 
              size="icon" 
              onClick={() => navigate('/students')}
            >
              <ChevronLeft size={18} />
            </Button>
            <h1 className="text-2xl font-bold">
              {student.firstName} {student.lastName}
            </h1>
            <Badge
              variant="outline"
              className={cn(
                "ml-2 capitalize",
                student.status === "active" && "border-green-200 bg-green-50 text-green-700 dark:bg-green-900/30 dark:text-green-400 dark:border-green-900",
                student.status === "inactive" && "border-red-200 bg-red-50 text-red-700 dark:bg-red-900/30 dark:text-red-400 dark:border-red-900",
                student.status === "pending" && "border-yellow-200 bg-yellow-50 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400 dark:border-yellow-900",
                student.status === "archived" && "border-gray-200 bg-gray-50 text-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-700"
              )}
            >
              {student.status}
            </Badge>
          </div>
          
          <div className="flex flex-wrap gap-2">
            <Button 
              variant="outline" 
              size="sm" 
              asChild
            >
              <Link to={`/students/${id}/edit`}>
                <Edit size={16} className="mr-1" />
                Edit
              </Link>
            </Button>
            
            <Button 
              variant="outline" 
              size="sm" 
              asChild
            >
              <Link to={`/students/${id}/payment`}>
                <CreditCard size={16} className="mr-1" />
                Payment
              </Link>
            </Button>
            
            <Button 
              variant="outline" 
              size="sm" 
              asChild
            >
              <Link to={`/students/${id}/transfer`}>
                <Repeat size={16} className="mr-1" />
                Transfer
              </Link>
            </Button>
            
            <Button 
              variant="outline" 
              size="sm"
              asChild
            >
              <Link to={`/students/${id}/notes/new`}>
                <MessageSquare size={16} className="mr-1" />
                Add Note
              </Link>
            </Button>
            
            {student.status === "archived" ? (
              <Button 
                variant="outline" 
                size="sm"
                onClick={handleRestore}
                disabled={isRestoring}
              >
                <RotateCcw size={16} className="mr-1" />
                {isRestoring ? "Restoring..." : "Restore"}
              </Button>
            ) : (
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button 
                    variant="outline" 
                    size="sm"
                  >
                    <Archive size={16} className="mr-1" />
                    Archive
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Archive Student</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to archive this student? The student will no longer appear in active lists but can be restored later.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction 
                      onClick={handleArchive}
                      disabled={isArchiving}
                    >
                      {isArchiving ? "Archiving..." : "Archive"}
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            )}
            
            <AlertDialog>
              <AlertDialogTrigger asChild>
                <Button 
                  variant="destructive" 
                  size="sm"
                >
                  <Trash2 size={16} className="mr-1" />
                  Delete
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle>Delete Student</AlertDialogTitle>
                  <AlertDialogDescription>
                    Are you sure you want to permanently delete this student? This action cannot be undone.
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction className="bg-red-600 hover:bg-red-700">
                    Delete Permanently
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>
        </div>
        
        <Separator />
        
        {/* Student information tabs */}
        <Tabs 
          defaultValue="overview" 
          value={activeTab} 
          onValueChange={setActiveTab}
        >
          <TabsList className="mb-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="attendance">Attendance</TabsTrigger>
            <TabsTrigger value="payments">Payments</TabsTrigger>
            <TabsTrigger value="classes">Classes</TabsTrigger>
            <TabsTrigger value="notes">Notes</TabsTrigger>
            <TabsTrigger value="history">History</TabsTrigger>
          </TabsList>
          
          <TabsContent value="overview" className="mt-0">
            <StudentOverviewTab student={student} />
          </TabsContent>
          
          <TabsContent value="attendance" className="mt-0">
            <StudentAttendanceTab studentId={student.id} />
          </TabsContent>
          
          <TabsContent value="payments" className="mt-0">
            <StudentPaymentsTab studentId={student.id} payments={student.payments || []} />
          </TabsContent>
          
          <TabsContent value="classes" className="mt-0">
            <StudentClassesTab 
              studentId={student.id} 
              currentClass={student.currentClass} 
              classHistory={student.classHistory || []} 
            />
          </TabsContent>
          
          <TabsContent value="notes" className="mt-0">
            <StudentNotesTab studentId={student.id} notes={student.notes || []} onNotesUpdated={refetch} />
          </TabsContent>
          
          <TabsContent value="history" className="mt-0">
            <StudentHistoryTab 
              studentId={student.id} 
              levelHistory={student.levelHistory || []} 
              classHistory={student.classHistory || []}
            />
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
}
