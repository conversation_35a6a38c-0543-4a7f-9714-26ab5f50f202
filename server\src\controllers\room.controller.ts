// server/src/controllers/room.controller.ts
import { Request, Response } from 'express';
import { RoomService } from '../services/room.service';
import { AppError } from '../types/error.types';
import { SystemLogger } from '../services/logger.service';
import {
    RoomQueryOptions,
    CreateRoomDTO,
    UpdateRoomDTO,
    ScheduleMaintenanceDTO,
    TimeSlot
} from '../types/room.types';

export class RoomController {
    static async getRooms(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const {
            page,
            limit,
            sortBy,
            sortOrder,
            status,
            building,
            floor,
            minCapacity,
            features,
            search,
            date,
            timeStart,
            timeEnd
        } = req.query;

        const options: RoomQueryOptions = {
            page: page ? parseInt(page as string) : undefined,
            limit: limit ? parseInt(limit as string) : undefined,
            sortBy: sortBy as string,
            sortOrder: sortOrder as 'asc' | 'desc',
            status: status as any,
            building: building as string,
            floor: floor ? parseInt(floor as string) : undefined,
            minCapacity: minCapacity ? parseInt(minCapacity as string) : undefined,
            features: features ? (features as string).split(',') : undefined,
            search: search as string,
            availableAt: date && timeStart && timeEnd ? {
                date: new Date(date as string),
                timeSlot: {
                    start: timeStart as string,
                    end: timeEnd as string
                }
            } : undefined
        };

        const result = await RoomService.getRooms(options, currentUser._id.toString());

        res.json({
            success: true,
            data: result.rooms,
            pagination: result.pagination
        });
    }

    static async getRoomById(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { id } = req.params;
        const room = await RoomService.getRoomById(id, currentUser._id.toString());

        res.json({
            success: true,
            data: room
        });
    }

    static async createRoom(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const roomData: CreateRoomDTO = req.body;
        const room = await RoomService.createRoom(
            roomData,
            currentUser._id.toString()
        );

        await SystemLogger.log({
            severity: 'info',
            category: 'room_management',
            action: 'create_room',
            performedBy: currentUser._id.toString(),
            details: {
                roomId: room.id,
                roomName: room.name,
                building: room.building
            },
            status: 'success',
            timestamp: new Date()
        });

        res.status(201).json({
            success: true,
            message: 'Room created successfully',
            data: room
        });
    }

    static async updateRoom(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { id } = req.params;
        const updateData: UpdateRoomDTO = req.body;

        const room = await RoomService.updateRoom(
            id,
            updateData,
            currentUser._id.toString()
        );

        await SystemLogger.log({
            severity: 'info',
            category: 'room_management',
            action: 'update_room',
            performedBy: currentUser._id.toString(),
            targetId: id,
            details: {
                updates: updateData
            },
            status: 'success',
            timestamp: new Date()
        });

        res.json({
            success: true,
            message: 'Room updated successfully',
            data: room
        });
    }

    static async scheduleMaintenance(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { id } = req.params;
        const maintenanceData: ScheduleMaintenanceDTO = req.body;

        const room = await RoomService.scheduleMaintenance(
            id,
            maintenanceData,
            currentUser._id.toString()
        );

        await SystemLogger.log({
            severity: 'info',
            category: 'room_management',
            action: 'schedule_maintenance',
            performedBy: currentUser._id.toString(),
            targetId: id,
            details: {
                startDate: maintenanceData.startDate,
                endDate: maintenanceData.endDate,
                reason: maintenanceData.reason
            },
            status: 'success',
            timestamp: new Date()
        });

        res.json({
            success: true,
            message: 'Maintenance scheduled successfully',
            data: room
        });
    }

    static async checkAvailability(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { id } = req.params;
        const { date, timeStart, timeEnd, requiredCapacity } = req.query;

        if (!date || !timeStart || !timeEnd) {
            throw new AppError(400, 'Date and time range are required');
        }

        const timeSlot: TimeSlot = {
            start: timeStart as string,
            end: timeEnd as string
        };

        const availability = await RoomService.checkRoomAvailability(
            id,
            new Date(date as string),
            timeSlot,
            requiredCapacity ? parseInt(requiredCapacity as string) : undefined
        );

        res.json({
            success: true,
            data: availability
        });
    }

    static async findAvailableRooms(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { date, timeStart, timeEnd, minCapacity, building, floor } = req.query;

        if (!date || !timeStart || !timeEnd) {
            throw new AppError(400, 'Date and time range are required');
        }

        const timeSlot: TimeSlot = {
            start: timeStart as string,
            end: timeEnd as string
        };

        const options = {
            minCapacity: minCapacity ? parseInt(minCapacity as string) : undefined,
            building: building as string,
            floor: floor ? parseInt(floor as string) : undefined
        };

        const availableRooms = await RoomService.findAvailableRooms(
            new Date(date as string),
            timeSlot,
            options
        );

        res.json({
            success: true,
            data: availableRooms
        });
    }

    static async getRoomSchedule(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { id } = req.params;
        const { startDate, endDate } = req.query;

        if (!startDate || !endDate) {
            throw new AppError(400, 'Start and end dates are required');
        }

        const schedule = await RoomService.getRoomSchedule(
            id,
            new Date(startDate as string),
            new Date(endDate as string)
        );

        res.json({
            success: true,
            data: schedule
        });
    }

    static async getRoomUtilization(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const { id } = req.params;
        const { startDate, endDate } = req.query;

        if (!startDate || !endDate) {
            throw new AppError(400, 'Start and end dates are required');
        }

        const utilization = await RoomService.getRoomUtilization(
            id,
            new Date(startDate as string),
            new Date(endDate as string)
        );

        res.json({
            success: true,
            data: utilization
        });
    }

    static async exportRoomData(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        const {
            format = 'csv',
            startDate,
            endDate,
            includeSchedule,
            includeMaintenance,
            fields
        } = req.query;

        const exportData = await RoomService.exportRoomData({
            format: format as 'csv' | 'json',
            dateRange: startDate && endDate ? {
                start: new Date(startDate as string),
                end: new Date(endDate as string)
            } : undefined,
            includeSchedule: includeSchedule === 'true',
            includeMaintenance: includeMaintenance === 'true',
            fields: fields ? (fields as string).split(',') : undefined
        });

        const filename = `rooms_export_${new Date().toISOString()}.${format}`;
        res.setHeader('Content-Disposition', `attachment; filename=${filename}`);
        res.setHeader('Content-Type', format === 'csv' ? 'text/csv' : 'application/json');
        res.send(exportData);
    }
}