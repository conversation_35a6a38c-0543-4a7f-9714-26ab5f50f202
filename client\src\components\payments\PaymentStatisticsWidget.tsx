import { useEffect, useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { getPaymentStatistics } from "@/services/paymentService";
import { 
  PaymentStatistics, 
  PaymentMethodStatistic, 
  PaymentStatusStatistic 
} from "@/types/payment";
import { format } from "date-fns";
import { 
  ResponsiveContainer, 
  PieChart, 
  Pie, 
  Cell, 
  Tooltip, 
  Legend,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid
} from 'recharts';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8'];

const renderCustomizedLabel = ({ cx, cy, midAngle, innerRadius, outerRadius, percent, index }: any) => {
  const RADIAN = Math.PI / 180;
  const radius = innerRadius + (outerRadius - innerRadius) * 0.5;
  const x = cx + radius * Math.cos(-midAngle * RADIAN);
  const y = cy + radius * Math.sin(-midAngle * RADIAN);

  return (
    <text x={x} y={y} fill="white" textAnchor={x > cx ? 'start' : 'end'} dominantBaseline="central">
      {`${(percent * 100).toFixed(0)}%`}
    </text>
  );
};

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(amount);
};

const PaymentStatisticsWidget = () => {
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [groupBy, setGroupBy] = useState<'monthly' | 'method' | 'status'>('monthly');
  const [includeVoided, setIncludeVoided] = useState(false);
  const [methodStats, setMethodStats] = useState<any[]>([]);
  const [statusStats, setStatusStats] = useState<any[]>([]);

  const { data, isLoading, isError, refetch } = useQuery({
    queryKey: ['paymentStatistics', startDate, endDate, groupBy, includeVoided],
    queryFn: () => getPaymentStatistics(
      startDate ? format(startDate, 'yyyy-MM-dd') : undefined,
      endDate ? format(endDate, 'yyyy-MM-dd') : undefined,
      groupBy,
      includeVoided
    ),
    // refetchInterval: 60000, // Refetch every 60 seconds
  });

  const statistics: PaymentStatistics | undefined = data?.data;

  useEffect(() => {
    if (statistics) {
      setMethodStats(statistics.methodBreakdown.map(item => ({
        name: item.method,
        value: item.value
      })));
      setStatusStats(statistics.statusBreakdown.map(item => ({
        name: item.status,
        value: item.value
      })));
    }
  }, [statistics]);

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle><Skeleton className="h-6 w-40" /></CardTitle>
            <CardDescription><Skeleton className="h-4 w-64" /></CardDescription>
          </CardHeader>
          <CardContent>
            <Skeleton className="h-48" />
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle><Skeleton className="h-6 w-40" /></CardTitle>
            <CardDescription><Skeleton className="h-4 w-64" /></CardDescription>
          </CardHeader>
          <CardContent>
            <Skeleton className="h-48" />
          </CardContent>
        </Card>
        <Card className="md:col-span-2 lg:col-span-1">
          <CardHeader>
            <CardTitle><Skeleton className="h-6 w-40" /></CardTitle>
            <CardDescription><Skeleton className="h-4 w-64" /></CardDescription>
          </CardHeader>
          <CardContent>
            <Skeleton className="h-48" />
          </CardContent>
        </Card>
      </div>
    );
  }

  if (isError || !statistics) {
    return (
      <div className="text-center text-muted-foreground">
        <p>Could not load payment statistics.</p>
        <button onClick={() => refetch()} className="underline">Try Again</button>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {/* Total Amount and Count */}
      <Card>
        <CardHeader>
          <CardTitle>Total Payments</CardTitle>
          <CardDescription>Overview of all payments</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="text-2xl font-bold">{formatCurrency(statistics.totalAmount)}</div>
          <div className="text-sm text-muted-foreground">
            {statistics.totalCount} Payments
          </div>
        </CardContent>
      </Card>

      {/* Payment Method Breakdown */}
      <Card>
        <CardHeader>
          <CardTitle>Payment Methods</CardTitle>
          <CardDescription>Breakdown by payment method</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-64">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={methodStats}
                  dataKey="value"
                  nameKey="name"
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  innerRadius={40}
                  fill="#8884d8"
                  labelLine={true}
                  label={renderCustomizedLabel}
                >
                  {methodStats.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => formatCurrency(value as number)} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </CardContent>
      </Card>

      {/* Recent Payments */}
      <Card className="md:col-span-2 lg:col-span-1">
        <CardHeader>
          <CardTitle>Recent Payments</CardTitle>
          <CardDescription>Last 10 payments received</CardDescription>
        </CardHeader>
        <CardContent>
          <ul className="list-none p-0">
            {statistics.periodBreakdown.map((period, index) => (
              <li key={index} className="py-2 border-b last:border-b-0">
                <div className="flex justify-between items-center">
                  <span>{period.label}</span>
                  <span className="font-bold">{formatCurrency(period.value)}</span>
                </div>
              </li>
            ))}
          </ul>
        </CardContent>
      </Card>
    </div>
  );
};

export default PaymentStatisticsWidget;
