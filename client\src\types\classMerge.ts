
import { TeacherScheduleItem } from "./class";

export interface ClassMergeRequest {
  targetClassId: string;
  reason: string;
}

export interface TeacherAssignment {
  teacherId: string;
  schedule: TeacherScheduleItem[];
}

export interface ClassSplitRequest {
  name: string;
  level: string;
  room: string;
  capacity: number;
  teacherAssignments: TeacherAssignment[];
  studentIds: string[];
  reason: string;
}

export type ClassOperationStatus = "idle" | "loading" | "success" | "error";

export interface MergeCompatibilityResult {
  compatible: boolean;
  issues: string[];
  mergedCapacity?: {
    total: number;
    current: number;
    available: number;
  };
}

export type MergeWizardStep = "select-target" | "review-details" | "confirm";
export type SplitWizardStep = "new-class-details" | "teacher-assignment" | "student-selection" | "confirm";
