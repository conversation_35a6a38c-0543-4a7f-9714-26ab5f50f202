// server/src/services/attendance.service.ts    
import mongoose, { FilterQuery } from 'mongoose';
import { Attendance } from '../models/attendance.model';
import { Class } from '../models/class.model';
import { Student } from '../models/student.model';
import { AppError } from '../types/error.types';
import { SystemLogger } from './logger.service';
import {
    AttendanceQueryOptions,
    MarkAttendanceDTO,
    BulkMarkAttendanceDTO,
    AddExcuseDTO,
    VerifyExcuseDTO,
    AttendanceResponseDTO,
    StudentAttendanceStats,
    ClassAttendanceStats,
    AttendanceExportOptions,
    AttendanceStatus,
    ExcuseStatus,
    ConsolidatedAttendanceRecord,
    UnifiedAttendanceResponse
} from '../types/attendance.types';

export class AttendanceService {
    static async getAttendanceRecords(
        options: AttendanceQueryOptions = {},
        requestingUserId: string
    ) {
        try {
            const {
                page = 1,
                limit = 10,
                sortBy = 'date',
                sortOrder = 'desc',
                startDate,
                endDate,
                classId,
                teacherId,
                studentId,
                status,
                isMakeupClass
            } = options;

            const query: FilterQuery<typeof Attendance> = {};

            // Build query filters
            if (startDate || endDate) {
                query.date = {};
                if (startDate) query.date.$gte = new Date(startDate);
                if (endDate) query.date.$lte = new Date(endDate);
            }

            if (classId) {
                query.classId = new mongoose.Types.ObjectId(classId);
            }

            if (teacherId) {
                query.teacherId = new mongoose.Types.ObjectId(teacherId);
            }

            if (studentId) {
                query['students.studentId'] = new mongoose.Types.ObjectId(studentId);
            }

            if (status) {
                query['students.status'] = status;
            }

            if (typeof isMakeupClass === 'boolean') {
                query.isMakeupClass = isMakeupClass;
            }

            const skip = (page - 1) * limit;
            const sort: Record<string, 1 | -1> = {
                [sortBy]: sortOrder === 'asc' ? 1 : -1
            };

            const [records, total] = await Promise.all([
                Attendance.find(query)
                    .populate('classId', 'name')
                    .populate('teacherId', 'username')
                    .populate('students.studentId', 'name')
                    .sort(sort)
                    .skip(skip)
                    .limit(limit),
                Attendance.countDocuments(query)
            ]);

            await SystemLogger.log({
                severity: 'info',
                category: 'attendance',
                action: 'list_attendance',
                performedBy: requestingUserId,
                details: { filters: options },
                status: 'success',
                timestamp: new Date()
            });

            return {
                records: records.map(record => this.formatAttendanceResponse(record)),
                pagination: {
                    total,
                    page,
                    limit,
                    pages: Math.ceil(total / limit)
                }
            };
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error fetching attendance records');
        }
    }

    static async markAttendance(
        classId: string,
        date: Date,
        attendanceData: MarkAttendanceDTO,
        markedBy: string
    ): Promise<AttendanceResponseDTO> {
        try {
            // Validate class exists and teacher is assigned
            const classDoc = await Class.findById(classId);
            if (!classDoc) {
                throw new AppError(404, 'Class not found');
            }

            // Validate teacher is assigned to class
            const isTeacherAssigned = classDoc.teachers.some(
                teacher => teacher.teacherId.toString() === markedBy
            );
            if (!isTeacherAssigned) {
                throw new AppError(403, 'Teacher not authorized for this class');
            }

            // Validate student is in class
            const student = await Student.findById(attendanceData.studentId);
            if (!student || student.currentClass?.toString() !== classId) {
                throw new AppError(404, 'Student not found in class');
            }

            // Find or create attendance record for the date
            let attendanceRecord = await Attendance.findOne({
                classId: new mongoose.Types.ObjectId(classId),
                date: new Date(date.setHours(0, 0, 0, 0))
            });

            if (!attendanceRecord) {
                attendanceRecord = new Attendance({
                    classId: new mongoose.Types.ObjectId(classId),
                    date: date,
                    teacherId: new mongoose.Types.ObjectId(markedBy),
                    students: [],
                    lastModifiedBy: new mongoose.Types.ObjectId(markedBy)
                });
            }

            // Mark attendance
            await attendanceRecord.markAttendance(
                new mongoose.Types.ObjectId(attendanceData.studentId),
                attendanceData.status,
                new mongoose.Types.ObjectId(markedBy),
                {
                    arrivalTime: attendanceData.arrivalTime,
                    excuse: attendanceData.excuse ? {
                        reason: attendanceData.excuse.reason,
                        documentUrl: attendanceData.excuse.documentUrl,
                        status: 'pending' as ExcuseStatus
                    } : undefined,
                    notes: attendanceData.notes
                }
            );

            await SystemLogger.log({
                severity: 'info',
                category: 'attendance',
                action: 'mark_attendance',
                performedBy: markedBy,
                details: {
                    classId,
                    date,
                    studentId: attendanceData.studentId,
                    status: attendanceData.status
                },
                status: 'success',
                timestamp: new Date()
            });

            return this.formatAttendanceResponse(
                await attendanceRecord.populate([
                    { path: 'classId', select: 'name' },
                    { path: 'teacherId', select: 'username' },
                    { path: 'students.studentId', select: 'name' }
                ])
            );
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error marking attendance');
        }
    }

    static async bulkMarkAttendance(
        classId: string,
        date: Date,
        attendanceData: BulkMarkAttendanceDTO,
        markedBy: string
    ): Promise<AttendanceResponseDTO> {
        try {
            // Validate class and teacher
            const classDoc = await Class.findById(classId);
            if (!classDoc) {
                throw new AppError(404, 'Class not found');
            }

            const isTeacherAssigned = classDoc.teachers.some(
                teacher => teacher.teacherId.toString() === markedBy
            );
            if (!isTeacherAssigned) {
                throw new AppError(403, 'Teacher not authorized for this class');
            }

            // Find or create attendance record
            let attendanceRecord = await Attendance.findOne({
                classId: new mongoose.Types.ObjectId(classId),
                date: new Date(date.setHours(0, 0, 0, 0))
            });

            if (!attendanceRecord) {
                attendanceRecord = new Attendance({
                    classId: new mongoose.Types.ObjectId(classId),
                    date: date,
                    teacherId: new mongoose.Types.ObjectId(markedBy),
                    students: [],
                    lastModifiedBy: new mongoose.Types.ObjectId(markedBy)
                });
            }

            // Validate all students are in the class
            const studentIds = attendanceData.records.map(record => record.studentId);
            const students = await Student.find({
                _id: { $in: studentIds },
                currentClass: classId
            });

            if (students.length !== studentIds.length) {
                throw new AppError(400, 'One or more students not found in class');
            }

            // Mark attendance for all students
            await attendanceRecord.bulkMarkAttendance(
                attendanceData.records.map(record => ({
                    studentId: new mongoose.Types.ObjectId(record.studentId),
                    status: record.status,
                    arrivalTime: record.arrivalTime,
                    excuse: record.excuse ? {
                        reason: record.excuse.reason,
                        documentUrl: record.excuse.documentUrl,
                        status: 'pending' as ExcuseStatus
                    } : undefined,
                    notes: record.notes
                })),
                new mongoose.Types.ObjectId(markedBy)
            );

            await SystemLogger.log({
                severity: 'info',
                category: 'attendance',
                action: 'bulk_mark_attendance',
                performedBy: markedBy,
                details: {
                    classId,
                    date,
                    studentCount: attendanceData.records.length
                },
                status: 'success',
                timestamp: new Date()
            });

            return this.formatAttendanceResponse(
                await attendanceRecord.populate([
                    { path: 'classId', select: 'name' },
                    { path: 'teacherId', select: 'username' },
                    { path: 'students.studentId', select: 'name' }
                ])
            );
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error marking bulk attendance');
        }
    }

    static async addExcuse(
        classId: string,
        date: Date,
        excuseData: AddExcuseDTO,
        addedBy: string
    ): Promise<AttendanceResponseDTO> {
        try {
            const attendanceRecord = await Attendance.findOne({
                classId: new mongoose.Types.ObjectId(classId),
                date: new Date(date.setHours(0, 0, 0, 0))
            });

            if (!attendanceRecord) {
                throw new AppError(404, 'Attendance record not found');
            }

            await attendanceRecord.addExcuse(
                new mongoose.Types.ObjectId(excuseData.studentId),
                {
                    reason: excuseData.reason,
                    documentUrl: excuseData.documentUrl,
                    status: 'pending',
                    notes: excuseData.notes
                },
                new mongoose.Types.ObjectId(addedBy)
            );

            await SystemLogger.log({
                severity: 'info',
                category: 'attendance',
                action: 'add_excuse',
                performedBy: addedBy,
                details: {
                    classId,
                    date,
                    studentId: excuseData.studentId
                },
                status: 'success',
                timestamp: new Date()
            });

            return this.formatAttendanceResponse(
                await attendanceRecord.populate([
                    { path: 'classId', select: 'name' },
                    { path: 'teacherId', select: 'username' },
                    { path: 'students.studentId', select: 'name' }
                ])
            );
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error adding excuse');
        }
    }

    static async verifyExcuse(
        classId: string,
        date: Date,
        studentId: string,
        verificationData: VerifyExcuseDTO,
        verifiedBy: string
    ): Promise<AttendanceResponseDTO> {
        try {
            const attendanceRecord = await Attendance.findOne({
                classId: new mongoose.Types.ObjectId(classId),
                date: new Date(date.setHours(0, 0, 0, 0)),
                'students.studentId': new mongoose.Types.ObjectId(studentId)
            });

            if (!attendanceRecord) {
                throw new AppError(404, 'Attendance record not found');
            }

            const studentRecord = attendanceRecord.students.find(
                s => s.studentId.toString() === studentId
            );

            if (!studentRecord?.excuse) {
                throw new AppError(404, 'No excuse found for this attendance record');
            }

            studentRecord.excuse.status = verificationData.status;
            studentRecord.excuse.verifiedBy = new mongoose.Types.ObjectId(verifiedBy);
            studentRecord.excuse.verifiedAt = new Date();

            if (verificationData.status === 'approved') {
                studentRecord.status = 'excused';
            }

            await attendanceRecord.save();

            await SystemLogger.log({
                severity: 'info',
                category: 'attendance',
                action: 'verify_excuse',
                performedBy: verifiedBy,
                details: {
                    classId,
                    date,
                    studentId,
                    status: verificationData.status
                },
                status: 'success',
                timestamp: new Date()
            });

            return this.formatAttendanceResponse(
                await attendanceRecord.populate([
                    { path: 'classId', select: 'name' },
                    { path: 'teacherId', select: 'username' },
                    { path: 'students.studentId', select: 'name' }
                ])
            );
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error verifying excuse');
        }
    }

    static async getStudentAttendanceStats(
        studentId: string,
        startDate: Date,
        endDate: Date
    ): Promise<StudentAttendanceStats> {
        try {
            const student = await Student.findById(studentId).select('name');
            if (!student) {
                throw new AppError(404, 'Student not found');
            }

            const attendanceRecords = await Attendance.find({
                'students.studentId': new mongoose.Types.ObjectId(studentId),
                date: {
                    $gte: startDate,
                    $lte: endDate
                }
            });

            let stats = {
                studentId,
                studentName: student.name,
                totalClasses: attendanceRecords.length,
                present: 0,
                absent: 0,
                late: 0,
                excused: 0,
                attendanceRate: 0,
                averageDelay: 0,
                excusePattern: {
                    total: 0,
                    approved: 0,
                    rejected: 0,
                    pending: 0,
                    commonReasons: [] as { reason: string; count: number }[]
                }
            };

            let totalDelayMinutes = 0;
            let delayCount = 0;
            const reasons = new Map<string, number>();

            attendanceRecords.forEach(record => {
                const studentAttendance = record.students.find(
                    s => s.studentId.toString() === studentId
                );

                if (studentAttendance) {
                    stats[studentAttendance.status as keyof Pick<StudentAttendanceStats, AttendanceStatus>]++;

                    if (studentAttendance.status === 'late' && studentAttendance.arrivalTime) {
                        const scheduledTime = new Date(record.date);
                        const delayMinutes = Math.floor(
                            (studentAttendance.arrivalTime.getTime() - scheduledTime.getTime()) / 60000
                        );
                        totalDelayMinutes += delayMinutes;
                        delayCount++;
                    }

                    if (studentAttendance.excuse) {
                        stats.excusePattern[studentAttendance.excuse.status]++;
                        stats.excusePattern.total++;

                        const reason = studentAttendance.excuse.reason;
                        reasons.set(reason, (reasons.get(reason) || 0) + 1);
                    }
                }
            });

            // Calculate attendance rate and average delay
            stats.attendanceRate = stats.totalClasses > 0
                ? ((stats.present + stats.late) / stats.totalClasses) * 100
                : 0;

            stats.averageDelay = delayCount > 0
                ? totalDelayMinutes / delayCount
                : 0;

            // Get top 5 common excuse reasons
            stats.excusePattern.commonReasons = Array.from(reasons.entries())
                .sort((a, b) => b[1] - a[1])
                .slice(0, 5)
                .map(([reason, count]) => ({ reason, count }));

            return stats;
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error calculating student attendance statistics');
        }
    }

    static async getClassAttendanceStats(
        classId: string,
        startDate: Date,
        endDate: Date
    ): Promise<ClassAttendanceStats> {
        try {
            const classDoc = await Class.findById(classId).select('name');
            if (!classDoc) {
                throw new AppError(404, 'Class not found');
            }

            const attendanceRecords = await Attendance.find({
                classId: new mongoose.Types.ObjectId(classId),
                date: {
                    $gte: startDate,
                    $lte: endDate
                }
            }).populate('students.studentId', 'name');

            const stats: ClassAttendanceStats = {
                classId,
                className: classDoc.name,
                period: {
                    start: startDate,
                    end: endDate
                },
                totalDays: attendanceRecords.length,
                averageAttendanceRate: 0,
                attendanceByDate: [],
                studentStats: []
            };

            // Calculate attendance by date
            stats.attendanceByDate = attendanceRecords.map(record => {
                const dateStats = {
                    date: record.date,
                    presentCount: 0,
                    absentCount: 0,
                    lateCount: 0,
                    excusedCount: 0,
                    attendanceRate: 0
                };

                record.students.forEach(student => {
                    dateStats[`${student.status}Count` as keyof typeof dateStats]++;
                });

                const totalStudents = record.students.length;
                dateStats.attendanceRate = totalStudents > 0
                    ? ((dateStats.presentCount + dateStats.lateCount) / totalStudents) * 100
                    : 0;

                return dateStats;
            });

            // Calculate student-specific stats
            const studentMap = new Map<string, {
                id: string;
                name: string;
                records: typeof attendanceRecords[0]['students'][0][];
            }>();

            attendanceRecords.forEach(record => {
                record.students.forEach(student => {
                    const studentId = student.studentId.toString();
                    if (!studentMap.has(studentId)) {
                        studentMap.set(studentId, {
                            id: studentId,
                            name: (student.studentId as any).name,
                            records: []
                        });
                    }
                    studentMap.get(studentId)?.records.push(student);
                });
            });

            stats.studentStats = await Promise.all(
                Array.from(studentMap.values()).map(async ({ id, name, records }) => {
                    return this.calculateStudentStats(id, name, records, stats.totalDays);
                })
            );

            // Calculate class average attendance rate
            stats.averageAttendanceRate = stats.studentStats.reduce(
                (sum, student) => sum + student.attendanceRate,
                0
            ) / (stats.studentStats.length || 1);

            return stats;
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error calculating class attendance statistics');
        }
    }

    static async exportAttendance(
        options: AttendanceExportOptions,
        exportedBy: string
    ): Promise<string> {
        try {
            const query: FilterQuery<typeof Attendance> = {
                date: {
                    $gte: options.dateRange.start,
                    $lte: options.dateRange.end
                }
            };

            const records = await Attendance.find(query)
                .populate('classId', 'name')
                .populate('teacherId', 'username')
                .populate('students.studentId', 'name')
                .sort({ date: 1 });

            const exportData = this.formatExportData(records, options);

            await SystemLogger.log({
                severity: 'info',
                category: 'attendance',
                action: 'export_attendance',
                performedBy: exportedBy,
                details: {
                    options,
                    recordCount: records.length
                },
                status: 'success',
                timestamp: new Date()
            });

            if (options.format === 'json') {
                return JSON.stringify(exportData, null, 2);
            }

            // CSV format
            return this.convertToCSV(exportData, options);
        } catch (error) {
            throw error instanceof AppError ? error : new AppError(500, 'Error exporting attendance data');
        }
    }

    static async getUnifiedStudentAttendance(
        studentId: string,
        requestingUserId: string,
        startDate?: Date,
        endDate?: Date
    ): Promise<UnifiedAttendanceResponse> {
        try {
            // Verify student exists and get class history
            const student = await Student.findById(studentId).select('name classHistory currentClass');
            if (!student) {
                throw new AppError(404, 'Student not found');
            }
    
            // Set default date range if not provided
            const today = new Date();
            const defaultStartDate = new Date(today);
            defaultStartDate.setMonth(today.getMonth() - 3);
            
            const effectiveStartDate = startDate || defaultStartDate;
            const effectiveEndDate = endDate || today;
    
            // Get all class IDs the student has been enrolled in during the date range
            const relevantClassIds = [
                // Include current class if exists
                ...(student.currentClass ? [student.currentClass] : []),
                // Include past classes from history that overlap with date range
                ...student.classHistory
                    .filter(history => {
                        const startOverlap = !history.endDate || history.endDate >= effectiveStartDate;
                        const endOverlap = history.startDate <= effectiveEndDate;
                        return startOverlap && endOverlap;
                    })
                    .map(history => history.classId)
            ];
    
            // Get attendance records from all relevant classes
            const attendanceRecords = await Attendance.find({
                date: {
                    $gte: effectiveStartDate,
                    $lte: effectiveEndDate
                },
                classId: { $in: relevantClassIds },
                'students.studentId': new mongoose.Types.ObjectId(studentId)
            })
            .populate('classId', 'name')
            .sort({ date: 1 })
            .lean();
    
            // Process records into consolidated format with proper statistics
            const consolidatedRecords: ConsolidatedAttendanceRecord[] = [];
            const classSummary: Record<string, {
                classId: string;
                className: string;
                totalDays: number;
                present: number;
                absent: number;
                late: number;
                excused: number;
            }> = {};
    
            // Process each attendance record
            attendanceRecords.forEach(record => {
                const studentAttendance = record.students.find(
                    s => s.studentId.toString() === studentId
                );
    
                if (studentAttendance) {
                    // Add to consolidated records
                    consolidatedRecords.push({
                        date: record.date,
                        status: studentAttendance.status,
                        className: (record.classId as any).name || 'Unknown Class',
                        classId: record.classId.toString(),
                        arrivalTime: studentAttendance.arrivalTime,
                        excuse: studentAttendance.excuse,
                        notes: studentAttendance.notes
                    });
    
                    // Update class-specific statistics
                    const classId = record.classId.toString();
                    if (!classSummary[classId]) {
                        classSummary[classId] = {
                            classId,
                            className: (record.classId as any).name || 'Unknown Class',
                            totalDays: 0,
                            present: 0,
                            absent: 0,
                            late: 0,
                            excused: 0
                        };
                    }
                    
                    classSummary[classId].totalDays++;
                    classSummary[classId][studentAttendance.status]++;
                }
            });
    
            // Calculate overall statistics
            const statistics = {
                totalDays: consolidatedRecords.length,
                present: consolidatedRecords.filter(r => r.status === 'present').length,
                absent: consolidatedRecords.filter(r => r.status === 'absent').length,
                late: consolidatedRecords.filter(r => r.status === 'late').length,
                excused: consolidatedRecords.filter(r => r.status === 'excused').length,
                attendanceRate: 0,
                byClass: [] as Array<{
                    classId: string;
                    className: string;
                    totalDays: number;
                    present: number;
                    absent: number;
                    late: number;
                    excused: number;
                    attendanceRate: number;
                }>
            };
    
            // Calculate attendance rates
            statistics.attendanceRate = statistics.totalDays > 0
                ? ((statistics.present + statistics.late) / statistics.totalDays) * 100
                : 0;
    
            // Finalize class statistics
            statistics.byClass = Object.values(classSummary).map(cls => ({
                ...cls,
                attendanceRate: cls.totalDays > 0
                    ? ((cls.present + cls.late) / cls.totalDays) * 100
                    : 0
            }));
    
            // Log the activity
            await SystemLogger.log({
                severity: 'info',
                category: 'attendance',
                action: 'get_unified_attendance',
                performedBy: requestingUserId,
                targetId: studentId,
                details: {
                    studentId,
                    dateRange: {
                        start: effectiveStartDate,
                        end: effectiveEndDate
                    },
                    recordCount: consolidatedRecords.length,
                    classCount: Object.keys(classSummary).length
                },
                status: 'success',
                timestamp: new Date()
            });
    
            return {
                studentId,
                studentName: student.name,
                attendanceRecords: consolidatedRecords,
                statistics
            };
        } catch (error) {
            if (error instanceof AppError) throw error;
    
            if (error instanceof mongoose.Error.CastError) {
                throw new AppError(400, `Invalid format for field: ${error.path}`);
            }
    
            throw new AppError(500, `Error retrieving unified attendance: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    private static formatAttendanceResponse(record: any): AttendanceResponseDTO {
        return {
            id: record._id.toString(),
            classId: record.classId._id.toString(),
            className: record.classId.name,
            date: record.date,
            teacherId: record.teacherId._id.toString(),
            teacherName: record.teacherId.username,
            students: record.students.map((student: any) => ({
                studentId: student.studentId._id.toString(),
                studentName: student.studentId.name,
                status: student.status,
                arrivalTime: student.arrivalTime,
                excuse: student.excuse ? {
                    reason: student.excuse.reason,
                    documentUrl: student.excuse.documentUrl,
                    status: student.excuse.status,
                    verifiedBy: student.excuse.verifiedBy?.toString(),
                    verifiedAt: student.excuse.verifiedAt
                } : undefined,
                notes: student.notes
            })),
            isMakeupClass: record.isMakeupClass,
            makeupClassId: record.makeupClassId?.toString(),
            lastModified: {
                by: record.lastModifiedBy.toString(),
                at: record.modifiedAt
            }
        };
    }

    private static async calculateStudentStats(
        studentId: string,
        studentName: string,
        records: any[],
        totalDays: number
    ): Promise<StudentAttendanceStats> {
        const stats: StudentAttendanceStats = {
            studentId,
            studentName,
            totalClasses: totalDays,
            present: 0,
            absent: 0,
            late: 0,
            excused: 0,
            attendanceRate: 0,
            averageDelay: 0,
            excusePattern: {
                total: 0,
                approved: 0,
                rejected: 0,
                pending: 0,
                commonReasons: []
            }
        };

        let totalDelayMinutes = 0;
        let delayCount = 0;
        const reasons = new Map<string, number>();

        records.forEach(record => {
            stats[record.status as keyof Pick<StudentAttendanceStats, AttendanceStatus>]++;

            if (record.status === 'late' && record.arrivalTime) {
                const scheduledTime = new Date(record.date);
                const delayMinutes = Math.floor(
                    (record.arrivalTime.getTime() - scheduledTime.getTime()) / 60000
                );
                totalDelayMinutes += delayMinutes;
                delayCount++;
            }

            if (record.excuse) {
                // Use the ExcuseStatus type we already have
                const excuseStatus = record.excuse.status as ExcuseStatus;
                stats.excusePattern[excuseStatus]++;
                stats.excusePattern.total++;
                reasons.set(record.excuse.reason, (reasons.get(record.excuse.reason) || 0) + 1);
            }
        });

        stats.attendanceRate = totalDays > 0
            ? ((stats.present + stats.late) / totalDays) * 100
            : 0;

        stats.averageDelay = delayCount > 0
            ? totalDelayMinutes / delayCount
            : 0;

        stats.excusePattern.commonReasons = Array.from(reasons.entries())
            .sort((a, b) => b[1] - a[1])
            .slice(0, 5)
            .map(([reason, count]) => ({ reason, count }));

        return stats;
    }

    private static formatExportData(records: any[], options: AttendanceExportOptions): any[] {
        let formattedData: any[];

        switch (options.groupBy) {
            case 'student':
                formattedData = this.formatExportByStudent(records, options);
                break;
            case 'class':
                formattedData = this.formatExportByClass(records, options);
                break;
            default: // 'date'
                formattedData = this.formatExportByDate(records, options);
        }

        return formattedData;
    }

    private static formatExportByDate(records: any[], options: AttendanceExportOptions): any[] {
        return records.map(record => ({
            date: record.date.toISOString().split('T')[0],
            class: record.classId.name,
            teacher: record.teacherId.username,
            totalStudents: record.students.length,
            present: record.students.filter((s: any) => s.status === 'present').length,
            absent: record.students.filter((s: any) => s.status === 'absent').length,
            late: record.students.filter((s: any) => s.status === 'late').length,
            excused: record.students.filter((s: any) => s.status === 'excused').length,
            ...(options.includeStudentDetails && {
                students: record.students.map((s: any) => ({
                    name: s.studentId.name,
                    status: s.status,
                    ...(options.includeExcuses && s.excuse ? {
                        excuse: s.excuse.reason,
                        excuseStatus: s.excuse.status
                    } : {})
                }))
            })
        }));
    }

    private static formatExportByStudent(records: any[], options: AttendanceExportOptions): any[] {
        const studentMap = new Map<string, any>();

        records.forEach(record => {
            record.students.forEach((student: any) => {
                const studentId = student.studentId._id.toString();
                if (!studentMap.has(studentId)) {
                    studentMap.set(studentId, {
                        studentName: student.studentId.name,
                        present: 0,
                        absent: 0,
                        late: 0,
                        excused: 0,
                        totalClasses: 0,
                        attendance: []
                    });
                }

                const studentStats = studentMap.get(studentId);
                studentStats[student.status]++;
                studentStats.totalClasses++;

                if (options.includeStudentDetails) {
                    studentStats.attendance.push({
                        date: record.date.toISOString().split('T')[0],
                        status: student.status,
                        ...(options.includeExcuses && student.excuse ? {
                            excuse: student.excuse.reason,
                            excuseStatus: student.excuse.status
                        } : {})
                    });
                }
            });
        });

        return Array.from(studentMap.entries()).map(([id, stats]) => ({
            studentId: id,
            ...stats,
            attendanceRate: ((stats.present + stats.late) / stats.totalClasses * 100).toFixed(2) + '%'
        }));
    }

    private static formatExportByClass(records: any[], options: AttendanceExportOptions): any[] {
        const classMap = new Map<string, any>();

        records.forEach(record => {
            const classId = record.classId._id.toString();
            if (!classMap.has(classId)) {
                classMap.set(classId, {
                    className: record.classId.name,
                    totalDays: 0,
                    averageAttendance: 0,
                    attendance: []
                });
            }

            const classStats = classMap.get(classId);
            classStats.totalDays++;

            const dayStats = {
                date: record.date.toISOString().split('T')[0],
                present: record.students.filter((s: any) => s.status === 'present').length,
                absent: record.students.filter((s: any) => s.status === 'absent').length,
                late: record.students.filter((s: any) => s.status === 'late').length,
                excused: record.students.filter((s: any) => s.status === 'excused').length
            };

            classStats.attendance.push(dayStats);
            classStats.averageAttendance += (dayStats.present + dayStats.late) / record.students.length;
        });

        return Array.from(classMap.entries()).map(([id, stats]) => ({
            classId: id,
            className: stats.className,
            totalDays: stats.totalDays,
            averageAttendanceRate: (stats.averageAttendance / stats.totalDays * 100).toFixed(2) + '%',
            attendance: stats.attendance
        }));
    }

    private static convertToCSV(data: any[], options: AttendanceExportOptions): string {
        const header = Object.keys(data[0]).join(',');
        const rows = data.map(row =>
            Object.values(row).map(value =>
                typeof value === 'object' ? `"${JSON.stringify(value)}"` : `"${value}"`
            ).join(',')
        );

        return [header, ...rows].join('\n');
    }
}