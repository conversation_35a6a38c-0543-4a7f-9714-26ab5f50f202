// server/src/routes/payment.routes.ts
import express from 'express';
import { PaymentController } from '../controllers/payment.controller';
import { authenticateToken, authorizeRoles } from '../middleware/auth.middleware';
import { validate } from '../middleware/validate.middleware';
import { paymentValidation } from '../validations/payment.validation';
import { catchAsync } from '../middleware/error.middleware';
import { apiLimiter } from '../middleware/rateLimiter.middleware';
import { PaymentMiddleware } from '../middleware/payment.middleware';

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Get payments with role-based access
router.get(
    '/',
    authorizeRoles('superAdmin', 'manager', 'secretary'),
    validate(paymentValidation.getPaymentsQuery),
    catchAsync(PaymentController.getPayments)
);

// Create new payment
router.post(
    '/',
    authorizeRoles('superAdmin', 'manager', 'secretary'),
    validate(paymentValidation.createPayment),
    PaymentMiddleware.validateDuplicatePayment,
    PaymentMiddleware.validatePaymentAmount,
    catchAsync(PaymentController.createPayment)
);

// Get student payment history
router.get(
    '/students/:studentId/history',
    authorizeRoles('superAdmin', 'manager', 'secretary'),
    PaymentMiddleware.validateStudent,
    catchAsync(PaymentController.getStudentPaymentHistory)
);

// Get payment statistics
router.get(
    '/statistics',
    authorizeRoles('superAdmin', 'manager'),
    validate(paymentValidation.getPaymentStats),
    catchAsync(PaymentController.getPaymentStatistics)
);

// Export payments
router.get(
    '/export',
    authorizeRoles('superAdmin', 'manager'),
    validate(paymentValidation.exportPayments),
    catchAsync(PaymentController.exportPayments)
);

// Get specific payment
router.get(
    '/:id',
    authorizeRoles('superAdmin', 'manager', 'secretary'),
    PaymentMiddleware.validatePaymentExists,
    PaymentMiddleware.validatePaymentAccess,
    catchAsync(PaymentController.getPaymentById)
);

// Update payment
router.patch(
    '/:id',
    authorizeRoles('superAdmin', 'manager', 'secretary'),
    validate(paymentValidation.updatePayment),
    PaymentMiddleware.validatePaymentExists,
    PaymentMiddleware.validatePaymentAccess,
    PaymentMiddleware.validatePaymentModification,
    catchAsync(PaymentController.updatePayment)
);

// Void payment
router.post(
    '/:id/void',
    authorizeRoles('superAdmin', 'manager'),
    validate(paymentValidation.voidPayment),
    PaymentMiddleware.validatePaymentExists,
    PaymentMiddleware.validatePaymentAccess,
    PaymentMiddleware.validatePaymentVoid,
    async (req, res, next) => {
        console.log('Void payment route hit:', {
            paymentId: req.params.id,
            body: req.body,
            user: req.user
        });
        next();
    },
    catchAsync(PaymentController.voidPayment)
);

// Generate receipt
router.get(
    '/:id/receipt',
    authorizeRoles('superAdmin', 'manager', 'secretary'),
    PaymentMiddleware.validatePaymentExists,
    PaymentMiddleware.validatePaymentAccess,
    validate(paymentValidation.generateReceipt),
    catchAsync(PaymentController.generatePaymentReceipt)
);

// Apply rate limiting to all routes
router.use(apiLimiter);

export default router;