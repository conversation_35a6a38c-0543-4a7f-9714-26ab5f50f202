
import { useState } from "react";
import { toast } from "sonner";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  <PERSON>alogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useQuery } from "@tanstack/react-query";
import { getCurrentUser } from "@/lib/auth";

// Mock function to fetch students
const fetchStudents = async () => {
  await new Promise(resolve => setTimeout(resolve, 500));
  return [
    { id: "s1", name: "<PERSON>" },
    { id: "s2", name: "<PERSON>" },
    { id: "s3", name: "<PERSON>" },
  ];
};

// Mock function to fetch classes
const fetchClasses = async () => {
  await new Promise(resolve => setTimeout(resolve, 500));
  return [
    { id: "c1", name: "English 101" },
    { id: "c2", name: "Math 202" },
    { id: "c3", name: "History 301" },
  ];
};

// Mock function to create note
const createNote = async (noteData: any) => {
  await new Promise(resolve => setTimeout(resolve, 800));
  return { success: true, noteId: "new-note-id" };
};

interface CreateNoteDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onNoteCreated: () => void;
  initialStudentId?: string;
  initialClassId?: string;
}

const CreateNoteDialog = ({ 
  open, 
  onOpenChange, 
  onNoteCreated,
  initialStudentId,
  initialClassId
}: CreateNoteDialogProps) => {
  const [noteType, setNoteType] = useState("general");
  const [noteTitle, setNoteTitle] = useState("");
  const [noteContent, setNoteContent] = useState("");
  const [selectedStudentId, setSelectedStudentId] = useState(initialStudentId || "");
  const [selectedClassId, setSelectedClassId] = useState(initialClassId || "");
  const [visibility, setVisibility] = useState("teachers");
  const [isSubmitting, setIsSubmitting] = useState(false);
  
  const currentUser = getCurrentUser();
  
  // Reset form when dialog opens or closes
  const resetForm = () => {
    setNoteType("general");
    setNoteTitle("");
    setNoteContent("");
    setSelectedStudentId(initialStudentId || "");
    setSelectedClassId(initialClassId || "");
    setVisibility("teachers");
  };
  
  // Fetch students data
  const { data: students = [], isLoading: isLoadingStudents } = useQuery({
    queryKey: ["students"],
    queryFn: fetchStudents,
    enabled: open,
  });
  
  // Fetch classes data
  const { data: classesResponse, isLoading: isLoadingClasses } = useQuery({
    queryKey: ["classes"],
    queryFn: fetchClasses,
    enabled: open,
  });

  // Extract classes array from the response
  const classes = classesResponse?.data || [];
  
  const handleCreateNote = async () => {
    // Validate form
    if (!noteTitle.trim()) {
      toast.error("Please enter a title for the note");
      return;
    }
    
    if (!noteContent.trim()) {
      toast.error("Please enter content for the note");
      return;
    }
    
    try {
      setIsSubmitting(true);
      
      // Create note object
      const noteData = {
        title: noteTitle,
        content: noteContent,
        type: noteType,
        visibility: visibility,
        studentId: selectedStudentId || null,
        classId: selectedClassId || null,
        createdBy: currentUser ? {
          id: currentUser.id,
          name: currentUser.name,
          role: currentUser.role
        } : null
      };
      
      // Submit note
      await createNote(noteData);
      
      // Reset form and close dialog
      resetForm();
      onNoteCreated();
    } catch (error) {
      console.error("Error creating note:", error);
      toast.error("Failed to create note");
    } finally {
      setIsSubmitting(false);
    }
  };
  
  return (
    <Dialog open={open} onOpenChange={(newOpen) => {
      if (!newOpen) resetForm();
      onOpenChange(newOpen);
    }}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>Create New Note</DialogTitle>
          <DialogDescription>
            Add a new note for a student, class, or general information.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="note-type">Note Type</Label>
              <Select value={noteType} onValueChange={setNoteType}>
                <SelectTrigger id="note-type">
                  <SelectValue placeholder="Select note type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="general">General</SelectItem>
                  <SelectItem value="academic">Academic</SelectItem>
                  <SelectItem value="behavioral">Behavioral</SelectItem>
                  <SelectItem value="administrative">Administrative</SelectItem>
                  <SelectItem value="announcement">Announcement</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="visibility">Visibility</Label>
              <Select value={visibility} onValueChange={setVisibility}>
                <SelectTrigger id="visibility">
                  <SelectValue placeholder="Select visibility" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="teachers">Teachers Only</SelectItem>
                  <SelectItem value="staff">Staff Only</SelectItem>
                  <SelectItem value="all">All (Including Students)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              placeholder="Enter note title"
              value={noteTitle}
              onChange={(e) => setNoteTitle(e.target.value)}
            />
          </div>
          
          <Tabs defaultValue={initialStudentId ? "student" : initialClassId ? "class" : "student"}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="student">Student Note</TabsTrigger>
              <TabsTrigger value="class">Class Note</TabsTrigger>
            </TabsList>
            
            <TabsContent value="student" className="space-y-4 pt-4">
              <div className="space-y-2">
                <Label htmlFor="student">Student</Label>
                <Select 
                  value={selectedStudentId} 
                  onValueChange={setSelectedStudentId}
                  disabled={isLoadingStudents}
                >
                  <SelectTrigger id="student">
                    <SelectValue placeholder="Select a student" />
                  </SelectTrigger>
                  <SelectContent>
                    {isLoadingStudents ? (
                      <SelectItem value="loading" disabled>Loading students...</SelectItem>
                    ) : students.length === 0 ? (
                      <SelectItem value="none" disabled>No students available</SelectItem>
                    ) : (
                      students.map((student) => (
                        <SelectItem key={student.id} value={student.id}>
                          {student.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
            </TabsContent>
            
            <TabsContent value="class" className="space-y-4 pt-4">
              <div className="space-y-2">
                <Label htmlFor="class">Class</Label>
                <Select 
                  value={selectedClassId} 
                  onValueChange={setSelectedClassId}
                  disabled={isLoadingClasses}
                >
                  <SelectTrigger id="class">
                    <SelectValue placeholder="Select a class" />
                  </SelectTrigger>
                  <SelectContent>
                    {isLoadingClasses ? (
                      <SelectItem value="loading" disabled>Loading classes...</SelectItem>
                    ) : classes.length === 0 ? (
                      <SelectItem value="none" disabled>No classes available</SelectItem>
                    ) : (
                      classes.map((classItem) => (
                        <SelectItem key={classItem.id} value={classItem.id}>
                          {classItem.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
            </TabsContent>
          </Tabs>
          
          <div className="space-y-2">
            <Label htmlFor="content">Content</Label>
            <Textarea
              id="content"
              placeholder="Enter note content"
              className="min-h-[150px]"
              value={noteContent}
              onChange={(e) => setNoteContent(e.target.value)}
            />
          </div>
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleCreateNote} disabled={isSubmitting}>
            {isSubmitting ? "Creating..." : "Create Note"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default CreateNoteDialog;
