import { apiClient } from '@/lib/api';
import { Payment, PaymentFilters, PaymentStatistics } from '@/types/payment';

export interface PaymentListApiResponse {
  payments: Payment[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  };
}

export interface PaymentDetailsApiResponse {
  success: boolean;
  data: Payment;
}

export interface PaymentStatisticsApiResponse {
  success: boolean;
  data: PaymentStatistics;
}

export interface PaymentReceiptApiResponse {
  success: boolean;
  url: string;
}

export interface PaymentExportApiResponse {
  success: boolean;
  url: string;
}

/**
 * Fetch payments with filters and pagination
 */
export const fetchPayments = async (filters: PaymentFilters = {}): Promise<PaymentListApiResponse> => {
  try {
    const params: any = {
      page: filters.page || 1,
      limit: filters.limit || 10,
    };

    if (filters.studentId) params.studentId = filters.studentId;
    if (filters.status) params.status = filters.status;
    if (filters.method) params.method = filters.method;
    if (filters.startDate) params.startDate = filters.startDate;
    if (filters.endDate) params.endDate = filters.endDate;
    if (filters.search) params.search = filters.search;
    if (filters.sortBy) params.sortBy = filters.sortBy;
    if (filters.sortOrder) params.sortOrder = filters.sortOrder;
    if (filters.amountRange) {
      params.minAmount = filters.amountRange.min;
      params.maxAmount = filters.amountRange.max;
    }

    const response = await apiClient.get<{
      success: boolean;
      data: Payment[];
      pagination: {
        page: number;
        limit: number;
        total: number;
        totalPages: number;
      };
    }>('/payments', params);

    return {
      payments: response.data,
      pagination: response.pagination
    };
  } catch (error) {
    console.error('Error fetching payments:', error);
    throw error;
  }
};

/**
 * Fetch a payment by ID
 */
export const fetchPayment = async (paymentId: string): Promise<PaymentDetailsApiResponse> => {
  try {
    const response = await apiClient.get<{
      success: boolean;
      data: Payment;
    }>(`/payments/${paymentId}`);

    return {
      success: true,
      data: response.data
    };
  } catch (error) {
    console.error('Error fetching payment:', error);
    throw error;
  }
};

/**
 * Create a new payment
 */
export const createPayment = async (paymentData: Partial<Payment>): Promise<PaymentDetailsApiResponse> => {
  try {
    const response = await apiClient.post<{
      success: boolean;
      data: Payment;
    }>('/payments', paymentData);

    return {
      success: true,
      data: response.data
    };
  } catch (error) {
    console.error('Error creating payment:', error);
    throw error;
  }
};

/**
 * Update a payment
 */
export const updatePayment = async (paymentId: string, paymentData: Partial<Payment>): Promise<PaymentDetailsApiResponse> => {
  try {
    const response = await apiClient.put<{
      success: boolean;
      data: Payment;
    }>(`/payments/${paymentId}`, paymentData);

    return {
      success: true,
      data: response.data
    };
  } catch (error) {
    console.error('Error updating payment:', error);
    throw error;
  }
};

/**
 * Void a payment
 */
export const voidPayment = async (paymentId: string, reason: string): Promise<PaymentDetailsApiResponse> => {
  try {
    const response = await apiClient.patch<{
      success: boolean;
      data: Payment;
    }>(`/payments/${paymentId}/void`, { reason });

    return {
      success: true,
      data: response.data
    };
  } catch (error) {
    console.error('Error voiding payment:', error);
    throw error;
  }
};

/**
 * Get payment statistics
 */
export const getPaymentStatistics = async (): Promise<PaymentStatistics> => {
  try {
    const response = await apiClient.get<PaymentStatistics>('/payments/statistics');
    return response;
  } catch (error) {
    console.error('Error fetching payment statistics:', error);
    throw error;
  }
};

/**
 * Generate payment receipt
 */
export const generatePaymentReceipt = async (paymentId: string, format: 'pdf' | 'html' = 'pdf'): Promise<PaymentReceiptApiResponse> => {
  try {
    const response = await apiClient.get(`/payments/${paymentId}/receipt?format=${format}`, {
      responseType: 'blob'
    });

    // Create a blob URL for the downloaded file
    const blob = new Blob([response], {
      type: format === 'pdf' ? 'application/pdf' : 'text/html'
    });
    const url = window.URL.createObjectURL(blob);

    // Trigger download
    const link = document.createElement('a');
    link.href = url;
    link.download = `receipt_${paymentId}.${format}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    return {
      success: true,
      url
    };
  } catch (error) {
    console.error('Error generating receipt:', error);
    throw error;
  }
};

/**
 * Export payments to file
 */
export const exportPayments = async (filters: PaymentFilters = {}, format: 'csv' | 'excel' | 'pdf' = 'csv'): Promise<PaymentExportApiResponse> => {
  try {
    const params: any = { format };
    
    if (filters.studentId) params.studentId = filters.studentId;
    if (filters.status) params.status = filters.status;
    if (filters.method) params.method = filters.method;
    if (filters.startDate) params.startDate = filters.startDate;
    if (filters.endDate) params.endDate = filters.endDate;
    if (filters.search) params.search = filters.search;

    const response = await apiClient.post<{
      success: boolean;
      url: string;
    }>('/payments/export', params);

    const url = response.url;
    return {
      success: true,
      url
    };
  } catch (error) {
    console.error('Error exporting payments:', error);
    throw error;
  }
};
