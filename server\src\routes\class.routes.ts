// server/src/routes/class.routes.ts
import express from 'express';
import { ClassController } from '../controllers/class.controller';
import { ClassMiddleware } from '../middleware/class.middleware';
import { authenticateToken, authorizeRoles } from '../middleware/auth.middleware';
import { validate } from '../middleware/validate.middleware';
import { classValidation } from '../validations/class.validation';
import { catchAsync } from '../middleware/error.middleware';
import { apiLimiter } from '../middleware/rateLimiter.middleware';

const router = express.Router();

// Apply authentication to all routes
router.use(authenticateToken);

// Get classes with role-based access
router.get(
  '/',
  authorizeRoles('superAdmin', 'manager', 'secretary', 'teacher'),
  validate(classValidation.getClassesQuery),
  catchAsync(ClassController.getClasses)
);

// Create new class
router.post(
  '/',
  authorizeRoles('superAdmin', 'manager'),
  validate(classValidation.createClass),
  ClassMiddleware.validateTeacherAssignments,
  ClassMiddleware.validateRoomAvailability,
  ClassMiddleware.validateClassDates,
  catchAsync(ClassController.createClass)
);

// Export classes
router.get(
  '/export',
  authorizeRoles('superAdmin', 'manager'),
  validate(classValidation.exportClasses),
  catchAsync(ClassController.exportClasses)
);

// Get room availability
router.get(
  '/rooms/:room/availability',
  authorizeRoles('superAdmin', 'manager', 'secretary'),
  catchAsync(ClassController.getRoomAvailability)
);

// Get teacher schedule
router.get(
  '/teachers/:teacherId/schedule',
  authorizeRoles('superAdmin', 'manager', 'secretary', 'teacher'),
  catchAsync(ClassController.getTeacherSchedule)
);

// Get class statistics
router.get(
  '/statistics',
  authorizeRoles('superAdmin', 'manager'),
  catchAsync(ClassController.getClassStatistics)
);

// Bulk operations
router.post(
  '/bulk',
  authorizeRoles('superAdmin', 'manager'),
  validate(classValidation.bulkOperation),
  ClassMiddleware.validateBulkOperation,
  catchAsync(ClassController.bulkOperation)
);

// Get specific class
router.get(
  '/:id',
  authorizeRoles('superAdmin', 'manager', 'secretary', 'teacher'),
  ClassMiddleware.validateClassExists,
  catchAsync(ClassController.getClassById)
);

// Get class schedule
router.get(
  '/:id/schedule',
  authorizeRoles('superAdmin', 'manager', 'secretary', 'teacher'),
  ClassMiddleware.validateClassExists,
  catchAsync(ClassController.getClassSchedule)
);

// Update class
router.patch(
  '/:id',
  authorizeRoles('superAdmin', 'manager'),
  validate(classValidation.updateClass),
  ClassMiddleware.validateClassExists,
  ClassMiddleware.validateTeacherAssignments,
  ClassMiddleware.validateRoomAvailability,
  ClassMiddleware.validateClassDates,
  catchAsync(ClassController.updateClass)
);

// Schedule makeup class
router.post(
  '/:id/makeup',
  authorizeRoles('superAdmin', 'manager'),
  validate(classValidation.scheduleMakeupClass),
  ClassMiddleware.validateClassExists,
  ClassMiddleware.validateMakeupClass,
  catchAsync(ClassController.scheduleMakeupClass)
);

// Merge classes
router.post(
  '/:id/merge',
  authorizeRoles('superAdmin', 'manager'),
  validate(classValidation.mergeClass),
  ClassMiddleware.validateClassExists,
  ClassMiddleware.validateMergeOperation,
  catchAsync(ClassController.mergeClasses)
);

// Replace teacher
router.post(
  '/:id/replace-teacher',
  authorizeRoles('superAdmin', 'manager'),
  validate(classValidation.teacherReplacement),
  ClassMiddleware.validateClassExists,
  ClassMiddleware.validateTeacherReplacement,
  catchAsync(ClassController.replaceTeacher)
);

// Split class
router.post(
  '/:id/split',
  authorizeRoles('superAdmin', 'manager'),
  validate(classValidation.splitClass),
  ClassMiddleware.validateClassExists,
  catchAsync(ClassController.splitClass)
);

// Teacher transition
router.post(
  '/:id/teacher-transition',
  authorizeRoles('superAdmin', 'manager'),
  validate(classValidation.initiateTeacherTransition),
  ClassMiddleware.validateClassExists,
  catchAsync(ClassController.initiateTeacherTransition)
);

// Apply rate limiting to all routes
router.use(apiLimiter);

export default router;