
import { useState } from "react";
import { toast } from "sonner";
import { useQuery } from "@tanstack/react-query";
import { 
  Save, 
  Database, 
  Download, 
  Upload, 
  Server, 
  RefreshCw,
  HardDrive,
  MemoryStick,
  Cpu,
  Shield,
  AlertTriangle
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import CustomCard from "@/components/ui/CustomCard";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { getSystemSettings, updateSystemSettings, getBackupHistory, triggerBackup, restoreBackup } from "@/services/systemService";

interface SystemConfigProps {}

const SystemConfig = ({}: SystemConfigProps) => {
  const [smtpSettings, setSmtpSettings] = useState({
    server: "smtp.vertex.edu",
    port: "587",
    username: "<EMAIL>",
    password: "••••••••••••",
    sslEnabled: true,
    fromName: "Vertex Academy",
    fromEmail: "<EMAIL>"
  });
  
  const [backupSettings, setBackupSettings] = useState({
    scheduledBackups: true,
    backupTime: "03:00",
    backupFrequency: "daily",
    retentionPeriod: "30",
    includeMediaFiles: true,
    compression: "gzip",
    encrypted: true,
    storageLocation: "/var/backups/vertex"
  });

  const [notificationSettings, setNotificationSettings] = useState({
    emailEnabled: true,
    smsEnabled: false,
    adminAlerts: true,
    userNotifications: true,
    errorNotifications: true,
    loginNotifications: false
  });

  const { data: backups, isLoading: isLoadingBackups } = useQuery({
    queryKey: ["backupHistory"],
    queryFn: getBackupHistory,
  });
  
  const [isBackupInProgress, setIsBackupInProgress] = useState(false);

  const { data: stats, isLoading, refetch } = useQuery({
    queryKey: ["systemStats"],
    queryFn: getSystemSettings,
  });

  const handleSaveSmtpSettings = async () => {
    try {
      await updateSystemSettings({ smtp: smtpSettings });
      toast.success("SMTP settings saved successfully");
    } catch (error) {
      toast.error("Failed to save SMTP settings");
    }
  };

  const handleSaveBackupSettings = async () => {
    try {
      await updateSystemSettings({ backup: backupSettings });
      toast.success("Backup settings saved successfully");
    } catch (error) {
      toast.error("Failed to save backup settings");
    }
  };

  const handleSaveNotificationSettings = async () => {
    try {
      await updateSystemSettings({ notifications: notificationSettings });
      toast.success("Notification settings saved successfully");
    } catch (error) {
      toast.error("Failed to save notification settings");
    }
  };

  const handleManualBackup = async () => {
    try {
      setIsBackupInProgress(true);
      await triggerBackup();
      toast.success("Manual backup completed successfully");
      refetch();
    } catch (error) {
      toast.error("Failed to complete backup");
    } finally {
      setIsBackupInProgress(false);
    }
  };

  const handleRestoreBackup = async (backupId: string) => {
    try {
      toast.loading("Restoring system from backup...");
      await restoreBackup(backupId);
      toast.success("System restore completed successfully");
    } catch (error) {
      toast.error("Failed to restore from backup");
    }
  };

  const latestBackup = backups?.[0];

  return (
    <div className="space-y-6">
      {/* System Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <CustomCard className="p-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-medium">System Status</h3>
            <Button variant="ghost" size="icon" onClick={() => refetch()}>
              <RefreshCw className="h-4 w-4" />
            </Button>
          </div>
          <div className="mt-4 space-y-2">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Version:</span>
              <span className="font-medium">1.2.3</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Environment:</span>
              <span className="font-medium">Production</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Database:</span>
              <span className="font-medium">PostgreSQL 14</span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Last Updated:</span>
              <span className="font-medium">2023-08-15</span>
            </div>
          </div>
        </CustomCard>

        <CustomCard className="p-4">
          <h3 className="text-lg font-medium">System Load</h3>
          <div className="mt-4 space-y-4">
            <div>
              <div className="flex justify-between mb-1">
                <div className="flex items-center">
                  <Cpu className="h-4 w-4 mr-2" />
                  <span>CPU</span>
                </div>
                <span className="text-sm">32%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full" 
                  style={{ width: `32%` }}
                ></div>
              </div>
            </div>
            
            <div>
              <div className="flex justify-between mb-1">
                <div className="flex items-center">
                  <MemoryStick className="h-4 w-4 mr-2" />
                  <span>Memory</span>
                </div>
                <span className="text-sm">48%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-green-600 h-2 rounded-full" 
                  style={{ width: `48%` }}
                ></div>
              </div>
            </div>
            
            <div>
              <div className="flex justify-between mb-1">
                <div className="flex items-center">
                  <HardDrive className="h-4 w-4 mr-2" />
                  <span>Disk</span>
                </div>
                <span className="text-sm">63%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-purple-600 h-2 rounded-full" 
                  style={{ width: `63%` }}
                ></div>
              </div>
            </div>
          </div>
        </CustomCard>

        <CustomCard className="p-4">
          <h3 className="text-lg font-medium">Backup Status</h3>
          <div className="mt-4 space-y-2">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Last Backup:</span>
              <span className="font-medium">
                {latestBackup 
                  ? new Date(latestBackup.timestamp).toLocaleString() 
                  : "Never"}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Size:</span>
              <span className="font-medium">
                {latestBackup?.size || "N/A"}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-muted-foreground">Status:</span>
              <span className="font-medium capitalize">
                {latestBackup?.status || "N/A"}
              </span>
            </div>
            <div className="mt-4">
              <Button 
                className="w-full mb-2" 
                onClick={handleManualBackup}
                disabled={isBackupInProgress}
              >
                <Download className="h-4 w-4 mr-2" />
                {isBackupInProgress ? "Backup in progress..." : "Run Backup Now"}
              </Button>
              
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button 
                    variant="outline" 
                    className="w-full"
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    Restore Backup
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>System Restore</AlertDialogTitle>
                    <AlertDialogDescription>
                      This will replace all current data with the selected backup.
                      All users will be disconnected during this process.
                      This operation cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={() => latestBackup && handleRestoreBackup(latestBackup.id)}>
                      Continue
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </div>
        </CustomCard>
      </div>

      {/* SMTP Configuration */}
      <CustomCard className="p-6">
        <h3 className="text-xl font-medium mb-4">Email Configuration</h3>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="smtp-server">SMTP Server</Label>
              <Input 
                id="smtp-server" 
                value={smtpSettings.server}
                onChange={(e) => setSmtpSettings({...smtpSettings, server: e.target.value})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="smtp-port">SMTP Port</Label>
              <Input 
                id="smtp-port" 
                value={smtpSettings.port}
                onChange={(e) => setSmtpSettings({...smtpSettings, port: e.target.value})}
              />
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="smtp-username">Username</Label>
              <Input 
                id="smtp-username" 
                value={smtpSettings.username}
                onChange={(e) => setSmtpSettings({...smtpSettings, username: e.target.value})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="smtp-password">Password</Label>
              <Input 
                id="smtp-password" 
                type="password" 
                value={smtpSettings.password}
                onChange={(e) => setSmtpSettings({...smtpSettings, password: e.target.value})}
              />
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="from-name">From Name</Label>
              <Input 
                id="from-name" 
                value={smtpSettings.fromName}
                onChange={(e) => setSmtpSettings({...smtpSettings, fromName: e.target.value})}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="from-email">From Email</Label>
              <Input 
                id="from-email" 
                type="email" 
                value={smtpSettings.fromEmail}
                onChange={(e) => setSmtpSettings({...smtpSettings, fromEmail: e.target.value})}
              />
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Switch 
              id="ssl-enabled" 
              checked={smtpSettings.sslEnabled}
              onCheckedChange={(checked) => setSmtpSettings({...smtpSettings, sslEnabled: checked})}
            />
            <Label htmlFor="ssl-enabled">Enable SSL/TLS</Label>
          </div>
          
          <Alert className="bg-blue-50 border-blue-200 text-blue-800">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Make sure your SMTP server is properly configured to avoid email delivery issues.
            </AlertDescription>
          </Alert>
          
          <div className="pt-4">
            <Button onClick={handleSaveSmtpSettings}>
              <Save className="h-4 w-4 mr-2" />
              Save Email Settings
            </Button>
          </div>
        </div>
      </CustomCard>

      {/* Backup Configuration */}
      <CustomCard className="p-6">
        <h3 className="text-xl font-medium mb-4">Backup Configuration</h3>
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <Switch 
              id="scheduled-backups" 
              checked={backupSettings.scheduledBackups}
              onCheckedChange={(checked) => setBackupSettings({...backupSettings, scheduledBackups: checked})}
            />
            <Label htmlFor="scheduled-backups">Enable Scheduled Backups</Label>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="backup-time">Backup Time</Label>
              <Input 
                id="backup-time" 
                type="time" 
                value={backupSettings.backupTime}
                onChange={(e) => setBackupSettings({...backupSettings, backupTime: e.target.value})}
                disabled={!backupSettings.scheduledBackups}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="backup-frequency">Frequency</Label>
              <select
                id="backup-frequency"
                className="w-full h-10 rounded-md border border-input bg-background px-3 py-2"
                value={backupSettings.backupFrequency}
                onChange={(e) => setBackupSettings({...backupSettings, backupFrequency: e.target.value})}
                disabled={!backupSettings.scheduledBackups}
              >
                <option value="daily">Daily</option>
                <option value="weekly">Weekly</option>
                <option value="monthly">Monthly</option>
              </select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="retention-period">Retention (days)</Label>
              <Input 
                id="retention-period" 
                type="number" 
                value={backupSettings.retentionPeriod}
                onChange={(e) => setBackupSettings({...backupSettings, retentionPeriod: e.target.value})}
                disabled={!backupSettings.scheduledBackups}
              />
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="storage-location">Storage Location</Label>
            <Input 
              id="storage-location" 
              value={backupSettings.storageLocation}
              onChange={(e) => setBackupSettings({...backupSettings, storageLocation: e.target.value})}
              disabled={!backupSettings.scheduledBackups}
            />
            <p className="text-xs text-muted-foreground">
              Absolute path or cloud storage URL
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center space-x-2">
              <Switch 
                id="include-media" 
                checked={backupSettings.includeMediaFiles}
                onCheckedChange={(checked) => setBackupSettings({...backupSettings, includeMediaFiles: checked})}
                disabled={!backupSettings.scheduledBackups}
              />
              <Label htmlFor="include-media">Include Media Files (images, documents)</Label>
            </div>
            
            <div className="flex items-center space-x-2">
              <Switch 
                id="encrypted-backups" 
                checked={backupSettings.encrypted}
                onCheckedChange={(checked) => setBackupSettings({...backupSettings, encrypted: checked})}
                disabled={!backupSettings.scheduledBackups}
              />
              <Label htmlFor="encrypted-backups">Enable Encryption</Label>
            </div>
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="compression">Compression Method</Label>
            <select
              id="compression"
              className="w-full h-10 rounded-md border border-input bg-background px-3 py-2"
              value={backupSettings.compression}
              onChange={(e) => setBackupSettings({...backupSettings, compression: e.target.value})}
              disabled={!backupSettings.scheduledBackups}
            >
              <option value="none">No Compression</option>
              <option value="gzip">GZIP</option>
              <option value="zip">ZIP</option>
              <option value="bzip2">BZIP2</option>
            </select>
          </div>
          
          <Alert>
            <Shield className="h-4 w-4" />
            <AlertDescription>
              Backup encryption uses AES-256. Store the encryption key securely as data cannot be recovered without it.
            </AlertDescription>
          </Alert>
          
          <div className="pt-4">
            <Button onClick={handleSaveBackupSettings}>
              <Save className="h-4 w-4 mr-2" />
              Save Backup Settings
            </Button>
          </div>
        </div>
      </CustomCard>

      {/* Notification Settings */}
      <CustomCard className="p-6">
        <h3 className="text-xl font-medium mb-4">Notification Settings</h3>
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-3">
            <div className="flex items-center justify-between space-x-2">
              <Label htmlFor="email-notifications">Email Notifications</Label>
              <Switch 
                id="email-notifications" 
                checked={notificationSettings.emailEnabled}
                onCheckedChange={(checked) => setNotificationSettings({...notificationSettings, emailEnabled: checked})}
              />
            </div>
            
            <div className="flex items-center justify-between space-x-2">
              <Label htmlFor="sms-notifications">SMS Notifications</Label>
              <Switch 
                id="sms-notifications" 
                checked={notificationSettings.smsEnabled}
                onCheckedChange={(checked) => setNotificationSettings({...notificationSettings, smsEnabled: checked})}
              />
            </div>
            
            <div className="flex items-center justify-between space-x-2">
              <Label htmlFor="admin-alerts">Administrative Alerts</Label>
              <Switch 
                id="admin-alerts" 
                checked={notificationSettings.adminAlerts}
                onCheckedChange={(checked) => setNotificationSettings({...notificationSettings, adminAlerts: checked})}
              />
            </div>
            
            <div className="flex items-center justify-between space-x-2">
              <Label htmlFor="user-notifications">User Notifications</Label>
              <Switch 
                id="user-notifications" 
                checked={notificationSettings.userNotifications}
                onCheckedChange={(checked) => setNotificationSettings({...notificationSettings, userNotifications: checked})}
              />
            </div>
            
            <div className="flex items-center justify-between space-x-2">
              <Label htmlFor="error-notifications">Error Notifications</Label>
              <Switch 
                id="error-notifications" 
                checked={notificationSettings.errorNotifications}
                onCheckedChange={(checked) => setNotificationSettings({...notificationSettings, errorNotifications: checked})}
              />
            </div>
            
            <div className="flex items-center justify-between space-x-2">
              <Label htmlFor="login-notifications">Login Notifications</Label>
              <Switch 
                id="login-notifications" 
                checked={notificationSettings.loginNotifications}
                onCheckedChange={(checked) => setNotificationSettings({...notificationSettings, loginNotifications: checked})}
              />
            </div>
          </div>
          
          <Alert variant="destructive" className="mt-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Email notifications rely on properly configured SMTP settings above.
            </AlertDescription>
          </Alert>
          
          <div className="pt-4">
            <Button onClick={handleSaveNotificationSettings}>
              <Save className="h-4 w-4 mr-2" />
              Save Notification Settings
            </Button>
          </div>
        </div>
      </CustomCard>
    </div>
  );
};

export default SystemConfig;
