// server/src/controllers/note.controller.ts
import { Request, Response } from 'express';
import mongoose from 'mongoose';
import { NoteService } from '../services/note.service';
import { AppError } from '../types/error.types';
import { SystemLogger } from '../services/logger.service';
import {
    NoteQueryOptions,
    CreateNoteDTO,
    UpdateNoteDTO,
    NoteBulkOperationDTO,
    NoteExportOptions,
    NoteSearchOptions,
    NoteCategory,
    NoteVisibility
} from '../types/notes.types';
import { isValidNoteCategory } from '../utils/noteUtils';
import { canCreateNoteWithVisibility, validateUserRole } from '../utils/permissionUtils';
import { getDateRangeFromPeriod } from '../utils/dateUtils';

export class NoteController {
    static async getNotes(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        try {
            const {
                page,
                limit,
                sortBy,
                sortOrder,
                type,
                visibility,
                studentId,
                classId,
                tags,
                search,
                fromDate,
                toDate,
                datePeriod,
                createdBy
            } = req.query;

            // Handle date period presets if provided
            let processedFromDate = fromDate ? new Date(fromDate as string) : undefined;
            let processedToDate = toDate ? new Date(toDate as string) : undefined;

            if (datePeriod && typeof datePeriod === 'string') {
                try {
                    const dateRange = getDateRangeFromPeriod(datePeriod);
                    processedFromDate = dateRange.startDate;
                    processedToDate = dateRange.endDate;
                } catch (err) {
                    console.error("Error processing date period:", err);
                    // Continue with the original dates if period processing fails
                }
            }

            // Process and validate ObjectId parameters
            let processedStudentId: string | undefined = undefined;
            if (studentId && typeof studentId === 'string') {
                if (mongoose.Types.ObjectId.isValid(studentId)) {
                    processedStudentId = studentId;
                } else {
                    throw new AppError(400, 'Invalid student ID format');
                }
            }

            let processedClassId: string | undefined = undefined;
            if (classId && typeof classId === 'string') {
                if (mongoose.Types.ObjectId.isValid(classId)) {
                    processedClassId = classId;
                } else {
                    throw new AppError(400, 'Invalid class ID format');
                }
            }

            let processedCreatedBy: string | undefined = undefined;
            if (createdBy && typeof createdBy === 'string') {
                if (mongoose.Types.ObjectId.isValid(createdBy)) {
                    processedCreatedBy = createdBy;
                } else {
                    throw new AppError(400, 'Invalid user ID format');
                }
            }

            // Process, validate, and safely handle tags
            const processTags = (tagsParam: any): string[] | undefined => {
                if (!tagsParam || typeof tagsParam !== 'string') return undefined;
                const tagArray = tagsParam.split(',').filter(tag => tag.trim().length > 0);
                return tagArray.length > 0 ? tagArray : undefined;
            };

            // Process and validate type
            let processedType: NoteCategory | undefined = undefined;
            if (type && typeof type === 'string') {
                if (isValidNoteCategory(type)) {
                    processedType = type;
                } else {
                    throw new AppError(400, 'Invalid note type');
                }
            }

            // Process and validate visibility
            let processedVisibility: NoteVisibility | undefined = undefined;
            if (visibility && typeof visibility === 'string') {
                if (['teacher_only', 'all_staff', 'manager_only'].includes(visibility)) {
                    processedVisibility = visibility as NoteVisibility;
                } else {
                    throw new AppError(400, 'Invalid visibility value');
                }
            }

            // Safely parse numeric parameters
            const parsePage = (pageParam: any): number => {
                if (!pageParam) return 1;
                const parsed = parseInt(pageParam as string);
                return !isNaN(parsed) && parsed > 0 ? parsed : 1;
            };

            const parseLimit = (limitParam: any): number => {
                if (!limitParam) return 10;
                const parsed = parseInt(limitParam as string);
                return !isNaN(parsed) && parsed > 0 && parsed <= 100 ? parsed : 10;
            };

            const options: NoteQueryOptions = {
                page: parsePage(page),
                limit: parseLimit(limit),
                sortBy: sortBy && typeof sortBy === 'string' && ['createdAt', 'modifiedAt', 'type', 'visibility'].includes(sortBy)
                    ? (sortBy as 'createdAt' | 'modifiedAt')
                    : 'createdAt',
                sortOrder: sortOrder && typeof sortOrder === 'string' && ['asc', 'desc'].includes(sortOrder)
                    ? (sortOrder as 'asc' | 'desc')
                    : 'desc',
                type: processedType,
                visibility: processedVisibility,
                studentId: processedStudentId,
                classId: processedClassId,
                tags: processTags(tags),
                search: search && typeof search === 'string' ? (search as string).trim() : undefined,
                fromDate: processedFromDate,
                toDate: processedToDate,
                createdBy: processedCreatedBy
            };

            const result = await NoteService.getNotes(
                options,
                currentUser._id.toString(),
                currentUser.role
            );

            res.json({
                success: true,
                data: result.notes || [],
                pagination: result.pagination || {
                    total: 0,
                    page: options.page || 1,
                    limit: options.limit || 10,
                    pages: 0
                }
            });
        } catch (error) {
            if (error instanceof AppError) {
                throw error;
            }

            console.error('Error in getNotes controller:', error);
            throw new AppError(500, `Error fetching notes: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    static async getNoteById(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        try {
            const { id } = req.params;

            if (!id || typeof id !== 'string') {
                throw new AppError(400, 'Note ID is required');
            }

            if (!mongoose.Types.ObjectId.isValid(id)) {
                throw new AppError(400, 'Invalid note ID format');
            }

            const note = await NoteService.getNoteById(
                id,
                currentUser._id.toString(),
                currentUser.role
            );

            res.json({
                success: true,
                data: note
            });
        } catch (error) {
            if (error instanceof AppError) {
                throw error;
            }

            console.error('Error in getNoteById controller:', error);
            throw new AppError(500, `Error fetching note: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    static async createNote(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        try {
            // Use the permission utility to validate the role
            if (!validateUserRole(currentUser.role)) {
                throw new AppError(403, `Invalid role: ${currentUser.role}`);
            }

            // Extract and validate required fields from the request body
            const { visibility, type, content, studentId, classId, relatedTo, tags } = req.body;

            if (!visibility) {
                throw new AppError(400, 'Visibility is required');
            }

            if (!type) {
                throw new AppError(400, 'Note type is required');
            }

            if (!content || typeof content !== 'string' || content.trim().length < 3) {
                throw new AppError(400, 'Note content must be at least 3 characters long');
            }

            // Ensure at least one reference exists
            if (!studentId && !classId && !relatedTo) {
                throw new AppError(400, 'Note must be associated with at least one entity (student, class, or related entity)');
            }

            // Validate studentId if provided
            if (studentId && !mongoose.Types.ObjectId.isValid(studentId)) {
                throw new AppError(400, 'Invalid student ID format');
            }

            // Validate classId if provided
            if (classId && !mongoose.Types.ObjectId.isValid(classId)) {
                throw new AppError(400, 'Invalid class ID format');
            }

            // Validate relatedTo if provided
            if (relatedTo) {
                if (!relatedTo.type || !relatedTo.id) {
                    throw new AppError(400, 'Related entity type and ID are required');
                }

                if (!mongoose.Types.ObjectId.isValid(relatedTo.id)) {
                    throw new AppError(400, 'Invalid related entity ID format');
                }
            }

            // Use the permission utility to check if the user can create a note with this visibility
            if (!canCreateNoteWithVisibility(currentUser.role, visibility)) {
                throw new AppError(403, `Users with role ${currentUser.role} cannot create notes with visibility ${visibility}`);
            }

            // Process tags safely
            let processedTags: string[] | undefined = undefined;
            if (tags) {
                if (Array.isArray(tags)) {
                    processedTags = tags.filter(t => t && typeof t === 'string' && t.trim().length > 0);
                    if (processedTags.length > 10) {
                        throw new AppError(400, 'Maximum 10 tags allowed');
                    }
                } else {
                    throw new AppError(400, 'Tags must be an array');
                }
            }

            const noteData: CreateNoteDTO = {
                ...req.body,
                content: content.trim(),
                tags: processedTags
            };

            const note = await NoteService.createNote(
                noteData,
                currentUser._id.toString()
            );

            await SystemLogger.log({
                severity: 'info',
                category: 'notes',
                action: 'create_note',
                performedBy: currentUser._id.toString(),
                details: {
                    noteId: note.id,
                    type: note.type,
                    visibility: note.visibility,
                    relatedEntities: {
                        studentId: noteData.studentId,
                        classId: noteData.classId
                    }
                },
                status: 'success',
                timestamp: new Date()
            });

            res.status(201).json({
                success: true,
                message: 'Note created successfully',
                data: note
            });
        } catch (error) {
            if (error instanceof AppError) {
                throw error;
            }

            console.error('Error in createNote controller:', error);
            throw new AppError(500, `Error creating note: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    static async updateNote(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        try {
            const { id } = req.params;

            if (!id || typeof id !== 'string') {
                throw new AppError(400, 'Note ID is required');
            }

            if (!mongoose.Types.ObjectId.isValid(id)) {
                throw new AppError(400, 'Invalid note ID format');
            }

            // Validate request body
            const { content, visibility, tags } = req.body;

            if (!content && !visibility && !tags) {
                throw new AppError(400, 'At least one field (content, visibility, or tags) must be provided for update');
            }

            // Validate content if provided
            if (content !== undefined) {
                if (typeof content !== 'string' || content.trim().length < 3) {
                    throw new AppError(400, 'Note content must be at least 3 characters long');
                }
            }

            // Validate visibility if provided
            if (visibility !== undefined) {
                if (!['teacher_only', 'all_staff', 'manager_only'].includes(visibility)) {
                    throw new AppError(400, 'Invalid visibility value');
                }

                if (!canCreateNoteWithVisibility(currentUser.role, visibility as NoteVisibility)) {
                    throw new AppError(403, `Users with role ${currentUser.role} cannot set visibility to ${visibility}`);
                }
            }

            // Validate tags if provided
            let processedTags: string[] | undefined = undefined;
            if (tags !== undefined) {
                if (!Array.isArray(tags)) {
                    throw new AppError(400, 'Tags must be an array');
                }

                processedTags = tags.filter(t => t && typeof t === 'string' && t.trim().length > 0);

                if (processedTags.length > 10) {
                    throw new AppError(400, 'Maximum 10 tags allowed');
                }
            }

            const updateData: UpdateNoteDTO = {
                content: content ? content.trim() : undefined,
                visibility: visibility as NoteVisibility | undefined,
                tags: processedTags
            };

            const note = await NoteService.updateNote(
                id,
                updateData,
                currentUser._id.toString(),
                currentUser.role
            );

            await SystemLogger.log({
                severity: 'info',
                category: 'notes',
                action: 'update_note',
                performedBy: currentUser._id.toString(),
                targetId: id,
                details: {
                    updates: updateData
                },
                status: 'success',
                timestamp: new Date()
            });

            res.json({
                success: true,
                message: 'Note updated successfully',
                data: note
            });
        } catch (error) {
            if (error instanceof AppError) {
                throw error;
            }

            console.error('Error in updateNote controller:', error);
            throw new AppError(500, `Error updating note: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    static async deleteNote(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        try {
            const { id } = req.params;

            if (!id || typeof id !== 'string') {
                throw new AppError(400, 'Note ID is required');
            }

            if (!mongoose.Types.ObjectId.isValid(id)) {
                throw new AppError(400, 'Invalid note ID format');
            }

            await NoteService.deleteNote(
                id,
                currentUser._id.toString(),
                currentUser.role
            );

            await SystemLogger.log({
                severity: 'info',
                category: 'notes',
                action: 'delete_note',
                performedBy: currentUser._id.toString(),
                targetId: id,
                details: {
                    noteId: id
                },
                status: 'success',
                timestamp: new Date()
            });

            res.json({
                success: true,
                message: 'Note deleted successfully'
            });
        } catch (error) {
            if (error instanceof AppError) {
                throw error;
            }

            console.error('Error in deleteNote controller:', error);
            throw new AppError(500, `Error deleting note: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    static async bulkOperation(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        try {
            const { noteIds, operation, value } = req.body;

            // Validate noteIds
            if (!noteIds || !Array.isArray(noteIds) || noteIds.length === 0) {
                throw new AppError(400, 'At least one note ID must be provided');
            }

            if (noteIds.length > 50) {
                throw new AppError(400, 'Cannot process more than 50 notes at once');
            }

            // Validate each note ID
            for (const noteId of noteIds) {
                if (!mongoose.Types.ObjectId.isValid(noteId)) {
                    throw new AppError(400, `Invalid note ID format: ${noteId}`);
                }
            }

            // Validate operation
            if (!operation || !['delete', 'updateVisibility', 'updateTags'].includes(operation)) {
                throw new AppError(400, 'Invalid operation specified');
            }

            // Validate value based on operation
            if (operation === 'updateVisibility') {
                if (!value || !['teacher_only', 'all_staff', 'manager_only'].includes(value)) {
                    throw new AppError(400, 'Invalid visibility value');
                }

                if (!canCreateNoteWithVisibility(currentUser.role, value as NoteVisibility)) {
                    throw new AppError(403, `Users with role ${currentUser.role} cannot set visibility to ${value}`);
                }
            } else if (operation === 'updateTags') {
                if (!value || !Array.isArray(value)) {
                    throw new AppError(400, 'Tags must be an array');
                }

                if (value.length > 10) {
                    throw new AppError(400, 'Maximum 10 tags allowed');
                }

                // Validate each tag
                for (const tag of value) {
                    if (!tag || typeof tag !== 'string' || tag.trim().length === 0) {
                        throw new AppError(400, 'Tags must be non-empty strings');
                    }
                }
            }

            const operationData: NoteBulkOperationDTO = {
                noteIds,
                operation,
                value
            };

            const result = await NoteService.bulkOperation(
                operationData,
                currentUser._id.toString(),
                currentUser.role
            );

            await SystemLogger.log({
                severity: 'info',
                category: 'notes',
                action: 'bulk_operation',
                performedBy: currentUser._id.toString(),
                details: {
                    operation: operationData.operation,
                    noteIds: operationData.noteIds,
                    result: result,
                    performedBy: {
                        userId: currentUser._id.toString(),
                        role: currentUser.role
                    }
                },
                status: 'success',
                timestamp: new Date()
            });

            res.json({
                success: true,
                message: 'Bulk operation completed',
                data: result
            });
        } catch (error) {
            if (error instanceof AppError) {
                throw error;
            }

            console.error('Error in bulkOperation controller:', error);
            throw new AppError(500, `Error performing bulk operation: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    static async searchNotes(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        try {
            const {
                page,
                limit,
                sortBy,
                sortOrder,
                type,
                visibility,
                studentId,
                classId,
                tags,
                search,
                fromDate,
                toDate,
                datePeriod,
                searchFields,
                includeArchived,
                groupBy
            } = req.query;

            // Handle date period presets if provided
            let processedFromDate = fromDate ? new Date(fromDate as string) : undefined;
            let processedToDate = toDate ? new Date(toDate as string) : undefined;

            if (datePeriod && typeof datePeriod === 'string') {
                try {
                    const dateRange = getDateRangeFromPeriod(datePeriod);
                    processedFromDate = dateRange.startDate;
                    processedToDate = dateRange.endDate;
                } catch (err) {
                    console.error("Error processing date period:", err);
                    // Continue with original dates if period processing fails
                }
            }

            // Process ObjectId parameters
            let processedStudentId: string | undefined = undefined;
            if (studentId && typeof studentId === 'string') {
                if (mongoose.Types.ObjectId.isValid(studentId)) {
                    processedStudentId = studentId;
                } else {
                    throw new AppError(400, 'Invalid student ID format');
                }
            }

            let processedClassId: string | undefined = undefined;
            if (classId && typeof classId === 'string') {
                if (mongoose.Types.ObjectId.isValid(classId)) {
                    processedClassId = classId;
                } else {
                    throw new AppError(400, 'Invalid class ID format');
                }
            }

            // Process search fields
            const processSearchFields = (fieldsParam: any): Array<'content' | 'tags'> | undefined => {
                if (!fieldsParam) return undefined;
                if (typeof fieldsParam !== 'string') return undefined;

                const fields = fieldsParam.split(',').filter(f => ['content', 'tags'].includes(f));
                return fields.length > 0 ? fields as Array<'content' | 'tags'> : undefined;
            };

            // Process tags
            const processTags = (tagsParam: any): string[] | undefined => {
                if (!tagsParam) return undefined;
                if (typeof tagsParam !== 'string') return undefined;

                const tagArray = tagsParam.split(',').filter(tag => tag.trim().length > 0);
                return tagArray.length > 0 ? tagArray : undefined;
            };

            // Process type
            let processedType: NoteCategory | undefined = undefined;
            if (type && typeof type === 'string') {
                if (isValidNoteCategory(type)) {
                    processedType = type;
                } else {
                    throw new AppError(400, 'Invalid note type');
                }
            }

            // Process visibility
            let processedVisibility: NoteVisibility | undefined = undefined;
            if (visibility && typeof visibility === 'string') {
                if (['teacher_only', 'all_staff', 'manager_only'].includes(visibility)) {
                    processedVisibility = visibility as NoteVisibility;
                } else {
                    throw new AppError(400, 'Invalid visibility value');
                }
            }

            // Process group by parameter
            let processedGroupBy: 'student' | 'class' | 'type' | 'date' | undefined = undefined;
            if (groupBy && typeof groupBy === 'string') {
                if (['student', 'class', 'type', 'date'].includes(groupBy)) {
                    processedGroupBy = groupBy as 'student' | 'class' | 'type' | 'date';
                } else {
                    throw new AppError(400, 'Invalid groupBy parameter');
                }
            }

            // Safely parse page and limit
            const parsePage = (pageParam: any): number => {
                if (!pageParam) return 1;
                const parsed = parseInt(pageParam as string);
                return !isNaN(parsed) && parsed > 0 ? parsed : 1;
            };

            const parseLimit = (limitParam: any): number => {
                if (!limitParam) return 10;
                const parsed = parseInt(limitParam as string);
                return !isNaN(parsed) && parsed > 0 && parsed <= 100 ? parsed : 10;
            };

            const searchOptions: NoteSearchOptions = {
                page: parsePage(page),
                limit: parseLimit(limit),
                sortBy: sortBy && typeof sortBy === 'string' && ['createdAt', 'modifiedAt'].includes(sortBy)
                    ? (sortBy as 'createdAt' | 'modifiedAt')
                    : 'createdAt',
                sortOrder: sortOrder && typeof sortOrder === 'string' && ['asc', 'desc'].includes(sortOrder)
                    ? (sortOrder as 'asc' | 'desc')
                    : 'desc',
                type: processedType,
                visibility: processedVisibility,
                studentId: processedStudentId,
                classId: processedClassId,
                tags: processTags(tags),
                search: search && typeof search === 'string' ? search.trim() : undefined,
                searchFields: processSearchFields(searchFields),
                fromDate: processedFromDate,
                toDate: processedToDate,
                includeArchived: includeArchived === 'true',
                groupBy: processedGroupBy
            };

            const result = await NoteService.searchNotes(
                searchOptions,
                currentUser._id.toString(),
                currentUser.role
            );

            res.json({
                success: true,
                data: result.notes || [],
                pagination: result.pagination || {
                    total: 0,
                    page: searchOptions.page || 1,
                    limit: searchOptions.limit || 10,
                    pages: 0
                }
            });
        } catch (error) {
            if (error instanceof AppError) {
                throw error;
            }

            console.error('Error in searchNotes controller:', error);
            throw new AppError(500, `Error searching notes: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    static async exportNotes(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        try {
            const {
                format = 'csv',
                fields,
                includeHistory,
                fromDate,
                toDate,
                datePeriod,
                groupBy
            } = req.query;

            // Validate format
            if (format && typeof format === 'string' && format !== 'csv' && format !== 'json') {
                throw new AppError(400, 'Export format must be either csv or json');
            }

            // Handle date period presets if provided
            let processedFromDate = fromDate ? new Date(fromDate as string) : undefined;
            let processedToDate = toDate ? new Date(toDate as string) : undefined;

            if (datePeriod && typeof datePeriod === 'string') {
                try {
                    const dateRange = getDateRangeFromPeriod(datePeriod);
                    processedFromDate = dateRange.startDate;
                    processedToDate = dateRange.endDate;
                } catch (err) {
                    console.error("Error processing date period:", err);
                    // Continue with original dates if period processing fails
                }
            }

            // Process fields
            const processFields = (fieldsParam: any): string[] | undefined => {
                if (!fieldsParam || typeof fieldsParam !== 'string') return undefined;

                return fieldsParam.split(',').filter(f => f.trim().length > 0);
            };

            // Process groupBy parameter
            let processedGroupBy: 'student' | 'class' | 'type' | 'date' | undefined = undefined;
            if (groupBy && typeof groupBy === 'string') {
                if (['student', 'class', 'type', 'date'].includes(groupBy)) {
                    processedGroupBy = groupBy as 'student' | 'class' | 'type' | 'date';
                } else {
                    throw new AppError(400, 'Invalid groupBy parameter');
                }
            }

            const options: NoteExportOptions = {
                format: (format || 'csv') as 'csv' | 'json',
                fields: processFields(fields),
                includeHistory: includeHistory === 'true',
                dateRange: (processedFromDate && processedToDate) ? {
                    start: processedFromDate,
                    end: processedToDate
                } : undefined,
                groupBy: processedGroupBy
            };

            const exportData = await NoteService.exportNotes(
                options,
                currentUser._id.toString(),
                currentUser.role
            );

            await SystemLogger.log({
                severity: 'info',
                category: 'notes',
                action: 'export_notes',
                performedBy: currentUser._id.toString(),
                details: {
                    format: options.format,
                    fields: options.fields,
                    includeHistory: options.includeHistory,
                    dateRange: options.dateRange,
                    groupBy: options.groupBy,
                    exportedBy: {
                        userId: currentUser._id.toString(),
                        role: currentUser.role
                    }
                },
                status: 'success',
                timestamp: new Date()
            });

            const filename = `notes_export_${new Date().toISOString().replace(/[:.-]/g, '_')}.${options.format}`;
            res.setHeader('Content-Disposition', `attachment; filename=${filename}`);
            res.setHeader('Content-Type', options.format === 'csv' ? 'text/csv' : 'application/json');
            res.send(exportData);
        } catch (error) {
            if (error instanceof AppError) {
                throw error;
            }

            console.error('Error in exportNotes controller:', error);
            throw new AppError(500, `Error exporting notes: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    static async getStudentNotes(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        try {
            const { studentId } = req.params;

            if (!studentId || !mongoose.Types.ObjectId.isValid(studentId)) {
                throw new AppError(400, 'Invalid student ID format');
            }

            const {
                page = '1',
                limit = '10',
                type,
                fromDate,
                toDate,
                datePeriod
            } = req.query;

            // Handle date period presets if provided
            let processedFromDate = fromDate ? new Date(fromDate as string) : undefined;
            let processedToDate = toDate ? new Date(toDate as string) : undefined;

            if (datePeriod && typeof datePeriod === 'string') {
                try {
                    const dateRange = getDateRangeFromPeriod(datePeriod);
                    processedFromDate = dateRange.startDate;
                    processedToDate = dateRange.endDate;
                } catch (err) {
                    console.error("Error processing date period:", err);
                    // Continue with original dates if period processing fails
                }
            }

            // Process type safely
            const typeParam = type as string | undefined;
            let processedType: NoteCategory | undefined = undefined;

            if (typeParam) {
                if (isValidNoteCategory(typeParam)) {
                    processedType = typeParam;
                } else {
                    throw new AppError(400, 'Invalid note type');
                }
            }

            // Safely parse page and limit
            const parsePage = (pageParam: any): number => {
                if (!pageParam) return 1;
                const parsed = parseInt(pageParam as string);
                return !isNaN(parsed) && parsed > 0 ? parsed : 1;
            };

            const parseLimit = (limitParam: any): number => {
                if (!limitParam) return 10;
                const parsed = parseInt(limitParam as string);
                return !isNaN(parsed) && parsed > 0 && parsed <= 100 ? parsed : 10;
            };

            const result = await NoteService.getStudentNotes(
                studentId,
                {
                    page: parsePage(page),
                    limit: parseLimit(limit),
                    type: processedType,
                    fromDate: processedFromDate,
                    toDate: processedToDate
                },
                currentUser._id.toString(),
                currentUser.role
            );

            res.json({
                success: true,
                data: result.notes || [],
                pagination: result.pagination || {
                    total: 0,
                    page: parsePage(page),
                    limit: parseLimit(limit),
                    pages: 0
                }
            });
        } catch (error) {
            if (error instanceof AppError) {
                throw error;
            }

            console.error('Error in getStudentNotes controller:', error);
            throw new AppError(500, `Error getting student notes: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    static async getClassNotes(req: Request, res: Response) {
        const currentUser = req.user;
        if (!currentUser?._id) {
            throw new AppError(401, 'User not authenticated');
        }

        try {
            const { classId } = req.params;

            if (!classId || !mongoose.Types.ObjectId.isValid(classId)) {
                throw new AppError(400, 'Invalid class ID format');
            }

            const {
                page = '1',
                limit = '10',
                type,
                fromDate,
                toDate,
                datePeriod
            } = req.query;

            // Handle date period presets if provided
            let processedFromDate = fromDate ? new Date(fromDate as string) : undefined;
            let processedToDate = toDate ? new Date(toDate as string) : undefined;

            if (datePeriod && typeof datePeriod === 'string') {
                try {
                    const dateRange = getDateRangeFromPeriod(datePeriod);
                    processedFromDate = dateRange.startDate;
                    processedToDate = dateRange.endDate;
                } catch (err) {
                    console.error("Error processing date period:", err);
                    // Continue with original dates if period processing fails
                }
            }

            // Process type safely
            const typeParam = type as string | undefined;
            let processedType: NoteCategory | undefined = undefined;

            if (typeParam) {
                if (isValidNoteCategory(typeParam)) {
                    processedType = typeParam;
                } else {
                    throw new AppError(400, 'Invalid note type');
                }
            }

            // Safely parse page and limit
            const parsePage = (pageParam: any): number => {
                if (!pageParam) return 1;
                const parsed = parseInt(pageParam as string);
                return !isNaN(parsed) && parsed > 0 ? parsed : 1;
            };

            const parseLimit = (limitParam: any): number => {
                if (!limitParam) return 10;
                const parsed = parseInt(limitParam as string);
                return !isNaN(parsed) && parsed > 0 && parsed <= 100 ? parsed : 10;
            };

            const result = await NoteService.getClassNotes(
                classId,
                {
                    page: parsePage(page),
                    limit: parseLimit(limit),
                    type: processedType,
                    fromDate: processedFromDate,
                    toDate: processedToDate
                },
                currentUser._id.toString(),
                currentUser.role
            );

            res.json({
                success: true,
                data: result.notes || [],
                pagination: result.pagination || {
                    total: 0,
                    page: parsePage(page),
                    limit: parseLimit(limit),
                    pages: 0
                }
            });
        } catch (error) {
            if (error instanceof AppError) {
                throw error;
            }

            console.error('Error in getClassNotes controller:', error);
            throw new AppError(500, `Error getting class notes: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
}