// server/src/services/schedule.service.ts
import { Class } from '../models/class.model';
import { Room } from '../models/room.model';
import { User } from '../models/user.model';
import { AppError } from '../types/error.types';
// Removed date-fns dependency - using native Date methods

export interface ScheduleItem {
    id: string;
    className: string;
    classLevel: string;
    timeStart: string;
    timeEnd: string;
    room: string;
    roomId: string;
    teacher: string;
    teacherId: string;
    date: string;
    status: string;
}

export interface ScheduleFilters {
    teacherId?: string;
    roomId?: string;
    classLevel?: string;
}

export interface DateRangeOptions {
    startDate?: Date;
    endDate?: Date;
    includeMakeup?: boolean;
}

export class ScheduleService {
    static async getDailySchedule(
        date: Date,
        filters: ScheduleFilters,
        userId: string
    ): Promise<ScheduleItem[]> {
        try {
            const startOfDay = new Date(date);
            startOfDay.setHours(0, 0, 0, 0);
            
            const endOfDay = new Date(date);
            endOfDay.setHours(23, 59, 59, 999);

            // Build query
            const query: any = {
                startDate: { $lte: endOfDay },
                endDate: { $gte: startOfDay },
                status: { $in: ['active', 'scheduled'] }
            };

            // Apply filters
            if (filters.teacherId) {
                query.teacherId = filters.teacherId;
            }
            if (filters.roomId) {
                query.roomId = filters.roomId;
            }
            if (filters.classLevel) {
                query.level = filters.classLevel;
            }

            const classes = await Class.find(query)
                .populate('teacherId', 'firstName lastName')
                .populate('roomId', 'name')
                .sort({ startTime: 1 });

            return this.formatScheduleItems(classes, date);
        } catch (error) {
            console.error('Error fetching daily schedule:', error);
            throw new AppError(500, 'Failed to fetch daily schedule');
        }
    }

    static async getWeeklySchedule(
        startDate: Date,
        filters: ScheduleFilters,
        userId: string
    ): Promise<ScheduleItem[]> {
        try {
            // Calculate week start (Sunday) and end (Saturday)
            const weekStart = new Date(startDate);
            weekStart.setDate(startDate.getDate() - startDate.getDay());

            const weekEnd = new Date(weekStart);
            weekEnd.setDate(weekStart.getDate() + 6);

            // Build query
            const query: any = {
                startDate: { $lte: weekEnd },
                endDate: { $gte: weekStart },
                status: { $in: ['active', 'scheduled'] }
            };

            // Apply filters
            if (filters.teacherId) {
                query.teacherId = filters.teacherId;
            }
            if (filters.roomId) {
                query.roomId = filters.roomId;
            }
            if (filters.classLevel) {
                query.level = filters.classLevel;
            }

            const classes = await Class.find(query)
                .populate('teacherId', 'firstName lastName')
                .populate('roomId', 'name')
                .sort({ startDate: 1, startTime: 1 });

            return this.formatScheduleItems(classes);
        } catch (error) {
            console.error('Error fetching weekly schedule:', error);
            throw new AppError(500, 'Failed to fetch weekly schedule');
        }
    }

    static async getScheduleByRange(
        startDate: Date,
        endDate: Date,
        filters: ScheduleFilters,
        userId: string
    ): Promise<ScheduleItem[]> {
        try {
            // Build query
            const query: any = {
                startDate: { $lte: endDate },
                endDate: { $gte: startDate },
                status: { $in: ['active', 'scheduled'] }
            };

            // Apply filters
            if (filters.teacherId) {
                query.teacherId = filters.teacherId;
            }
            if (filters.roomId) {
                query.roomId = filters.roomId;
            }
            if (filters.classLevel) {
                query.level = filters.classLevel;
            }

            const classes = await Class.find(query)
                .populate('teacherId', 'firstName lastName')
                .populate('roomId', 'name')
                .sort({ startDate: 1, startTime: 1 });

            return this.formatScheduleItems(classes);
        } catch (error) {
            console.error('Error fetching schedule by range:', error);
            throw new AppError(500, 'Failed to fetch schedule');
        }
    }

    static async getTeacherSchedule(
        teacherId: string,
        options: DateRangeOptions,
        userId: string
    ): Promise<ScheduleItem[]> {
        try {
            const query: any = {
                teacherId,
                status: { $in: ['active', 'scheduled'] }
            };

            if (options.startDate && options.endDate) {
                query.startDate = { $lte: options.endDate };
                query.endDate = { $gte: options.startDate };
            }

            const classes = await Class.find(query)
                .populate('teacherId', 'firstName lastName')
                .populate('roomId', 'name')
                .sort({ startDate: 1, startTime: 1 });

            return this.formatScheduleItems(classes);
        } catch (error) {
            console.error('Error fetching teacher schedule:', error);
            throw new AppError(500, 'Failed to fetch teacher schedule');
        }
    }

    static async getRoomSchedule(
        roomId: string,
        options: DateRangeOptions,
        userId: string
    ): Promise<ScheduleItem[]> {
        try {
            const query: any = {
                roomId,
                status: { $in: ['active', 'scheduled'] }
            };

            if (options.startDate && options.endDate) {
                query.startDate = { $lte: options.endDate };
                query.endDate = { $gte: options.startDate };
            }

            const classes = await Class.find(query)
                .populate('teacherId', 'firstName lastName')
                .populate('roomId', 'name')
                .sort({ startDate: 1, startTime: 1 });

            return this.formatScheduleItems(classes);
        } catch (error) {
            console.error('Error fetching room schedule:', error);
            throw new AppError(500, 'Failed to fetch room schedule');
        }
    }

    static async getClassSchedule(
        classId: string,
        options: DateRangeOptions,
        userId: string
    ): Promise<ScheduleItem[]> {
        try {
            const classDoc = await Class.findById(classId)
                .populate('teacherId', 'firstName lastName')
                .populate('roomId', 'name');

            if (!classDoc) {
                throw new AppError(404, 'Class not found');
            }

            return this.formatScheduleItems([classDoc]);
        } catch (error) {
            console.error('Error fetching class schedule:', error);
            throw new AppError(500, 'Failed to fetch class schedule');
        }
    }

    private static formatScheduleItems(classes: any[], specificDate?: Date): ScheduleItem[] {
        const scheduleItems: ScheduleItem[] = [];

        for (const classDoc of classes) {
            // Generate schedule items based on class schedule
            const startDate = specificDate || classDoc.startDate;
            const endDate = classDoc.endDate;
            
            let currentDate = new Date(startDate);
            
            while (currentDate <= endDate) {
                // Check if this day is in the class schedule
                const dayOfWeek = currentDate.getDay();
                const dayName = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'][dayOfWeek];
                
                if (classDoc.schedule && classDoc.schedule[dayName]) {
                    const daySchedule = classDoc.schedule[dayName];
                    
                    scheduleItems.push({
                        id: `${classDoc._id}_${currentDate.toISOString().split('T')[0]}`,
                        className: classDoc.name,
                        classLevel: classDoc.level,
                        timeStart: daySchedule.startTime || '09:00',
                        timeEnd: daySchedule.endTime || '10:30',
                        room: classDoc.roomId?.name || 'TBD',
                        roomId: classDoc.roomId?._id?.toString() || '',
                        teacher: classDoc.teacherId ? `${classDoc.teacherId.firstName} ${classDoc.teacherId.lastName}` : 'TBD',
                        teacherId: classDoc.teacherId?._id?.toString() || '',
                        date: currentDate.toISOString().split('T')[0],
                        status: classDoc.status
                    });
                }
                
                if (specificDate) break; // For daily schedule, only process the specific date
                currentDate.setDate(currentDate.getDate() + 1);
            }
        }

        return scheduleItems.sort((a, b) => {
            const dateCompare = a.date.localeCompare(b.date);
            if (dateCompare !== 0) return dateCompare;
            return a.timeStart.localeCompare(b.timeStart);
        });
    }
}
