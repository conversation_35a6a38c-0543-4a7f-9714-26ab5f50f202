// server/src/models/teacher.report-template.model.ts
import mongoose, { Schema, Document, Model } from 'mongoose';
import { TeacherReportType, ReportTemplateConfig } from '../types/teacher.report.types';
// import { AppError } from '../types/error.types';

// Document interface
export interface IReportTemplateDocument extends Document, ReportTemplateConfig {
    _id: mongoose.Types.ObjectId;
    createdBy: mongoose.Types.ObjectId;
    lastUsed?: Date;
    usageCount: number;
    isDefault: boolean;
    validateTemplate(): Promise<boolean>;
    isAccessibleBy(userId: mongoose.Types.ObjectId, userRole: string): boolean;
}

// Model interface
interface IReportTemplateModel extends Model<IReportTemplateDocument> {
    findByTypeAndUser(type: TeacherReportType, userId: mongoose.Types.ObjectId): Promise<IReportTemplateDocument[]>;
    getDefaultTemplate(type: TeacherReportType): Promise<IReportTemplateDocument>;
}

// Schema definition
const reportTemplateSchema = new Schema<IReportTemplateDocument>({
    name: {
        type: String,
        required: true,
        trim: true,
        index: true
    },
    type: {
        type: String,
        required: true,
        enum: ['class_attendance', 'student_progress', 'class_behavior', 'makeup_classes'],
        index: true
    },
    createdBy: {
        type: Schema.Types.ObjectId,
        ref: 'User',
        required: true,
        index: true
    },
    isDefault: {
        type: Boolean,
        default: false,
        index: true
    },
    config: {
        header: {
            logo: {
                type: Boolean,
                default: true
            },
            title: {
                type: String,
                required: true
            },
            subtitle: String,
            date: {
                type: Boolean,
                default: true
            }
        },
        content: {
            sections: [{
                type: {
                    type: String,
                    enum: ['table', 'chart', 'summary', 'notes'],
                    required: true
                },
                title: String,
                data: {
                    type: [String],
                    required: true
                }
            }]
        },
        footer: {
            pageNumbers: {
                type: Boolean,
                default: true
            },
            signature: Boolean,
            notes: String
        }
    },
    lastUsed: {
        type: Date,
        sparse: true
    },
    usageCount: {
        type: Number,
        default: 0,
        min: 0
    }
}, {
    timestamps: true
});

// Indexes
reportTemplateSchema.index({ type: 1, createdBy: 1 });
reportTemplateSchema.index({ type: 1, isDefault: 1 });
reportTemplateSchema.index({ createdBy: 1, lastUsed: -1 });

// Instance methods
reportTemplateSchema.methods.validateTemplate = async function(
    this: IReportTemplateDocument
): Promise<boolean> {
    const requiredSections: Record<TeacherReportType, Array<'table' | 'chart' | 'summary' | 'notes'>> = {
        class_attendance: ['summary', 'table'],
        student_progress: ['summary', 'chart'],
        class_behavior: ['summary', 'notes'],
        makeup_classes: ['summary', 'table']
    };

    const sectionTypes = this.config.content.sections.map((section: { 
        type: 'table' | 'chart' | 'summary' | 'notes' 
    }) => section.type);

    // Check if this.type is a valid TeacherReportType
    if (!Object.keys(requiredSections).includes(this.type)) {
        return false;
    }

    const required = requiredSections[this.type as TeacherReportType];
    
    return required.every((type: 'table' | 'chart' | 'summary' | 'notes') => 
        sectionTypes.includes(type)
    );
};

reportTemplateSchema.methods.isAccessibleBy = function(
    userId: mongoose.Types.ObjectId,
    userRole: string
): boolean {
    if (this.createdBy.equals(userId)) return true;
    if (userRole === 'superAdmin') return true;
    if (this.isDefault) return true;
    return false;
};

// Static methods
reportTemplateSchema.static('findByTypeAndUser', async function(
    type: TeacherReportType,
    userId: mongoose.Types.ObjectId
): Promise<IReportTemplateDocument[]> {
    return this.find({
        $or: [
            { type, createdBy: userId },
            { type, isDefault: true }
        ]
    }).sort({ isDefault: -1, lastUsed: -1 });
});

reportTemplateSchema.static('getDefaultTemplate', async function(
    type: TeacherReportType
): Promise<IReportTemplateDocument> {
    const defaultTemplate = await this.findOne({ type, isDefault: true });
    if (!defaultTemplate) {
        return this.create(getDefaultTemplateConfig(type));
    }
    return defaultTemplate;
});

// Middleware
reportTemplateSchema.pre('save', async function(this: IReportTemplateDocument, next) {
    if (this.isDefault && this.isModified('isDefault')) {
        const model = mongoose.model('ReportTemplate') as IReportTemplateModel;
        await model.updateMany(
            { type: this.type, _id: { $ne: this._id } },
            { $set: { isDefault: false } }
        );
    }

    if (this.isModified('config')) {
        const isValid = await this.validateTemplate();
        if (!isValid) {
            next(new Error('Invalid template configuration'));
            return;
        }
    }

    next();
});

// Helper function to get default template configuration
function getDefaultTemplateConfig(type: TeacherReportType): Partial<IReportTemplateDocument> {
    const baseConfig = {
        name: `Default ${type.replace('_', ' ')} Template`,
        type,
        isDefault: true,
        config: {
            header: {
                logo: true,
                title: `${type.replace('_', ' ')} Report`,
                date: true
            },
            content: {
                sections: getDefaultSections(type)
            },
            footer: {
                pageNumbers: true,
                signature: false
            }
        }
    };

    return baseConfig;
}

function getDefaultSections(type: TeacherReportType) {
    const sectionConfigs: Record<TeacherReportType, Array<{
        type: 'table' | 'chart' | 'summary' | 'notes';
        title: string;
        data: string[];
    }>> = {
        class_attendance: [
            { type: 'summary', title: 'Attendance Overview', data: ['attendance_stats'] },
            { type: 'table', title: 'Detailed Attendance', data: ['attendance_records'] }
        ],
        student_progress: [
            { type: 'summary', title: 'Progress Overview', data: ['progress_summary'] },
            { type: 'chart', title: 'Progress Trend', data: ['progress_data'] }
        ],
        class_behavior: [
            { type: 'summary', title: 'Behavior Overview', data: ['behavior_stats'] },
            { type: 'notes', title: 'Behavior Notes', data: ['behavior_notes'] }
        ],
        makeup_classes: [
            { type: 'summary', title: 'Makeup Classes Summary', data: ['makeup_stats'] },
            { type: 'table', title: 'Makeup Class Schedule', data: ['makeup_records'] }
        ]
    };

    return sectionConfigs[type];
}

export const ReportTemplate = mongoose.model<IReportTemplateDocument, IReportTemplateModel>(
    'ReportTemplate',
    reportTemplateSchema
);