
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Room } from "@/types/room";
import { findAvailableRooms } from "@/services/roomService";
import { toast } from "sonner";

const AvailabilitySearch = () => {
  const [date, setDate] = useState<string>("");
  const [timeStart, setTimeStart] = useState<string>("");
  const [timeEnd, setTimeEnd] = useState<string>("");
  const [capacity, setCapacity] = useState<string>("");
  const [type, setType] = useState<string>("");
  const [results, setResults] = useState<Room[]>([]);
  const [loading, setLoading] = useState(false);

  const handleSearch = async () => {
    if (!date || !timeStart || !timeEnd) {
      toast.error("Please fill in all required fields");
      return;
    }

    setLoading(true);
    try {
      const filters: any = {};
      if (capacity) {
        filters.minCapacity = parseInt(capacity);
      }

      const response = await findAvailableRooms(date, timeStart, timeEnd, filters);
      setResults(response.data);

      if (response.data.length === 0) {
        toast.info("No available rooms found for the selected time slot");
      }
    } catch (error) {
      console.error("Error searching for available rooms:", error);
      toast.error("Failed to search for available rooms");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Find Available Rooms</CardTitle>
        <CardDescription>
          Search for available rooms based on date, time, and requirements
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div className="space-y-2">
            <Label htmlFor="date">Date</Label>
            <Input
              id="date"
              type="date"
              value={date}
              onChange={(e) => setDate(e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="timeStart">Start Time</Label>
            <Input
              id="timeStart"
              type="time"
              value={timeStart}
              onChange={(e) => setTimeStart(e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="timeEnd">End Time</Label>
            <Input
              id="timeEnd"
              type="time"
              value={timeEnd}
              onChange={(e) => setTimeEnd(e.target.value)}
            />
          </div>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div className="space-y-2">
            <Label htmlFor="capacity">Minimum Capacity</Label>
            <Input
              id="capacity"
              type="number"
              min="1"
              placeholder="Enter minimum capacity"
              value={capacity}
              onChange={(e) => setCapacity(e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="roomType">Room Type</Label>
            <Select value={type} onValueChange={setType}>
              <SelectTrigger id="roomType">
                <SelectValue placeholder="Any room type" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectItem value="any">Any room type</SelectItem>
                  <SelectItem value="classroom">Classroom</SelectItem>
                  <SelectItem value="lecture">Lecture Hall</SelectItem>
                  <SelectItem value="lab">Laboratory</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        <Button onClick={handleSearch} className="w-full" disabled={loading}>
          {loading ? "Searching..." : "Search Available Rooms"}
        </Button>

        {results.length > 0 && (
          <div className="mt-6">
            <h3 className="text-lg font-medium mb-3">Available Rooms ({results.length})</h3>
            <div className="space-y-3">
              {results.map((room) => (
                <div
                  key={room.id}
                  className="p-3 border rounded-md border-green-500 bg-green-50"
                >
                  <div className="flex justify-between items-center">
                    <div>
                      <span className="font-medium">{room.name}</span>
                      <p className="text-sm text-muted-foreground mt-1">
                        {room.building}, {room.floor === 0 ? 'Ground Floor' : `Floor ${room.floor}`} • Capacity: {room.capacity}
                      </p>
                      {room.features.length > 0 && (
                        <p className="text-xs text-muted-foreground mt-1">
                          Features: {room.features.slice(0, 3).join(", ")}
                          {room.features.length > 3 && ` +${room.features.length - 3} more`}
                        </p>
                      )}
                    </div>
                    <span className="inline-block px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
                      Available
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default AvailabilitySearch;
