
import React, { useState, useEffect } from "react";
import { format, addDays, startOfWeek } from "date-fns";
import {
  Card,
  CardContent,
} from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { UserRole } from "@/types";
import { getWeeklySchedule, ScheduleItem } from "@/services/scheduleService";
import { toast } from "sonner";

interface WeeklyScheduleViewProps {
  startDate: Date;
  userRole: UserRole | null;
}

// Remove local interface since we're importing it from the service

const WeeklyScheduleView: React.FC<WeeklyScheduleViewProps> = ({ startDate, userRole }) => {
  const [weeklySchedule, setWeeklySchedule] = useState<ScheduleItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  // Calculate the start of the week (Sunday)
  const weekStart = startOfWeek(startDate);
  
  // Generate array of days for the week
  const weekDays = Array.from({ length: 7 }).map((_, index) => addDays(weekStart, index));

  useEffect(() => {
    const fetchWeeklySchedule = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const startDateString = format(weekStart, 'yyyy-MM-dd');
        const schedule = await getWeeklySchedule(startDateString);

        setWeeklySchedule(schedule);
        setIsLoading(false);
      } catch (err) {
        console.error('Error fetching weekly schedule:', err);
        setError('Failed to load weekly schedule data');
        setIsLoading(false);
        toast.error('Failed to load weekly schedule data');
      }
    };

    fetchWeeklySchedule();
  }, [startDate, weekStart]);

  const getScheduleForDay = (dayIndex: number) => {
    const targetDate = format(addDays(weekStart, dayIndex), 'yyyy-MM-dd');
    return weeklySchedule.filter(item => item.date === targetDate);
  };

  if (error) {
    return (
      <div className="text-center p-6 text-destructive">
        <p>{error}</p>
        <Button variant="outline" className="mt-4" onClick={() => window.location.reload()}>
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold">
          Weekly Schedule: {format(weekStart, 'MMM d')} - {format(addDays(weekStart, 6), 'MMM d, yyyy')}
        </h2>
        <Button variant="outline">
          Export
        </Button>
      </div>

      {isLoading ? (
        <div className="space-y-4">
          {Array.from({ length: 7 }).map((_, i) => (
            <Skeleton key={i} className="h-32 w-full" />
          ))}
        </div>
      ) : (
        <div className="space-y-6">
          {weekDays.map((day, index) => (
            <Card key={index} className="overflow-hidden">
              <div className={`h-1 ${index === 0 || index === 6 ? 'bg-muted' : 'bg-primary'}`}></div>
              <CardContent className="p-4">
                <h3 className="font-medium text-lg mb-3">
                  {format(day, 'EEEE, MMMM d')}
                </h3>
                
                {getScheduleForDay(index).length === 0 ? (
                  <div className="text-center p-4 border border-dashed rounded-lg">
                    <p className="text-muted-foreground">No classes scheduled</p>
                  </div>
                ) : (
                  <div className="grid gap-2">
                    {getScheduleForDay(index).map((item, idx) => (
                      <div key={idx} className="p-3 border rounded-md grid grid-cols-12 gap-2">
                        <div className="col-span-2 md:col-span-1 font-medium text-sm text-muted-foreground">
                          {item.timeStart}
                        </div>
                        <div className="col-span-7 md:col-span-8">
                          <div className="font-medium">{item.className}</div>
                          <div className="text-sm text-muted-foreground">
                            {item.teacher} • {item.room}
                          </div>
                        </div>
                        <div className="col-span-3 text-right text-sm text-muted-foreground">
                          {item.timeStart} - {item.timeEnd}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default WeeklyScheduleView;
